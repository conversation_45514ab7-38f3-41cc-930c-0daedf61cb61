# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/actions/setup-node-pnpm-install/action.yml
# Composite actions docs: https://docs.github.com/en/actions/creating-actions/creating-a-composite-action

# ✍️ Description:
# This is a composite action, which means it can be used in other actions.
# It is used in almost all workflows to set up the environment and install dependencies.
# Updating  the package manager or Node version here will be reflected in all workflows.

# 👀 Example usage:
#       - name : 📦 Setup Node + PNPM + install deps
#         uses: ./.github/actions/setup-node-pnpm-install

name: 'Setup  Node + PNPM + Install Dependencies'
description: 'Setup  Node + PNPM + Install Dependencies'
runs:
  using: 'composite'
  steps:
    - uses: pnpm/action-setup@v4
      with:
        run_install: false
    - uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'pnpm'

    - name: 📦 Install Project Dependencies
      run: pnpm install --frozen-lockfile
      shell: bash
