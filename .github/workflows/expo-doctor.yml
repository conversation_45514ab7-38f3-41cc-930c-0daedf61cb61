# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/workflows/expo-doctor.yml

# ✍️ Description:
# This workflow runs the expo doctor command to check if your project dependencies are aligned with the expo sdk version you are using.
# Can be triggered manually from the Actions tab in your project.
# Runs Also on pull requests and pushes to the main/master branch, but only if the `package.json` or `pnpm-lock.yaml` files have been changed.

# 🚨 GITHUB SECRETS REQUIRED: NONE

name: Expo Doctor (expo)

on:
  push:
    branches:
      - main
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
  pull_request:
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'

permissions:
  contents: read
  pull-requests: write
  
jobs:
  doctor:
    name: Expo Doctor (expo)
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
      - name: 📦 Checkout project repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install

      - name: Run prebuild
        run: pnpm run prebuild

      - name: 🚑 Run Doctor Checks
        run: |
          chmod +x .github/scripts/expo-doctor.sh
          rm -rf ios android
          .github/scripts/expo-doctor.sh

      - name: Add doctor report as comment on PR
        if: github.event_name == 'pull_request' && always()
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: expo-doctor
          path: .expo/expo-doctor.md
