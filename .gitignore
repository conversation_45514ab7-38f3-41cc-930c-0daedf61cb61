node_modules/
.expo/
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/
yarn-error.log
/coverage
# macOS
.DS_Store

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

ios/
android/

# ENV
.env.production
.env.staging
.env.development

src/futures/

.maestro
GoogleService-Info.plist
google-services.json

.cursorrules

credentials.json
popla-upload-key.keystore
credentials.txt
credentials-*.json