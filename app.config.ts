import type { ConfigContext, ExpoConfig } from '@expo/config';
import type { AppIconBadgeConfig } from 'app-icon-badge/types';

import { ClientEnv, Env } from './env';
import semanticColors from './src/components/ui/semantic-colors';

const appIconBadgeConfig: AppIconBadgeConfig = {
  enabled: Env.APP_ENV !== 'production',
  badges: [
    {
      text: Env.APP_ENV,
      type: 'banner',
      color: 'white',
    },
    {
      text: Env.VERSION.toString(),
      type: 'ribbon',
      color: 'white',
    },
  ],
};

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: Env.NAME,
  description: `${Env.NAME} Mobile App`,
  owner: Env.EXPO_ACCOUNT_OWNER,
  scheme: Env.SCHEME,
  slug: 'popla',
  version: Env.VERSION.toString(),
  orientation: 'portrait',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  updates: {
    fallbackToCacheTimeout: 0,
    url: 'https://u.expo.dev/52996b2d-9ec4-43f1-85ad-adec93f02a5a',
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: Env.BUNDLE_ID,
    config: {
      usesNonExemptEncryption: false, // Avoid the export compliance warning on the app store
      googleMapsApiKey: Env.GOOGLE_MAPS_IOS_API_KEY,
    },
    googleServicesFile:
      Env.GOOGLE_SERVICES_INFO_PLIST || './GoogleService-Info.plist',
    usesAppleSignIn: true,
    icon: {
      dark: './assets/ios-dark.png',
      light: './assets/ios-light.png',
      tinted: './assets/ios-tinted.png',
    },
    entitlements: {
      'aps-environment':
        Env.APP_ENV === 'production' ? 'production' : 'development',
    },
    infoPlist: {
      UIBackgroundModes: ['remote-notification'],
      NSMicrophoneUsageDescription:
        'This app needs access to your camera to live sessions.',
      NSCameraUsageDescription:
        'This app needs access to your microphone to record song requests.',
      NSPhotoLibraryUsageDescription:
        'This app requires access to the photo library to upload banners for events.',
    },
    associatedDomains: [
      'applinks:getpopla.com',
      'applinks:app.getpopla.com',
      'applinks:dev.app.getpopla.com',
      'applinks:www.getpopla.com',
    ],
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: semanticColors.accent.moderate as unknown as string,
    },
    config: {
      googleMaps: {
        apiKey: Env.GOOGLE_MAPS_ANDROID_API_KEY,
      },
    },
    package: Env.PACKAGE,
    googleServicesFile: Env.GOOGLE_SERVICES_JSON || './google-services.json',
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'https',
            host: 'getpopla.com',
            pathPrefix: '/users',
          },
          {
            scheme: 'https',
            host: 'getpopla.com',
            pathPrefix: '/events',
          },
          {
            scheme: 'https',
            host: 'getpopla.com',
            pathPrefix: '/chats',
          },
          {
            scheme: 'https',
            host: 'getpopla.com',
            pathPrefix: '/session',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'https',
            host:
              Env.APP_ENV !== 'production'
                ? 'dev.app.getpopla.com'
                : 'app.getpopla.com',
            pathPrefix: '/users',
          },
          {
            scheme: 'https',
            host:
              Env.APP_ENV !== 'production'
                ? 'dev.app.getpopla.com'
                : 'app.getpopla.com',
            pathPrefix: '/events',
          },
          {
            scheme: 'https',
            host:
              Env.APP_ENV !== 'production'
                ? 'dev.app.getpopla.com'
                : 'app.getpopla.com',
            pathPrefix: '/chats',
          },
          {
            scheme: 'https',
            host:
              Env.APP_ENV !== 'production'
                ? 'dev.app.getpopla.com'
                : 'app.getpopla.com',
            pathPrefix: '/session',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    [
      'expo-splash-screen',
      {
        backgroundColor: semanticColors.accent.moderate as unknown as string,
        image: './assets/splash-icon.png',
        imageWidth: 180,
      },
    ],
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Aeonik-Air.ttf',
          './assets/fonts/Aeonik-AirItalic.ttf',
          './assets/fonts/Aeonik-Thin.ttf', // 100
          './assets/fonts/Aeonik-ThinItalic.ttf',
          './assets/fonts/Aeonik-Light.ttf', // 200
          './assets/fonts/Aeonik-LightItalic.ttf',
          './assets/fonts/Aeonik-Regular.ttf', // 400
          './assets/fonts/Aeonik-RegularItalic.ttf',
          './assets/fonts/Aeonik-Medium.ttf', // 500
          './assets/fonts/Aeonik-MediumItalic.ttf',
          './assets/fonts/Aeonik-Black.ttf',
          './assets/fonts/Aeonik-BlackItalic.ttf',
        ],
      },
    ],
    'expo-localization',
    'expo-router',
    ['app-icon-badge', appIconBadgeConfig],
    [
      'react-native-edge-to-edge',
      {
        android: {
          enforceNavigationBarContrast: false,
        },
      },
    ],
    ['@react-native-google-signin/google-signin'],
    ['expo-apple-authentication'],
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    '@react-native-firebase/app',
    [
      'expo-build-properties',
      {
        ios: {
          useFrameworks: 'static',
        },
        android: {
          enableProguardInReleaseBuilds: true,
          enableShrinkResourcesInReleaseBuilds: true,
          useLegacyPackaging: true,
          extraProguardRules:
            '-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g -dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args -dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error -dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter -dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider -dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension',
        },
      },
    ],
    [
      'expo-audio',
      {
        microphonePermission:
          'Allow $(PRODUCT_NAME) to access your microphone.',
      },
    ],
    [
      'expo-camera',
      {
        cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera',
      },
    ],
    [
      'expo-calendar',
      {
        calendarPermission:
          'Allow $(PRODUCT_NAME) to access your calendar to add events.',
      },
    ],
    [
      'expo-video',
      {
        supportsBackgroundPlayback: true,
        supportsPictureInPicture: true,
      },
    ],
  ],
  runtimeVersion: {
    policy: 'appVersion',
  },
  extra: {
    ...ClientEnv,
    eas: {
      projectId: Env.EAS_PROJECT_ID,
    },
  },
});
