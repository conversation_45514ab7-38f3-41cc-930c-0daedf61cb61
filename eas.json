{"cli": {"version": ">= 3.8.1", "appVersionSource": "remote"}, "build": {"production": {"channel": "production", "distribution": "store", "pnpm": "9.12.3", "ios": {"image": "latest"}, "android": {"buildType": "app-bundle", "image": "latest"}, "env": {"EXPO_NO_DOTENV": "1", "APP_ENV": "production", "FLIPPER_DISABLE": "1"}}, "staging": {"channel": "staging", "distribution": "internal", "pnpm": "9.12.3", "ios": {"image": "latest"}, "android": {"buildType": "apk", "image": "latest"}, "env": {"APP_ENV": "staging", "EXPO_NO_DOTENV": "1", "FLIPPER_DISABLE": "1"}}, "development": {"developmentClient": true, "distribution": "internal", "pnpm": "9.12.3", "ios": {"image": "latest"}, "android": {"image": "latest"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}}, "simulator": {"pnpm": "9.12.3", "developmentClient": true, "distribution": "internal", "ios": {"simulator": true, "image": "latest"}, "android": {"image": "latest", "buildType": "apk"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}}}, "submit": {"production": {}}}