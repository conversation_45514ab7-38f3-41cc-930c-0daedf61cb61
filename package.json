{"name": "popla-mobile", "version": "2.0.0", "private": true, "main": "expo-router/entry", "scripts": {"start": "cross-env EXPO_NO_DOTENV=1 expo start", "prebuild": "cross-env EXPO_NO_DOTENV=1 pnpm expo prebuild", "android": "cross-env EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_DOTENV=1 expo run:ios", "web": "cross-env EXPO_NO_DOTENV=1 expo start --web", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "preinstall": "npx only-allow pnpm", "start:staging": "cross-env APP_ENV=staging pnpm run start", "prebuild:staging": "cross-env APP_ENV=staging pnpm run prebuild", "prebuild:development": "cross-env APP_ENV=development pnpm run prebuild", "android:staging": "cross-env APP_ENV=staging pnpm run android", "ios:staging": "cross-env APP_ENV=staging pnpm run ios", "start:production": "cross-env APP_ENV=production pnpm run start", "prebuild:production": "cross-env APP_ENV=production pnpm run prebuild", "android:production": "cross-env APP_ENV=production pnpm run android", "ios:production": "cross-env APP_ENV=production pnpm run ios", "build:simulator:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile simulator --platform ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:staging:ios": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --auto-submit --profile staging --platform ios", "build:staging:android": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "prepare": "husky", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "pnpm run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "test": "jest", "check-all": "pnpm run lint && pnpm run type-check && pnpm run lint:translations && pnpm run test", "test:ci": "pnpm run test --coverage", "test:watch": "pnpm run test --watch", "install-maestro": "curl -Ls 'https://get.maestro.mobile.dev' | bash", "e2e-test": "maestro test .maestro/ -e APP_ID=com.get.popla.app.development"}, "dependencies": {"@alexzunik/react-native-money-input": "^0.4.1", "@expo/metro-runtime": "^5.0.4", "@gorhom/bottom-sheet": "^5.0.5", "@hookform/resolvers": "^3.9.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/slider": "4.5.6", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-native-google-signin/google-signin": "^13.2.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/popover": "^1.1.0", "@rn-primitives/portal": "^1.1.0", "@rn-primitives/radio-group": "^1.1.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/switch": "^1.1.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@tanstack/react-query": "^5.52.1", "app-icon-badge": "^0.1.2", "axios": "^1.7.5", "clsx": "^2.1.1", "date-fns": "^4.1.0", "expo": "~53.0.11", "expo-apple-authentication": "~7.2.4", "expo-audio": "~0.4.6", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-calendar": "~14.1.4", "expo-camera": "~16.1.8", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.5", "expo-network": "~7.1.5", "expo-router": "~5.1.0", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-updates": "~0.28.14", "expo-video": "~2.2.1", "i18next": "^23.14.0", "jwt-decode": "^4.0.0", "lodash.memoize": "^4.1.2", "lottie-react-native": "7.2.2", "moment-timezone": "^0.5.48", "moti": "^0.29.0", "nativewind": "^4.1.21", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-native": "0.79.3", "react-native-agora": "^4.5.3", "react-native-edge-to-edge": "^1.6.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-gifted-charts": "^1.4.61", "react-native-gifted-chat": "^2.8.1", "react-native-google-places-autocomplete": "git+https://github.com/kostyngricuk/react-native-google-places-autocomplete#37cfe5ad87d12b701706bc58f76a5d3a0955e60e", "react-native-image-crop-picker": "^0.50.1", "react-native-keyboard-controller": "^1.17.4", "react-native-maps": "1.20.1", "react-native-mmkv": "~3.1.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-pager-view": "6.7.1", "react-native-paystack-webview": "github:a<PERSON><PERSON><PERSON>-yinka/React-Native-Paystack-WebView", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.5", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.4.0", "react-native-svg": "~15.11.2", "react-native-tab-view": "^4.1.2", "react-native-view-shot": "~4.0.3", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-query-kit": "^3.3.0", "socket.io-client": "^4.8.1", "sonner-native": "^0.19.0", "tailwind-variants": "^0.2.1", "use-debounce": "^10.0.5", "zod": "^3.25.61", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@dev-plugins/react-query": "^0.3.1", "@expo/config": "~11.0.10", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react-native": "^12.7.2", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.12", "@types/lodash.memoize": "^4.1.9", "@types/react": "~19.0.14", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-config-expo": "^7.1.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-compiler": "19.0.0-beta-a7bf2bd-20241110", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.15.2", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~53.0.7", "jest-junit": "^16.0.0", "lint-staged": "^15.2.9", "np": "^10.0.7", "prettier": "^3.3.3", "react-query-external-sync": "^2.2.0", "tailwindcss": "3.4.4", "ts-jest": "^29.1.2", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/popla/expo-mobile.git"}, "packageManager": "pnpm@9.12.3", "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-restart", "i18next", "jwt-decode"]}}, "install": {"exclude": ["eslint-config-expo"]}}, "osMetadata": {"initVersion": "7.0.5"}, "pnpm": {"patchedDependencies": {"react-native-image-crop-picker@0.50.1": "patches/<EMAIL>"}}}