diff --git a/android/src/main/AndroidManifest.xml b/android/src/main/AndroidManifest.xml
index a08629b2ce9cd12e60a6d5a27c32b8a96b4547f9..9a075e4cfd798cf4069862698541d33e5b33a1bd 100644
--- a/android/src/main/AndroidManifest.xml
+++ b/android/src/main/AndroidManifest.xml
@@ -24,7 +24,8 @@
 
         <activity
             android:name="com.yalantis.ucrop.UCropActivity"
-            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
+            android:theme="@style/UCropTheme"
+            android:exported="false" />
 
 
         <!-- Prompt Google Play services to install the backported photo picker module -->
diff --git a/android/src/main/res/values/styles.xml b/android/src/main/res/values/styles.xml
new file mode 100644
index 0000000000000000000000000000000000000000..149a710eee87d5524ffcde4e1a36485045e5e79f
--- /dev/null
+++ b/android/src/main/res/values/styles.xml
@@ -0,0 +1,8 @@
+<resources>
+    <style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowMinWidthMajor">100%</item>
+        <item name="android:windowMinWidthMinor">100%</item>
+        <item name="android:windowContentOverlay">@null</item>
+    </style>
+</resources>
