import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractLoggedInUserImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { type HomeDataPayload } from '../user';
import { CURRENT_USER_URL } from './constants';
import { type UserObjectData } from './types';

export const useGetLoggedInUser = createQuery<
  UserObjectData,
  Pick<HomeDataPayload, 'userCurrency'>,
  Error
>({
  queryKey: ['getUser'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<Pick<HomeDataPayload, 'userCurrency'>>(queryObj);
    return HTTPS_BASE({
      url: CURRENT_USER_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as UserObjectData;
        const imageUrls = extractLoggedInUserImageUrls(data);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload logged-in user images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
