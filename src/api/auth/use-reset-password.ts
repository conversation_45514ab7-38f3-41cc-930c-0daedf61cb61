import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { USER_PASSWORD_RESET_URL } from '../user';
import { type OtpPayload } from './types';

export const useResetPassword = createMutation<
  undefined,
  Pick<OtpPayload, 'email'>,
  Error
>({
  mutationFn: async ({ email }) =>
    HTTPS_BASE({
      url: USER_PASSWORD_RESET_URL(email),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
