import { useReactQueryDevTools } from '@dev-plugins/react-query';
import {
  onlineManager,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import Constants from 'expo-constants';
import * as Network from 'expo-network';
import * as React from 'react';
import { Platform } from 'react-native';
import { useSyncQueriesExternal } from 'react-query-external-sync';

import { storage } from '@/lib/storage';
import { showErrorMessage } from '@/components/ui';

export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    // onError: (error) => toast.error(error.message),
  }),
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

export const hostIP =
  Constants.expoGoConfig?.debuggerHost?.split(`:`)[0] ||
  Constants.expoConfig?.hostUri?.split(`:`)[0];

onlineManager.setEventListener((setOnline) => {
  const eventSubscription = Network.addNetworkStateListener((state) => {
    const connected = !!state.isConnected;
    setOnline(connected);

    if (!connected) {
      showErrorMessage('No Internet Connection');
    }
  });
  return eventSubscription.remove;
});

export function APIProvider({ children }: { children: React.ReactNode }) {
  useReactQueryDevTools(queryClient);

  useSyncQueriesExternal({
    queryClient,
    // socketURL: 'http://localhost:42831', // Default port for React Native DevTools - Use for simulators!!!
    socketURL: `http://${hostIP}:42831`,
    deviceName: Platform?.OS || 'web',
    platform: Platform?.OS || 'web',
    deviceId: Platform?.OS || 'web',
    extraDeviceInfo: {
      appVersion: '2.0.0',
    },
    enableLogs: false,
    envVariables: {
      NODE_ENV: process.env.NODE_ENV,
    },
    mmkvStorage: storage,
  });

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
