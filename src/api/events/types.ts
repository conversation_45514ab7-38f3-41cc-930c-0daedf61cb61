import { type SelectedTicket } from '@/lib/hooks/use-purchase-ticket';
import { type LocationType } from '@/types';

import { type LocationObject, type Point, type UserObjectData } from '../auth';

export interface TicketCategories {
  [key: string]: {
    cost: number;
    sold: number;
    quantity: number;
    description?: string;
    selected?: boolean;
    originalCost?: number;
    convertedCost?: number;

    endDateTime: string;
    hasPresale: boolean;
    hasPurchaseLimit: boolean;
    hasTimeline: boolean;
    id: string;
    purchaseLimit: number;
    startDateTime: string;
  };
}

export interface IEventOrganizer {
  id: string;
  fullName: string;
  username: string;
  profileImageUrl: string;
}

export interface IEventLocation {
  city: string;
  state: string;
  street: string;
  address: string;
  country: string;
  landmark?: string;
  coordinates: {
    lat: number;
    lng: number;
    latitude: number;
    longitude: number;
  };
}

interface IBreakdown {
  [key: string]: number;
}

export interface ITicket {
  id: string;
  eventId: string;
  userId: string | null;
  meta: {
    category?: string;
    cost: number;
    user?: Pick<UserObjectData, 'email' | 'fullName'>;
    breakdown?: IBreakdown;
    creditTransactionId: string;
  };
  user?: {
    id: string;
    fullName: string;
    username: string;
    profileImageUrl: string;
  };
  transactionId: string;
  ticketId: string;
  isUsed: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  deletedBy: string | null;
}

export type ITicketExtension = ITicket & {
  title: string;
  bannerUrl: string;
  startTime: string;
  location: IEventLocation;
  organizer: IEventOrganizer;
  eventId: string;
  event: IEvent;
  user?: {
    id: string;
    fullName: string;
    username: string;
    profileImageUrl: string;
  };
};

export type EventStatus = 'DRAFT' | 'APPROVED' | 'SCHEDULED' | 'LIVE' | 'ENDED';
export type EventColor = string;
export type EventStatusAndColor = {
  status: EventStatus;
  color: EventColor;
};

export interface IEvent {
  id: string;
  title: string;
  description: string;
  slug: string;
  startTime: string;
  endTime: string;
  location: IEventLocation;
  collaborators: string[];
  currency: string;
  bannerUrl: string;
  ticketCategories: TicketCategories | null;
  organizerId: string;
  isTicketed?: boolean;
  isFeatured?: boolean;
  status: 'APPROVED' | 'DRAFT';
  media?: IBanner[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  deletedBy: string | null;
  organizer: IEventOrganizer;
  distance: string | null;
  isfavourite: boolean;
  isFavourite: boolean;
  ticketsSold: number;
  questersProfileImages: string[];
  categoryId?: string;
  registrationRequired?: boolean;
  allowedFreeRegistrant?: string;
  currencyConversion?: {
    conversionRate: number;
    convertedAt: string;
    sourceCurrency: string;
    targetCurrency: string;
    ticketCategories: TicketCategories;
  };

  eventFormat: EventFormat;
  eventType: EventType;
  eventViews: number;
  ticketType?: TicketType;

  timezone?: string;
  maxAttendees?: number;
  onlineEventUrl?: string;
  passcode?: string;
  zoomMeetingId?: string;
  goLiveAt?: Date;
  isListedOnSpotify?: boolean;
  isWalletCredited?: boolean;
  lastSpotifySync?: Date | null;

  // to be queried
  presaleConfig?: PresaleConfig[];
  artists?: EventArtist[];
}

export interface ISingleEvent {
  id: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  location: IEventLocation;
  collaborators: any[];
  bannerUrl: string;
  ticketCategories: TicketCategories;
  status: EventStatus;
  organizerId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null | string;
  deletedBy: null | string;
  organizer: IEventOrganizer;
  isFavourite: boolean;
  isFeatured?: boolean;
  hasTicket: boolean;
  registrationRequired?: boolean;
  purchasedTicket?: boolean;
  freeEventRegistration?: boolean;
  slug?: string;
  categoryId?: string;
  isTicketed?: boolean;
  allowedFreeRegistrant?: string;
  registrationCount?: string;
  currencyConversion?: {
    conversionRate: number;
    convertedAt: string;
    sourceCurrency: string;
    targetCurrency: string;
    ticketCategories: TicketCategories;
  };

  eventFormat: EventFormat;
  eventType: EventType;
  eventViews: number;
  ticketType?: TicketType;

  media?: string[];
  timezone?: string;
  currency?: string;
  maxAttendees?: number;
  onlineEventUrl?: string;
  passcode?: string;
  zoomMeetingId?: string;
  goLiveAt?: Date;
  isListedOnSpotify?: boolean;
  isWalletCredited?: boolean;
  lastSpotifySync?: Date | null;

  // to be queried
  presaleConfig?: PresaleConfig[];
  artists?: EventArtist[];
}

export interface EventSearchInterface {
  skip: number;
  take: number;
  organizerId?: string;
  eventDate?: Date;
  state?: string;
  city?: string;
  country?: string;
  title?: string;
  maxDistance?: string;
  coordinates?: Point;
  paginate?: boolean;
  isFeatured?: boolean;
  userId?: string;
}

export interface SearchResultInterface {
  loading?: boolean;
  results: IEvent[];
  total: number;
  take: number;
  skip: number;
}

export interface EventObject {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  state: string;
  city: string;
  address: string;
  lat: string;
  long: string;
  price: string;
  enventImage: string;
  collaborator: string[];
  totalTicket: number;
  avilableTicket: number;
  userId: string;
  createdAt: string;
  updatedAt: string;
  ownerName: string;
}

export interface MyEventsData {
  events: IEvent[];
  total: number;
}

export interface IEventFormData {
  title: string;
  description: string;
  timezone?: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  banner: string | null;
  media?: string[];
  categoryId?: string;
  isTicketed?: boolean;
  registrationRequired?: boolean;
  allowedFreeRegistrant?: string;
  collaborators?: string[];
  location: IEventLocation;
  tickets: {
    name: string;
    cost: string;
    quantity: string;
  }[];
}

export interface IEventFilterConfig {
  skip?: number;
  take?: number;
  organizerId?: string;
  eventDate?: string;
  allEvents: EventObject[];
  search: SearchResultInterface;
}

export interface AllEventsQueryParams {
  skip?: number;
  take?: number;
  organizerId?: string;
  eventDate?: string;
  state?: string;
  city?: string;
  country?: string;
  title?: string;
  maxDistance?: string;
  coordinates?: string; // '{ lng: number; lat: number };'
  userId?: string;
  categoryId?: string;
}

export interface GetTrendingEventsResponse {
  message: string;
  statusCode: number;
  data: { events: IEvent[] };
}

export interface TrendingEventsQueryParams {
  skip?: number;
  take?: number;
  startDate?: string;
  endDate?: string;
  isTicketed?: string;
  categoryId?: string;
  userId?: string;
}

export interface TrendingDJsQueryParams {
  skip?: number;
  take?: number;
}

export interface RandomDJsQueryParams {
  skip?: number;
  take?: number;
}

export interface EventsToFavouriteQueryParams {
  skip?: number;
  take?: number;
}

export interface TicketCategory {
  cost: number;
  quantity: number;
}

export interface EventItem {
  id: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  location: LocationObject;
  collaborators: string[];
  bannerUrl: string;
  ticketCategories: {
    [key: string]: TicketCategory;
  };
  organizer: {
    fullName: string;
    username: string;
  };
  createdAt: string;
  distance: any;
}

export type EventCreationResponse = IEvent;

export type PurchaseEventTicketResponse = ITicket[];

export interface PurchaseEventTicketPayload {
  id: string;
  category: Omit<SelectedTicket, 'cost'>[];
  userCurrency?: string;
  discountCode?: string;
}

export type TicketReservation = {
  id: string;
  userId: string;
  eventId: string;
  category: string;
  quantity: number;
  expiresAt: Date;
  createdAt: Date;
  used: boolean;
};

export type ReserveEventTicketResponse = {
  reservations: TicketReservation[];
  expiresAt: string;
};

export interface ReserveEventTicketPayload {
  id: string;
  reservations: Pick<SelectedTicket, 'category' | 'quantity'>[];
  userCurrency?: string;
}

export interface EventsSearchResultResponse {
  total: number;
  events: IEvent[];
}

export interface Artist {
  id: string;
  name: string;
  images: { url: string; height: number; width: number }[];
  [key: string]: any;
}

export interface ArtistById {
  id: string;
  name: string;
  images: { url: string; height: number; width: number }[];
}

export interface SpotifyArtistsResponse {
  artists: {
    href: string;
    items: Artist[];
    limit: 10;
    next: string;
    offset: 0;
    previous: null;
    total: number;
  };
}

export interface SpotifyArtistsByIdResponse {
  artists: [ArtistById];
}

export interface IEventCategories {
  id: string;
  category: string;
  value?: string;
  logo?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface IBanner {
  uri: string;
  name: string | null;
  type: string | null;
  size: number | null;
}

export type TicketCategoriesType = {
  [k: string]: {
    cost: string | number;
    quantity: string | number;
    description?: string;
    id: string;
    hasPresale: boolean;
    hasTimeline: boolean;
    startDateTime?: Date;
    endDateTime?: Date;
    hasPurchaseLimit: boolean;
    purchaseLimit?: number;
  };
};

export enum EventType {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
}
enum TicketType {
  FREE = 'FREE',
  PAID = 'PAID',
}

export enum EventFormat {
  IN_PERSON = 'IN_PERSON',
  ONLINE = 'ONLINE',
  HYBRID = 'HYBRID',
}

type EventArtist = {
  id: string;
  spotifyArtistId: string;
  eventId: string;
  artistName: string;
  avatar: string;
};

export type PresaleConfig = {
  name: string;
  price: number;
  quantity: number;
  ticketCategoryId: string;
  eventId: string;
  totalTickets: number;
  description?: string;
  purchaseLimit?: number;
  currency?: string;
  startDateTime?: Date | string;
  endDateTime?: Date | string;
};

export interface CreateEventPayload {
  title: string;
  description: string;
  banner: IBanner;
  media?: IBanner[];
  collaborators?: string;
  location?: LocationType;
  startTime: string;
  endTime: string;
  timezone?: string;
  ticketCategories?: TicketCategoriesType;
  currency?: string;
  categoryId: string;
  isTicketed: boolean;
  registrationRequired?: boolean;
  allowedFreeRegistrant?: boolean;
  maxAttendees?: number;
  eventType?: EventType;
  ticketType?: TicketType;
  eventFormat?: EventFormat;
  onlineEventUrl?: string;
  passcode?: string;
  zoomMeetingId?: string;
  goLiveAt?: Date;
  isListedOnSpotify?: boolean;
  presaleConfig?: PresaleConfig[];
  artists?: EventArtist[];
}

export interface VerifyTicketResponse {
  cost: number;
  breakdown: IBreakdown;
  creditTransactionId: string;
  eventTitle: string;
  user?: UserObjectData | Pick<UserObjectData, 'fullName' | 'email'>;
  isUsed: boolean;
}

export interface VerifyTicketPayload {
  eventId: string;
  ticketId: string;
}

export interface ITicketDetails {
  category?: string;
  cost: number;
  breakdown?: IBreakdown;
  creditTransactionId: string;
  id: string;
  userFriendlyTicketId: string;
}

export interface IEventAccessCode {
  id: string;
  code: string;
  eventId: string;
  isUsed: boolean;
  usedBy: string | null;
  usedAt: Date | null;
  deletedAt: Date | null;
  deletedBy: string | null;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date | null;
}

export interface CreateEventAccessCodePayload {
  eventId: string;
  quantity: number;
  expiresAt?: Date;
}

export interface ValidateEventAccessCodePayload {
  eventId: string;
  code: string;
}

export interface DeleteEventAccessCodePayload {
  eventId: string;
  accessCodeId: string;
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum DiscountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface CreateEventDiscount {
  code: string;
  discountType: DiscountType;
  discountValue: number;
  name?: string;
  description?: string;
  maxUsage?: number;
  startDateTime?: string;
  endDateTime?: string;
  isActive?: boolean;
  applicableTicketCategories?: string[];
}

export interface CreateEventDiscountPayload extends CreateEventDiscount {
  eventId: string;
}

export interface ValidateEventDiscountPayload {
  code: string;
  ticketCategory: string[];
  originalPrice: number;
}

export interface ValidateEventDiscountResponse {
  discount: EventDiscount;
  discountAmount: number;
  originalPrice: number;
  finalPrice: number;
  currency: string;
}

export interface EventDiscount extends CreateEventDiscount {
  id: string;
  event: IEvent;
  currentUsage: number;
  status: DiscountStatus;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  deletedBy: string;
  discountAmount: number;
  discountUsages?: any[];
}

export type ExportListType = 'excel' | 'pdf';

export interface GuestListPayload {
  eventId: string;
  fileType: ExportListType;
}

export interface GuestListResponse {
  type: ExportListType;
  link: string;
}
