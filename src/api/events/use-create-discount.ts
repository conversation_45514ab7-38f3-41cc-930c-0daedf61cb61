import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_DISCOUNT } from './constants';
import type { CreateEventDiscountPayload, EventDiscount } from './types';

export const useCreateEventDiscount = createMutation<
  EventDiscount,
  CreateEventDiscountPayload & { eventId: string },
  Error
>({
  mutationFn: async ({ eventId, ...payload }) =>
    HTTPS_BASE({
      url: EVENT_DISCOUNT(eventId),
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
