import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { DELETE_EVENT_ACCESS_CODE } from './constants';
import type { DeleteEventAccessCodePayload } from './types';

export const useDeleteEventAccessCode = createMutation<
  undefined,
  DeleteEventAccessCodePayload,
  Error
>({
  mutationFn: async ({ accessCodeId, eventId }) =>
    HTTPS_BASE({
      url: DELETE_EVENT_ACCESS_CODE(eventId, accessCodeId),
      method: 'DELETE',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
