import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_DISCOUNT } from './constants';
import { type EventDiscount } from './types';

export const useGetEventDiscounts = createQuery<
  EventDiscount[],
  { eventId: string },
  Error
>({
  queryKey: ['getEventDiscounts'],
  fetcher: async ({ eventId }) =>
    HTTPS_BASE({
      url: EVENT_DISCOUNT(eventId),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
