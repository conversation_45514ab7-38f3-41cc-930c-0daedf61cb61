import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractEventCategoryImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_CATEGORIES_URL } from './constants';
import { type IEventCategories } from './types';

export const useGetEventCategories = createQuery<
  IEventCategories[],
  undefined,
  Error
>({
  queryKey: ['getEventCategories'],
  fetcher: async () =>
    HTTPS_BASE({
      url: EVENT_CATEGORIES_URL,
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as IEventCategories[];

        const imageUrls = extractEventCategoryImageUrls(data);

        if (imageUrls.length > 0) {
          try {
            preloadImages(imageUrls);
          } catch (error) {
            console.warn('Failed to preload event category images:', error);
          }
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
