import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractSingleEventImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { SINGLE_EVENT_URL, SINGLE_EVENT_WITH_SLUG_URL } from './constants';
import { type ISingleEvent } from './types';

export const useGetEvent = createQuery<
  ISingleEvent,
  {
    id: string;
    targetCurrency?: string;
  },
  Error
>({
  queryKey: ['getEvent'],
  fetcher: async ({ id, targetCurrency }) => {
    const queryParams = constructQueryStrings<{ targetCurrency?: string }>({
      targetCurrency,
    });
    return HTTPS_BASE({
      url: SINGLE_EVENT_URL(id, queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as ISingleEvent;

        const imageUrls = extractSingleEventImageUrls(data);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload event with slug images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});

export const useGetEventWithSlug = createQuery<
  ISingleEvent,
  {
    slug: string;
    userId?: string;
    deviceId?: string;
    targetCurrency?: string;
  },
  Error
>({
  queryKey: ['getEventWithSlug'],
  fetcher: async ({ slug, targetCurrency, deviceId, userId }) => {
    const queryParams = constructQueryStrings<{
      userId?: string;
      deviceId?: string;
      targetCurrency?: string;
    }>({
      targetCurrency,
      deviceId,
      userId,
    });
    return HTTPS_BASE({
      url: SINGLE_EVENT_WITH_SLUG_URL(slug, queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as ISingleEvent;

        const imageUrls = extractSingleEventImageUrls(data);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload event with slug images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
