import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_GUEST_LIST } from './constants';
import { type GuestListPayload, type GuestListResponse } from './types';

export const getEventGuestList = async ({
  eventId,
  fileType,
}: GuestListPayload) =>
  HTTPS_BASE({
    url: EVENT_GUEST_LIST(eventId, fileType),
    method: 'GET',
  })
    .then((response) => response.data.data)
    .catch((error: AxiosError) => {
      let message = 'Something went wrong';

      if (axios.isAxiosError<ErrorResponse>(error)) {
        message = error.response?.data?.message ?? message;
      } else if (error instanceof Error) {
        message = error.message;
      }

      throw new Error(message);
    });

export const useGetEventGuestList = createQuery<
  GuestListResponse,
  GuestListPayload,
  Error
>({
  queryKey: ['getEventGuestList'],
  fetcher: getEventGuestList,
});
