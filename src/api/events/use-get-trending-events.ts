import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractEventImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { TRENDING_EVENTS_URL } from './constants';
import { type IEvent, type TrendingEventsQueryParams } from './types';

export const useGetTrendingEvents = createQuery<
  IEvent[],
  TrendingEventsQueryParams,
  Error
>({
  queryKey: ['getTrendingEvents'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<TrendingEventsQueryParams>(queryObj);
    return HTTPS_BASE({
      url: TRENDING_EVENTS_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const events = response.data.data.events;
        const imageUrls = extractEventImageUrls(events);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls);
        }

        return events;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
