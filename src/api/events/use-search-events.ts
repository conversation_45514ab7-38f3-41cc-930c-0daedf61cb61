import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractEventImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { SEARCH_EVENTS_URL } from './constants';
import {
  type EventSearchInterface,
  type EventsSearchResultResponse,
} from './types';

export const useSearchEvents = createQuery<
  EventsSearchResultResponse,
  EventSearchInterface,
  Error
>({
  queryKey: ['searchEvents'],
  fetcher: async (queryObj) => {
    const queryParams = constructQueryStrings<EventSearchInterface>(queryObj);
    return HTTPS_BASE({
      url: SEARCH_EVENTS_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as EventsSearchResultResponse;

        const imageUrls = extractEventImageUrls(data.events);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls);
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
