import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import type { ErrorResponse } from '../common/types';
import { SPOTIFY_SEARCH_ARTIST_BY_ID_URL } from './constants';
import { type SpotifyArtistsByIdResponse } from './types';

export const useSearchSpotifyArtistsById = createQuery<
  SpotifyArtistsByIdResponse,
  { ids: string[]; accessToken: string },
  Error
>({
  queryKey: ['spotifySearchArtistById'],
  fetcher: async ({ accessToken, ids }) =>
    axios
      .get<SpotifyArtistsByIdResponse>(
        `${SPOTIFY_SEARCH_ARTIST_BY_ID_URL}?ids=${ids.join(',')}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
          },
        }
      )
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          console.log(
            '🚀 ~ error.response:',
            JSON.stringify(error.request),
            error.response,
            error.response?.data
          );
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
