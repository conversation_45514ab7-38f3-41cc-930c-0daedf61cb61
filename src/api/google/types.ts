import {
  type AddressComponent,
  type PlusCode,
} from 'react-native-google-places-autocomplete';

type PlacesSearchStatus =
  | 'OK'
  | 'ZERO_RESULTS'
  | 'OVER_QUERY_LIMIT'
  | 'REQUEST_DENIED'
  | 'INVALID_REQUEST'
  | 'UNKNOWN_ERROR';

export interface PlaceDetailsResponse {
  html_attributions: string[];
  result: Place;
  status: PlacesSearchStatus;
  info_messages?: string[];
}

export interface PlacesTextSearchResponse {
  html_attributions: string[];
  result: Place[];
  status: PlacesSearchStatus;
  info_messages?: string[];
  error_message?: string;
  next_page_token?: string;
}

export interface PlacesNearbySearchResponse {
  html_attributions: string[]; // Required: attributions that must be displayed to the user
  results: Place[]; // Required: array of Place objects (subset of Place Details fields)
  status: PlacesSearchStatus; // Required: request outcome/status code
  error_message?: string; // Optional: detailed error message if status ≠ OK
  info_messages?: string[]; // Optional: supplemental info about the request, for status OK
  next_page_token?: string; // Optional: token to fetch more results (up to ~60 total)
}

export interface LatLngLiteral {
  lat: number;
  lng: number;
}

export interface Bounds {
  northeast: LatLngLiteral;
  southwest: LatLngLiteral;
}

interface Geometry {
  location: LatLngLiteral;
  viewport: Bounds;
}

interface PlaceOpeningHoursPeriodDetail {
  day: number;
  time: string;
  date?: string;
  truncated?: boolean;
}

interface PlaceOpeningHoursPeriod {
  open: PlaceOpeningHoursPeriodDetail;
  close?: PlaceOpeningHoursPeriodDetail;
}

interface PlaceSpecialDay {
  date?: string;
  exceptional_hours?: boolean;
}

interface PlaceOpeningHours {
  open_now?: boolean;
  periods?: PlaceOpeningHoursPeriod[];
  special_days?: PlaceSpecialDay[];
  type?: string;
  weekday_text?: string[];
}

interface PlaceEditorialSummary {
  language?: string;
  overview?: string;
}

interface PlacePhoto {
  height: number;
  html_attributions: string[];
  photo_reference: string;
  width: number;
}

export interface Place {
  adress_components?: AddressComponent[];
  adr_address?: string;
  business_status?: string;
  curbside_pickup?: boolean;
  current_opening_hours?: PlaceOpeningHours;
  delivery?: boolean;
  dine_in?: boolean;
  editorial_summary?: PlaceEditorialSummary;
  formatted_address?: string;
  formatted_phone_number?: string;
  geometry?: Geometry;
  icon?: string;
  icon_background_color?: string;
  icon_mask_base_uri?: string;
  international_phone_number?: string;
  name?: string;
  opening_hours?: PlaceOpeningHours;
  photos?: PlacePhoto[];
  place_id?: string;
  plus_code?: PlusCode;
  price_level?: number;
  rating?: number;
  reservable?: boolean;
  reviews?: PlaceReview[];
  secondary_opening_hours?: PlaceOpeningHours;
  serves_beer?: boolean;
  serves_breakfast?: boolean;
  serves_brunch?: boolean;
  serves_dinner?: boolean;
  serves_lunch?: boolean;
  serves_vegetarian_food?: boolean;
  serves_wine?: boolean;
  takeout?: boolean;
  url?: string;
  user_ratings_total?: number;
  utc_offset?: number;
  vicinity?: string;
  website?: string;
  wheelchair_accessible_entrance?: boolean;
}

/** A review of the place submitted by a user. */
export interface PlaceReview {
  /** The name of the user who submitted the review. Anonymous reviews are attributed to "A Google user". */
  author_name: string;
  /** The user's overall rating for this place. Whole number from 1 to 5. */
  rating: number;
  /** The time that the review was submitted, in text form relative to current time (e.g., "a month ago"). */
  relative_time_description: string;
  /** The timestamp when the review was submitted, in seconds since midnight, January 1, 1970 UTC. */
  time: number;

  /** URL to the user's Google Maps Local Guides profile, if available. */
  author_url?: string;
  /** IETF language code of the returned review (e.g. "en"). Empty if there’s only a rating. */
  language?: string;
  /** IETF language code of the original review if translated (different from `language`). */
  original_language?: string;
  /** URL to the user's profile photo, if available. */
  profile_photo_url?: string;
  /** The text content of the review. May include HTML markup (e.g. "&amp;"). Optional. */
  text?: string;
  /** `true` if the review was translated from another language. */
  translated?: boolean;
}
