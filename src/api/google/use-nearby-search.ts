import axios, { type AxiosError } from 'axios';
import { type SearchType } from 'react-native-google-places-autocomplete';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import { type ErrorResponse } from '../common/types';
import { NEARBY_SEARCH_API } from './constants';
import { type PlacesNearbySearchResponse } from './types';

type Variables = {
  /** The point around which to retrieve place information. This must be specified as latitude,longitude */
  location: string;
  /** Defines the distance (in meters) within which to return place results */
  radius: string;

  /** Note: Adding both `keyword` and `type` with the same value (`keyword=cafe&type=cafe` or `keyword=parking&type=parking`) can yield `ZERO_RESULTS`. */
  keyword?: string;

  /** @see https://developers.google.com/places/web-service/supported_types#table1 */
  type?: SearchType;
};

export const useNearbySearch = createQuery<
  PlacesNearbySearchResponse,
  Variables,
  Error
>({
  queryKey: ['nearbySearch'],
  fetcher: async (payload) => {
    const queryParams = constructQueryStrings<Variables>(payload);
    return HTTPS_BASE({
      url: NEARBY_SEARCH_API(queryParams),
      method: 'GET',
    })
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
