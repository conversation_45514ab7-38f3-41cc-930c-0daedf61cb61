import axios, { type AxiosError } from 'axios';
import { type SearchType } from 'react-native-google-places-autocomplete';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import { type ErrorResponse } from '../common/types';
import { TEXT_SEARCH_API } from './constants';
import { type PlacesNearbySearchResponse } from './types';

type Variables = {
  /** The text string on which to search, for example: "restaurant" or "123 Main Street". This must a place name, address, or category of establishments. */
  query: string;

  /** Defines the distance (in meters) within which to return place results */
  radius: string;

  /** The point around which to retrieve place information. This must be specified as latitude,longitude */
  location?: string;

  /** Note: Adding both `keyword` and `type` with the same value (`keyword=cafe&type=cafe` or `keyword=parking&type=parking`) can yield `ZERO_RESULTS`. */
  keyword?: string;

  /** @see https://developers.google.com/places/web-service/supported_types#table1 */
  type?: SearchType;
};

export const useTextSearch = createQuery<
  PlacesNearbySearchResponse,
  Variables,
  Error
>({
  queryKey: ['textSearch'],
  fetcher: async (payload) => {
    const queryParams = constructQueryStrings<Variables>(payload);
    return HTTPS_BASE({
      url: TEXT_SEARCH_API(queryParams),
      method: 'GET',
    })
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
