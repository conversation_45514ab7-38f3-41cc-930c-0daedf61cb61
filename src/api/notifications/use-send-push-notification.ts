import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { PUSH_NOTIFICATION_URL } from './constants';
import type {
  PushNotificationPayload,
  PushNotificationResponse,
} from './types';

export const useSendPushNotification = createMutation<
  PushNotificationResponse,
  PushNotificationPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: PUSH_NOTIFICATION_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
