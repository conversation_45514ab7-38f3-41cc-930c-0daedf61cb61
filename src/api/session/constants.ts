// DJ-SESSION
export const BASE_DJ_SESSION_URL = '/dj-sessions';

export const GET_SESSIONS_URL = (queryString: string) =>
  `${BASE_DJ_SESSION_URL}${queryString ? `?${queryString}` : ''}`;

export const JOIN_SESSION_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/join`;

export const GET_SESSION_URL = (sessionId: string, userCurrency: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}${
    userCurrency ? `?userCurrency=${userCurrency}` : ''
  }`;

export const GET_SESSION_QUEUES_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/queue`;

export const UPDATE_SONG_REQUEST_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/song/update`;

export const SEND_SONG_RECORDING_URL = (sessionId: string, songId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/song/${songId}/recording`;

export const DELETE_SONG_REQUEST_URL = (sessionId: string, songId: string) => {
  return `${BASE_DJ_SESSION_URL}/${sessionId}/song/${songId}`;
};
export const END_SESSION_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/end`;

export const LEAVE_SESSION_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/leave`;

export const SESSION_VIEWERS_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/viewers`;

export const BOOST_REQUEST_URL = (
  sessionId: string,
  songId: string,
  queryString: string
) => `${BASE_DJ_SESSION_URL}/${sessionId}/boost/${songId}${queryString}`;

export const SEARCH_LIVE_SESSION_URL = (queryString: URLSearchParams) =>
  `${BASE_DJ_SESSION_URL}?${queryString}`;

export const GET_RECENT_LIVE_SESSION_URL = (queryString: URLSearchParams) =>
  `${BASE_DJ_SESSION_URL}/recent?${queryString}`;

export const REQUEST_SONG_URL = (sessionId: string) =>
  `${BASE_DJ_SESSION_URL}/${sessionId}/song/request`;

export const GET_REQUEST_HISTORY_URL = (queryParams: string) =>
  `${BASE_DJ_SESSION_URL}/request-history?${queryParams}`;

export const SESSION_CONNECTION_STATUS_URL = `${BASE_DJ_SESSION_URL}/status`;

// SPOTIFY
export const SPOTIFY_TOKEN_URL = 'https://accounts.spotify.com/api/token';

export const SPOTIFY_SEARCH_MUSIC_URL = 'https://api.spotify.com/v1/search?q=';

// AGORA
export const AGORA_TOKEN_URL = (
  channelName: string,
  uid: number,
  role: number
) =>
  `/dj-sessions/agora/token?channelName=${channelName}&uid=${uid}&role=${role}`;
export const AGORA_RTM_TOKEN_URL = (uid: string) =>
  `/dj-sessions/agora/rtm-token?uid=${uid}`;
