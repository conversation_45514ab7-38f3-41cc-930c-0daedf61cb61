import type { AxiosError } from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractSessionImageUrls,
  HTTPS_BASE,
} from '../common';
import { GET_SESSIONS_URL } from './constants';
import { type LiveSessionResponse } from './types';
import { type SessionQuery } from './types';

type Response = { djSessions: LiveSessionResponse[]; total: number };
type Variables = Partial<SessionQuery>;

export const useGetNearbySessions = createQuery<
  Response,
  Variables,
  AxiosError
>({
  queryKey: ['getSessions'],
  fetcher: async (queryObj) => {
    const queryParams = constructQueryStrings<Variables>(queryObj);
    return HTTPS_BASE({
      url: GET_SESSIONS_URL(queryParams),
      method: 'GET',
    }).then((response) => {
      const data = response.data.data as Response;

      const imageUrls = extractSessionImageUrls(data.djSessions);

      if (imageUrls.length > 0) {
        preloadImages(imageUrls).catch((error) => {
          console.warn('Failed to preload session images:', error);
        });
      }

      return data;
    });
  },
});
