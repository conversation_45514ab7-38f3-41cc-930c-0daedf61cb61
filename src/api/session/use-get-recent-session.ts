import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractSessionImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_RECENT_LIVE_SESSION_URL } from './constants';
import { type LiveSessionResponse, type SessionQuery } from './types';

export const useGetRecentLiveSessions = createQuery<
  LiveSessionResponse[],
  Partial<SessionQuery>,
  Error
>({
  queryKey: ['getRecentLiveSessions'],
  fetcher: async (queryObj) => {
    const searchParams = new URLSearchParams(queryObj ?? {});
    return HTTPS_BASE({
      url: GET_RECENT_LIVE_SESSION_URL(searchParams),
      method: 'GET',
    })
      .then((response) => {
        const djSessions = response.data.data as LiveSessionResponse[];

        const imageUrls = extractSessionImageUrls(djSessions);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload session images:', error);
          });
        }

        return djSessions;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
