import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { extractLiveSessionQueueImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_SESSION_QUEUES_URL } from './constants';
import { type DJLiveSession, type UserLiveSession } from './types';
import { preloadImages } from '@/components/ui';

export const useGetLiveSessionQueues = createQuery<
  DJLiveSession & UserLiveSession,
  { sessionId: string },
  Error
>({
  queryKey: ['getLiveSessionQueues'],
  fetcher: async ({ sessionId }) =>
    HTTPS_BASE({
      url: GET_SESSION_QUEUES_URL(sessionId),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as DJLiveSession & UserLiveSession;

        const imageUrls = extractLiveSessionQueueImageUrls(data);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload live session images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
