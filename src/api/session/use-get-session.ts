import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractLiveSessionImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_SESSION_URL } from './constants';
import { type LiveSessionInfoResponse } from './types';

export const useGetLiveSession = createQuery<
  LiveSessionInfoResponse,
  { sessionId: string; userCurrency: string },
  Error
>({
  queryKey: ['getLiveSession'],
  fetcher: async ({ sessionId, userCurrency }) =>
    HTTPS_BASE({
      url: GET_SESSION_URL(sessionId, userCurrency),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as LiveSessionInfoResponse;

        const imageUrls = extractLiveSessionImageUrls(data);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload live session images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
