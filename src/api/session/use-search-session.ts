import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractSessionImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { SEARCH_LIVE_SESSION_URL } from './constants';
import { type SearchLiveSessionResponse, type SessionQuery } from './types';

export const useSearchLiveSessions = createQuery<
  SearchLiveSessionResponse,
  Partial<SessionQuery>,
  Error
>({
  queryKey: ['searchLiveSessions'],
  fetcher: async (queryObj) => {
    const searchParams = new URLSearchParams(queryObj ?? {});
    return HTTPS_BASE({
      url: SEARCH_LIVE_SESSION_URL(searchParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as SearchLiveSessionResponse;

        const imageUrls = extractSessionImageUrls(data.djSessions);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload live session images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
