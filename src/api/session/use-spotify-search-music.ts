import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import type { ErrorResponse } from '../common/types';
import { SPOTIFY_SEARCH_MUSIC_URL } from './constants';
import { type SpotifyTracksResponse } from './types';
import { extractSpotifyTrackImageUrls } from '..';
import { preloadImages } from '@/components/ui';

export const useSpotifySearchMusic = createQuery<
  SpotifyTracksResponse,
  { query: string; accessToken: string },
  Error
>({
  queryKey: ['spotifySearchMusic'],
  fetcher: async ({ accessToken, query }) =>
    axios
      .get<SpotifyTracksResponse>(
        `${SPOTIFY_SEARCH_MUSIC_URL}${query}&type=track`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
          },
        }
      )
      .then((response) => {
        const data = response.data;

        const imageUrls = extractSpotifyTrackImageUrls(data);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload Spotify track images:', error);
          });
        }
        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          console.log(
            '🚀 ~ error.response:',
            JSON.stringify(error.request),
            error.response,
            error.response?.data
          );
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
