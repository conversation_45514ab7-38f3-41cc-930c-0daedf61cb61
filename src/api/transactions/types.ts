import { type SelectedTicket } from '@/lib/hooks/use-purchase-ticket';
import {
  type TransactionCategory,
  type TransactionStatus,
  type TransactionType,
} from '@/types';

import { type Song } from '../session';
import { type ITransaction } from '../user';

export interface InitializeTransactionPayload {
  amount: number;
  country: string;
}

export interface InitializeTicketTransactionPayload {
  eventId: number;
  tickets: Omit<SelectedTicket, 'cost'>[];
  guestEmail?: string;
  guestName?: string;
}

export interface InitializeTransactionResponse {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionResponse extends ITransaction {
  djSession?: {
    title: string;
    cost: number;
    shoutoutCost: number | null;
    playedSongRequestsWithShoutout: number;
    playedSongRequestsWithoutShoutout: number;
    boostedSongRequests: number;
    shoutOutRequests: number;
  };

  songRequest?: {
    status: SongRequestStatus;
    shoutOutTo: string | null;
    isBoosted: boolean;
    song: Song;
    djSession: {
      bannerUrl: string;
      title: string;
      cost: number;
      shoutoutCost: number | null;
      dj: {
        id: string;
        username: string;
        profileImageUrl: string | null;
      };
    };
  };

  ticket?: {
    event: {
      bannerUrl: string;
      title: string;
      startTime: Date;
      organizer: {
        id: string;
        username: string;
        profileImageUrl: string | null;
      };
    };
    meta: {
      user?: {
        email: string;
        fullName: string;
      };
      currency?: string;
      breakdown?: {
        cost: number;
        type: string;
        quantity: number;
      }[];
    };
    serviceFee: number;
    discount?: {
      id: string;
      code: string;
      amount: number;
    };
  };
}

export interface SongRequestStatus {
  PLAYED: 'PLAYED';
  PENDING: 'PENDING';
  ACCEPTED: 'ACCEPTED';
  REJECTED: 'REJECTED';
  CANCELLED: 'CANCELLED';
}

export interface VerifyPaystackTransactionPayload {
  transactionRef: string;
}

export interface VerifyPaystackTransactionResponse {
  amount: number;
  status: 'success' | 'failed';
}

export interface PayoutInformationPayload {
  accountName: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
}

export interface SaveAccountDetailsResponse {
  active: boolean;
  createdAt: Date;
  currency: string;
  description: any;
  details: {
    account_name: string;
    account_number: string;
    authorization_code: any;
    bank_code: string;
    bank_name: string;
  };
  domain: string;
  email: any;
  id: number;
  integration: number;
  isDeleted: boolean;
  is_deleted: boolean;
  metadata: any;
  name: string;
  recipient_code: string;
  type: string;
  updatedAt: Date;
}

export interface MakeWithdrawalRequestResponse {
  amount: number;
  createdAt: Date;
  id: string;
  meta: any;
  payoutDate: Date;
  status: string;
  transactionId: string;
  updatedAt: Date;
  userId: string;
}
