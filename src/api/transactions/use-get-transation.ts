import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_TRANSACTION_URL } from './constants';
import type { TransactionResponse } from './types';

export const useGetTransaction = createQuery<
  TransactionResponse,
  { id: string },
  AxiosError
>({
  queryKey: ['getTransaction'],
  fetcher: async ({ id }) =>
    HTTPS_BASE({
      url: GET_TRANSACTION_URL(id),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
