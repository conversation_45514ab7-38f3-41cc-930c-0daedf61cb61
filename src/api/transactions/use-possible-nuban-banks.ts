import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GUESS_NUBAN_URL } from './constants';

interface AccountGuess {
  name: string;
  bank_code: string;
  destbankcode: string;
  recipientaccount: string;
  id: string;
  title: string;
}

export const useGuessNuban = createQuery<
  AccountGuess[],
  { accountNumber: string },
  Error
>({
  queryKey: ['guessNuban'],
  fetcher: async ({ accountNumber }) =>
    HTTPS_BASE({
      url: GUESS_NUBAN_URL(accountNumber),
      method: 'GET',
    })
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
