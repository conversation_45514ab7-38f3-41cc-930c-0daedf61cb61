import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { VERIFY_NUBAN_URL } from './constants';

interface NubanResponse {
  bank_name: string;
  account_name: string;
  account_number: string;
  bank_code: string;
  requests: string;
  execution_time: string;
}

export const useVerifyNuban = createQuery<
  NubanResponse[],
  { bankCode: string; accountNumber: string },
  Error
>({
  queryKey: ['verifyNuban'],
  fetcher: async ({ bankCode, accountNumber }) =>
    HTTPS_BASE({
      url: VERIFY_NUBAN_URL(bankCode, accountNumber),
      method: 'GET',
    })
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
