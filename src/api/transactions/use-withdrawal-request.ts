import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { REQUEST_PAYOUT_URL } from './constants';
import { type MakeWithdrawalRequestResponse } from './types';

export const useWithdrawalRequest = createMutation<
  MakeWithdrawalRequestResponse,
  { amount: number },
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: REQUEST_PAYOUT_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
