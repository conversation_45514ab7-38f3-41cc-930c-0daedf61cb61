// USER
export const BASE_USER_URL = '/users';
export const USER_PASSWORD_RESET_URL = (email: string) =>
  `${BASE_USER_URL}/password/reset?email=${email}`;
export const VERIFY_USERNAME = `${BASE_USER_URL}/verify-username`;
export const SEARCH_USERS_URL = (queryString: string) =>
  `${BASE_USER_URL}?${queryString}`;
export const USER_HOME_URL = (queryString: string) =>
  `${BASE_USER_URL}/home${queryString}`;
export const PROFILE_UPDATE_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/profile`;
export const FCMTOKEN_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/fcmToken`;
export const GET_USER_TRANSACTION_HISTORY_URL = (
  userId: string,
  queryParams: string
) =>
  `${BASE_USER_URL}/${userId}/transactions${queryParams ? `?${queryParams}` : ''}`;
export const SEND_FEEDBACK_URL = '/contact-us';
export const USER_TICKETS_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/tickets`;

export const REPORT_PROBLEM_URL = '/reports';

export const FAVORITE_URL = '/users/favorites';

export const TRENDING_DJS_URL = `${BASE_USER_URL}/dj/trending`;

export const PROFILE_IMAGE_UPLOAD_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/media`;

export const CREATE_PIN_URL = `${BASE_USER_URL}/wallet/pin`;
export const VALIDATE_PIN_URL = `${BASE_USER_URL}/wallet/validate-pin`;

export const NEARBY_USERS_URL = (distance: string) =>
  `${BASE_USER_URL}/nearby${distance ? `?distance=${distance}` : ''}`;

export const FARAWAY_USERS_URL = (queryParams: string) =>
  `${BASE_USER_URL}/faraway${queryParams ? `?${queryParams}` : ''}`;
