import { type ReportEnum } from '@/app/profile/report-problem';
import {
  type TransactionCategory,
  type TransactionStatus,
  type TransactionType,
} from '@/types';

import type {
  LocationObject,
  Point,
  SocialsTypes,
  USER_ROLE,
  UserObjectData,
} from '../auth';
import { type IEvent, type ITicket } from '../events';
import { type LiveSessionResponse, type TrendingDjResponse } from '../session';

export interface UserSearchInterface {
  skip: number;
  take: number;
  roles?: USER_ROLE[];
  userIds?: string[];
  usernames?: string[];
  query?: string;
  paginate?: boolean;
}

export interface SearchResultResponse {
  total: number;
  users: UserObjectData[];
}

export interface EventSearchInterface {
  skip: number;
  take: number;
  organizerId?: string;
  eventDate?: Date;
  state?: string;
  city?: string;
  country?: string;
  title?: string;
  maxDistance?: string;
  coordinates?: Point;
  paginate?: boolean;
  isFeatured?: boolean;
}

export type HomeDataPayload = {
  lat: number;
  lng: number;
  userCurrency: string;
};

export type HomeDataResponse = {
  user: UserObjectData;
  trendingDJs: TrendingDjResponse[];
  djSessions: LiveSessionResponse[];
};

export interface UpdateProfileInterface {
  bio?: string;
  socials?: SocialsTypes;
  location?: LocationObject;
  fullName?: string;
  phoneNumber?: string;
  role?: string;
  category?: string;
  genres?: string[];
  gender?: string;
  profileImageUrl?: object;
  emailNotification?: boolean;
  pushNotification?: boolean;
  allowNearbyDiscovery?: boolean;
}

export interface PatchFCMTokenInterface {
  userId: string;
  fcmToken: string;
}

export interface SendFeebackInterface {
  email: string;
  message: string;
  fullName: string;
}

export interface ReportProblemInterface {
  title: string;
  type: NonNullable<ReportEnum | undefined>;
  description: string;
  songRequestId?: string;
}

export interface ITransactionFilterConfig {
  id: string;
  skip?: number;
  take?: number;
  status?: string;
  category?: string;
  type?: string;
  currency?: string;
  startDate?: number;
  endDate?: number;
}

export interface ITransaction {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  currency?: string;
}

export interface GetTransactionHistoryResponse {
  transactions: ITransaction[];
  total: number;
}

export type IEventsWithTicket = IEvent & { tickets: ITicket[] };

export interface UserTicketsResponse {
  eventsWithTicket: IEventsWithTicket[];
  total: number;
}

export type AddFavouritePayload = {
  type: 'EVENT' | 'ACCOUNT';
  accountId: string | null;
  eventId: string | null;
};

export type RemoveFavouritePayload = {} & AddFavouritePayload;

export type PinPayload = {
  pin: string;
};
