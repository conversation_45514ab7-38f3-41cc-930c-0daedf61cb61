import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { CREATE_PIN_URL } from './constants';
import type { PinPayload } from './types';

export const useCreatePin = createMutation<undefined, PinPayload, Error>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: CREATE_PIN_URL,
      method: 'PUT',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
