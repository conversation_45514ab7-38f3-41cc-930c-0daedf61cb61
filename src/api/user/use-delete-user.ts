import axios, { type AxiosError } from 'axios';
import { createMutation } from 'react-query-kit';

import type { ErrorResponse } from '../common/types';
import { HTTPS_BASE } from '../common';

type Variables = { id: string };
type Response = { message: string };

export const useDeleteUser = createMutation<Response, Variables, AxiosError>({
  mutationFn: async ({ id }) => {
    try {
      const response = await HTTPS_BASE.delete(`users/${id}`);
      return response.data;
    } catch (error) {
      let message = 'Something went wrong';

      if (axios.isAxiosError<ErrorResponse>(error)) {
        message = error.response?.data?.message ?? message;
      } else if (error instanceof Error) {
        message = error.message;
      }

      throw new Error(message);
    }
  },
});
