import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import type { UserObjectData } from '../auth/types';
import {
  constructQueryStrings,
  extractFarawayUsersImageUrls,
  HTTPS_BASE,
} from '../common';
import { type ErrorResponse } from '../common/types';
import { FARAWAY_USERS_URL } from './constants';

type Variables = { minDistance: string; maxDistance?: string };
export type Response = {
  users: {
    distance: number;
    user: UserObjectData;
  }[];
  maxDistance: number;
};

export const useGetFarawayUsers = createQuery<Response, Variables, AxiosError>({
  queryKey: ['faraway-users'],
  fetcher: async (query) => {
    const queryParams = constructQueryStrings<Variables>(query);
    return HTTPS_BASE({
      url: FARAWAY_USERS_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as Response;

        const imageUrls = extractFarawayUsersImageUrls(data);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload faraway user images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
