import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { type UserObjectData } from '../auth';
import { extractFavoritesImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { type IEvent } from '../events';
import { FAVORITE_URL } from './constants';

export type UserFavouriteType = 'ACCOUNT' | 'EVENT';
export const useGetUserFavourites = createQuery<
  IEvent[] | UserObjectData[],
  { type: UserFavouriteType },
  Error
>({
  queryKey: ['getUserFavourites'],
  fetcher: async ({ type }) =>
    HTTPS_BASE({
      url: `${FAVORITE_URL}?type=${type}`,
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data;

        const imageUrls = extractFavoritesImageUrls(data, type);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload favorite images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
