import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractTrendingCreatorImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import type { TrendingDjResponse } from '../session/types';
import { TRENDING_DJS_URL } from './constants';

export const useGetTrendingCreators = createQuery<
  TrendingDjResponse[],
  void,
  Error
>({
  queryKey: ['getTrendingCreators'],
  fetcher: async () =>
    HTTPS_BASE({
      url: TRENDING_DJS_URL,
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as TrendingDjResponse[];

        const imageUrls = extractTrendingCreatorImageUrls(data);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload trending creator images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }
        throw new Error(message);
      }),
});
