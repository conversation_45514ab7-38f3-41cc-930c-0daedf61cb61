import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractUserTicketsImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { USER_TICKETS_URL } from './constants';
import { type UserTicketsResponse } from './types';

export const useGetUserTickets = createQuery<
  UserTicketsResponse,
  { id: string },
  Error
>({
  queryKey: ['getUserTickets'],
  fetcher: async ({ id }) =>
    HTTPS_BASE({
      url: USER_TICKETS_URL(id),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as UserTicketsResponse;

        const imageUrls = extractUserTicketsImageUrls(data);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload user ticket images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
