import axios, { type AxiosError } from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import type { UserObjectData } from '../auth/types';
import { extractLoggedInUserImageUrls, HTTPS_BASE } from '../common';
import { type ErrorResponse } from '../common/types';

type Variables = { id: string };
type Response = UserObjectData;

export const useGetUser = createQuery<Response, Variables, AxiosError>({
  queryKey: ['users'],
  fetcher: async ({ id }) =>
    HTTPS_BASE.get(`users/${id}`)
      .then((response) => {
        const data = response.data.data as UserObjectData;
        const imageUrls = extractLoggedInUserImageUrls(data);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload logged-in user images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
