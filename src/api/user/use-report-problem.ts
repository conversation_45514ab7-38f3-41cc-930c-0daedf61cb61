import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { REPORT_PROBLEM_URL } from './constants';
import { type ReportProblemInterface } from './types';

export const useReportProblem = createMutation<
  undefined,
  ReportProblemInterface,
  Error
>({
  mutationFn: async (payload) =>
    HTTPS_BASE({
      url: REPORT_PROBLEM_URL,
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
