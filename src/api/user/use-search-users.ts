import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractNearbyUserImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { SEARCH_USERS_URL } from './constants';
import type {
  EventSearchInterface,
  SearchResultResponse,
  UserSearchInterface,
} from './types';

export type SearchUsersPayload = { queryObj: Partial<UserSearchInterface> };

export const useApiSearchUsers = createQuery<
  SearchResultResponse,
  SearchUsersPayload,
  Error
>({
  queryKey: ['searchUsers'],
  fetcher: async ({ queryObj }) => {
    if (queryObj.skip === 0) {
      delete queryObj.skip;
    }
    const queryString =
      constructQueryStrings<
        Partial<UserSearchInterface & EventSearchInterface>
      >(queryObj);
    return HTTPS_BASE({
      url: SEARCH_USERS_URL(queryString),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as SearchResultResponse;
        const imageUrls = extractNearbyUserImageUrls(data.users);
        if (imageUrls.length > 0) {
          preloadImages(imageUrls).catch((error) => {
            console.warn('Failed to preload live session images:', error);
          });
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
