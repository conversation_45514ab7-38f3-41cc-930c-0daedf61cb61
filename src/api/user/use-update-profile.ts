import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { type UserObjectData } from '../auth';
import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { PROFILE_UPDATE_URL } from './constants';
import { type UpdateProfileInterface } from './types';

export const useUpdateProfile = createMutation<
  UserObjectData,
  {
    userId: string;
    payload: UpdateProfileInterface;
  },
  Error
>({
  mutationFn: async ({ userId, payload }) =>
    HTTPS_BASE({
      url: PROFILE_UPDATE_URL(userId),
      method: 'PATCH',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
