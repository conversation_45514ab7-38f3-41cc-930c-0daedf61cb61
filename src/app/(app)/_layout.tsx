import {
  collection,
  doc,
  getFirestore,
  onSnapshot,
  query,
  where,
} from '@react-native-firebase/firestore';
import { useQueryClient } from '@tanstack/react-query';
import { BlurView } from 'expo-blur';
import { Redirect, SplashScreen, Tabs } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  CalendarFilledIcon,
  CalendarIcon,
  ChatFilledIcon,
  ChatIcon,
  colors,
  HeartFilledIcon,
  HeartIcon,
  HomeFilledIcon,
  HomeIcon,
  SearchFilledIcon,
  SearchIcon,
  semanticColors,
  View,
} from '@/components/ui';
import { useAuth, useIsFirstTime } from '@/lib';

export const TAB_HEIGHT = 65;

export default function TabLayout() {
  const insets = useSafeAreaInsets();
  const queryClient = useQueryClient();
  const status = useAuth.use.status();
  const user = useAuth.use.user();
  const [isFirstTime] = useIsFirstTime();
  const { colorScheme } = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  const [hasUnreadMessages, setHasUnreadMessages] = React.useState(false);

  const hideSplash = React.useCallback(async () => {
    await SplashScreen.hideAsync();
  }, []);

  React.useEffect(() => {
    if (status !== 'idle') {
      setTimeout(() => {
        hideSplash();
      }, 1000);
    }
  }, [hideSplash, status]);

  React.useEffect(() => {
    if (!user?.id) return;

    const db = getFirestore();

    const userChatsRef = collection(
      doc(collection(db, 'UserChats'), user.id),
      'Chats'
    );
    const chatRequestsRef = query(
      collection(db, 'ChatRequests'),
      where('receiverId', '==', user.id),
      where('status', '==', 'pending')
    );

    const unsubscribers = [
      onSnapshot(userChatsRef, (chatSnapshot) => {
        const hasUnreadChats = chatSnapshot.docs.some(
          (doc) => doc.data().unreadCount > 0
        );
        setHasUnreadMessages(hasUnreadChats);
      }),
      onSnapshot(chatRequestsRef, (requestSnapshot) => {
        setHasUnreadMessages((prev) => prev || !requestSnapshot.empty);
      }),
    ];

    return () => unsubscribers.forEach((unsub) => unsub());
  }, [user?.id]);

  if (isFirstTime) {
    return <Redirect href="/onboarding" />;
  }
  if (status === 'signedOut') {
    queryClient.clear();
    return <Redirect href="/onboarding" />;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        animation: 'fade',
        transitionSpec: {
          animation: 'timing',
          config: {
            duration: 250,
          },
        },
        tabBarStyle: {
          position: 'absolute',
          height: TAB_HEIGHT + insets.bottom,
          borderTopWidth: 1,
          borderTopColor: isDarkMode
            ? semanticColors.border.subtle.dark
            : semanticColors.border.subtle.light,
          backgroundColor: 'transparent',
          elevation: 0,
          paddingTop: 8,
        },
        tabBarBackground: () => (
          <BlurView
            tint={isDarkMode ? 'dark' : 'extraLight'}
            intensity={isDarkMode ? 70 : 65}
            style={{
              ...StyleSheet.absoluteFillObject,
              overflow: 'hidden',
              backgroundColor: 'transparent',
            }}
          />
        ),
        tabBarActiveTintColor: colors.brand[60], // Purple color for active tab
        tabBarInactiveTintColor: isDarkMode ? colors.grey[60] : colors.grey[50], // Gray color for inactive tabs
        tabBarLabelStyle: {
          fontFamily: 'Aeonik-Regular',
          fontSize: 14,
          marginBottom: insets.bottom > 0 ? 0 : 10,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) =>
            focused ? (
              <HomeFilledIcon color={color} />
            ) : (
              <HomeIcon color={color} />
            ),
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color, focused }) =>
            focused ? (
              <SearchFilledIcon color={color} />
            ) : (
              <SearchIcon color={color} />
            ),
        }}
      />
      <Tabs.Screen
        name="event"
        options={{
          title: 'Event',
          tabBarIcon: ({ color, focused }) =>
            focused ? (
              <CalendarFilledIcon color={color} />
            ) : (
              <CalendarIcon color={color} />
            ),
        }}
      />

      <Tabs.Screen
        name="chats"
        options={{
          title: 'Chats',
          tabBarIcon: ({ color, focused }) => (
            <View className="relative">
              {focused ? (
                <ChatFilledIcon fill={color} color={color} />
              ) : (
                <ChatIcon strokeColor={color} />
              )}

              {hasUnreadMessages && (
                <View className="absolute right-0 top-0 size-2 rounded-full bg-red-50" />
              )}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="favourites"
        options={{
          title: 'Favourites',
          tabBarIcon: ({ color, focused }) =>
            focused ? (
              <HeartFilledIcon color={color} />
            ) : (
              <HeartIcon color={color} />
            ),
        }}
      />
    </Tabs>
  );
}
