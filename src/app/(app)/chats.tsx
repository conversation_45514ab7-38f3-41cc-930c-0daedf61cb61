import { BlurView } from 'expo-blur';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Keyboard, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import Animated, {
  Extrapolation,
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDebounce } from 'use-debounce';

import { AvatarWithLetter } from '@/components/avatars/avatar-with-letter';
import { ChatListItem } from '@/components/chats/list/chat-list-item';
import { MessageRequestsSection } from '@/components/chats/list/message-requests';
import { ChatsLoading } from '@/components/loading/chats';
import { NewChatModalContent } from '@/components/modals/new-chat';
import { EmptyState } from '@/components/ui';
import {
  FocusAwareStatusBar,
  H1,
  H3,
  List,
  MdBoldLabel,
  Modal,
  SafeAreaView,
  ScrollView,
  SearchInput,
  semanticColors,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { useChatList, useMessageRequests } from '@/lib';

import { type FormType } from '../chats/search';
import { TAB_HEIGHT } from './_layout';

export default function Chats() {
  const insets = useSafeAreaInsets();
  const modal = useModal();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const snapPoints = React.useMemo(() => ['60%', '85%'], []);

  const { chatList, loading, setLoading } = useChatList();
  const {
    requests,
    loading: requestsLoading,
    acceptRequest,
    declineRequest,
  } = useMessageRequests();

  const { watch, setValue } = useForm<FormType>({
    defaultValues: { search: '' },
  });
  const searchQuery = watch('search');
  const [debouncedText] = useDebounce(searchQuery, 500);

  const filteredChatList = chatList.filter((chat) =>
    chat.userName.toLowerCase().includes(debouncedText.toLowerCase())
  );

  const scrollY = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const bigTitleStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [0, 50],
      [1, 0],
      Extrapolation.CLAMP
    );
    const height = interpolate(
      scrollY.value,
      [0, 50],
      [60, 0],
      Extrapolation.CLAMP
    );

    return {
      opacity,
      height,
    };
  });

  // Fade in smaller H3 in top bar
  const smallTitleStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [30, 70], [0, 1], Extrapolation.CLAMP),
  }));

  // Fade out buttons on scroll
  const buttonsStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [10, 60], [1, 0], Extrapolation.CLAMP),
  }));

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="flex-1">
          <View className="h-14 flex-row items-center justify-between px-4">
            <AvatarWithLetter />
            <Animated.View
              style={smallTitleStyle}
              className="absolute inset-x-0 items-center"
            >
              <H3>Chats</H3>
            </Animated.View>
            <Animated.View style={buttonsStyle}>
              <TouchableOpacity
                className="flex-row items-center justify-center gap-x-1 rounded-full bg-brand-20 px-4 py-2 dark:bg-brand-90"
                onPress={modal.present}
              >
                <MdBoldLabel className="text-brand-70 dark:text-brand-40">
                  New chat
                </MdBoldLabel>
              </TouchableOpacity>
            </Animated.View>
          </View>
          <Animated.View style={bigTitleStyle}>
            <H1 className="p-4 pt-0">Chats</H1>
          </Animated.View>
          <View className="mx-4">
            <SearchInput
              value={searchQuery}
              onChangeText={(text) => setValue('search', text)}
              placeholder="Search"
              showClearButton={searchQuery !== ''}
            />
          </View>
          {requestsLoading || loading ? (
            <ChatsLoading />
          ) : (
            <View className="relative flex-1">
              {/* Message Requests Section */}
              {!(requests.length || filteredChatList.length) && (
                <View
                  className="flex-1"
                  style={{
                    marginBottom: TAB_HEIGHT + insets.bottom - 60,
                  }}
                >
                  <EmptyState text="You have no chats yet" />
                </View>
              )}
              <MessageRequestsSection
                requests={requests}
                onAccept={acceptRequest}
                onDecline={declineRequest}
              />
              {/* <Animated.ScrollView
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={16}
                onScroll={scrollHandler}
              > */}
              <ScrollView
                style={{
                  marginBottom: TAB_HEIGHT + insets.bottom,
                }}
              >
                <List
                  data={filteredChatList}
                  estimatedItemSize={74}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item }) => (
                    <ChatListItem chat={item} setLoading={setLoading} />
                  )}
                  ItemSeparatorComponent={() => <View className="size-2" />}
                  keyExtractor={(item) => String(item.id)}
                />
              </ScrollView>
              {/* </Animated.ScrollView> */}
            </View>
          )}
          <Modal
            ref={modal.ref}
            index={0}
            backgroundStyle={{
              backgroundColor: isDark
                ? '#070707'
                : semanticColors.bg.canvas.light,
            }}
            snapPoints={snapPoints}
            hasHandle={false}
            handleClassName="bg-bg-interactive-secondary-light dark:bg-bg-interactive-secondary-dark"
            topInset={insets.top}
          >
            <BlurView
              tint={isDark ? 'systemChromeMaterialDark' : 'extraLight'}
              intensity={isDark ? 20 : 70}
              style={styles.blurView}
            />
            <NewChatModalContent onDismiss={modal.dismiss} />
          </Modal>
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  blurView: {
    overflow: 'hidden',
    backgroundColor: 'transparent',
    ...StyleSheet.absoluteFillObject,
  },
});
