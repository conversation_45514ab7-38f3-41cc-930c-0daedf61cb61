import Slider from '@react-native-community/slider';
import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useCallback, useMemo, useRef, useState } from 'react';
import { RefreshControl } from 'react-native';
import { type Point } from 'react-native-google-places-autocomplete';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import Animated, {
  Extrapolation,
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  type AllEventsQueryParams,
  EventFormat,
  useGetEventCategories,
  useGetEvents,
  useSearchEvents,
} from '@/api/events';
import { AvatarWithLetter } from '@/components/avatars/avatar-with-letter';
import { PortraitEventCard } from '@/components/cards/portrait-event';
import EventCarousel from '@/components/carousel/event-carousel';
import LocationInput from '@/components/input/location-autocomplete';
import LoadingScreen from '@/components/loading';
import { EventsLoading } from '@/components/loading/events';
import { EmptyState } from '@/components/ui';
import {
  Button,
  colors,
  DatePicker,
  FocusAwareStatusBar,
  H1,
  H2,
  H3,
  List,
  MdBoldLabel,
  Modal,
  P,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { cn, filterEmptyProperties, useAuth, useKeyboardVisible } from '@/lib';
import { type LocationType } from '@/types';

export default function Event() {
  const currentUser = useAuth.use.user();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  const isKeyboardVisible = useKeyboardVisible();
  const modal = useModal();
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);
  const locationInputRef = useRef<View>(null);

  const [refreshing, setRefreshing] = useState(false);
  const queryClient = useQueryClient();

  const scrollY = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const bigTitleStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [0, 50],
      [1, 0],
      Extrapolation.CLAMP
    );
    const height = interpolate(
      scrollY.value,
      [0, 50],
      [60, 0],
      Extrapolation.CLAMP
    );

    return {
      opacity,
      height,
    };
  });

  const subtitleStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [0, 30], [1, 0], Extrapolation.CLAMP),
  }));

  // Fade in smaller H3 in top bar
  const smallTitleStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [30, 70], [0, 1], Extrapolation.CLAMP),
  }));

  // Fade out buttons on scroll
  const buttonsStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [10, 60], [1, 0], Extrapolation.CLAMP),
  }));

  const onRefresh = useCallback(() => {
    setRefreshing(true);

    setTimeout(() => {
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['getEvents'],
          exact: false,
        }),
        queryClient.invalidateQueries({
          queryKey: ['searchEvents'],
          exact: true,
        }),
        queryClient.invalidateQueries({
          queryKey: ['getEventCategories'],
          exact: true,
        }),
      ]).finally(() => {
        setRefreshing(false);
      });
    }, 1000);
  }, [queryClient]);

  const snapPoints = useMemo(() => ['30%'], []);

  const [selectedCategoryTab, setSelectedCategoryTab] = useState<{
    id: string;
    name: string;
  }>({ id: 'all', name: 'All' });
  const [eventDate, setSelectedEventDate] = useState(
    dayjs(new Date()).format('YYYY-MM-DD')
  );
  const [locationObject, setLocationObject] =
    useState<Pick<AllEventsQueryParams, 'city' | 'country'>>();
  const [coordinates, setCoordinates] =
    useState<Pick<LocationType, 'coordinates'>['coordinates']>();

  const { data: eventsData, isLoading } = useGetEvents({
    variables: {
      userId: currentUser?.id,
      eventDate,
      ...(selectedCategoryTab.id !== 'all' && {
        categoryId: selectedCategoryTab.id,
      }),
      ...(locationObject && filterEmptyProperties(locationObject)),
      ...(coordinates && { coordinates: JSON.stringify(coordinates) }),
    },
    refetchInterval: 60 * 1000,
  });

  const { data: eventData } = useSearchEvents({
    variables: { skip: 0, take: 8, isFeatured: true },
  });

  const { data: categories, isLoading: categoriesLoading } =
    useGetEventCategories();

  const onSelectLocation = useCallback(
    async (
      { city, country }: Omit<LocationType, 'coordinates'>,
      { lat, lng }: Point
    ) => {
      setLocationObject({
        city,
        country,
      });
      if (lat !== 0 || lng !== 0) {
        setCoordinates({ lat, lng });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const handleLocationInputFocus = () => {
    if (locationInputRef.current && scrollViewRef.current) {
      locationInputRef.current.measureLayout(
        scrollViewRef.current as unknown as number,
        (_x, y) => {
          // Scroll to position the LocationInput at the top of the viewport
          scrollViewRef.current?.scrollTo({
            y,
            animated: true,
          });
        },
        () => {
          console.warn('Failed to measure LocationInput position:');
        }
      );
    }
  };

  if (categoriesLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1 px-4">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior="padding"
        keyboardVerticalOffset={0}
      >
        <View className="h-14 flex-row items-center justify-between">
          <AvatarWithLetter />
          <Animated.View
            style={smallTitleStyle}
            className="absolute inset-x-0 items-center"
          >
            <H3>Events</H3>
          </Animated.View>

          <Animated.View style={buttonsStyle}>
            {currentUser?.role === 'QUESTER' ? (
              <TouchableOpacity
                className="flex-row items-center justify-center rounded-full bg-brand-20 px-4 py-2 dark:bg-brand-90"
                onPress={() => {
                  router.push('/events/tickets');
                }}
              >
                <MdBoldLabel className="text-brand-70 dark:text-brand-40">
                  Tickets
                </MdBoldLabel>
              </TouchableOpacity>
            ) : (
              <View className="flex-row gap-x-2">
                <TouchableOpacity
                  className="flex-row items-center justify-center rounded-full bg-brand-20 px-4 py-2 dark:bg-brand-90"
                  onPress={() => {
                    router.push(`/events/my-events`);
                  }}
                >
                  <MdBoldLabel className="text-brand-70 dark:text-brand-40">
                    My Events
                  </MdBoldLabel>
                </TouchableOpacity>
                <TouchableOpacity
                  className="flex-row items-center justify-center rounded-full bg-brand-20 px-4 py-2 dark:bg-brand-90"
                  onPress={() => {
                    router.push('/events/tickets');
                  }}
                >
                  <MdBoldLabel className="text-brand-70 dark:text-brand-40">
                    Tickets
                  </MdBoldLabel>
                </TouchableOpacity>
              </View>
            )}
          </Animated.View>
        </View>
        <Animated.View style={bigTitleStyle} className="gap-1">
          <H1>Events</H1>
          <Animated.View style={subtitleStyle}>
            <P className="text-grey-60 dark:text-grey-50">
              Find your next adventure.
            </P>
          </Animated.View>
        </Animated.View>

        {/* <Animated.ScrollView
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        > */}
        <ScrollView
          ref={scrollViewRef}
          style={{
            marginBottom: isKeyboardVisible ? 0 : insets.bottom + 20,
          }}
          className="bg-bg-canvas flex-1 dark:bg-grey-100"
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          keyboardShouldPersistTaps="handled"
        >
          {eventData && eventData.events.length > 0 && (
            <EventCarousel events={eventData?.events} />
          )}
          <DatePicker
            onSelected={(date) =>
              setSelectedEventDate(dayjs(date).format('YYYY-MM-DD'))
            }
          />

          {/* Location and money filter */}
          <View className="h-auto flex-row gap-2 py-4" ref={locationInputRef}>
            <LocationInput
              onSelectLocation={onSelectLocation}
              borderRadius={9999}
              hasCurrentLocationIcon={false}
              className="flex-1"
              onFocus={handleLocationInputFocus}
            />
            {/* <TouchableOpacity
            className="size-12 items-center justify-center rounded-[10px] bg-brand-60"
            onPress={modal.present}
          >
            <PaperMoneyIcon />
          </TouchableOpacity> */}
          </View>

          <ScrollView
            showsHorizontalScrollIndicator={false}
            horizontal
            className="max-h-12"
          >
            {[{ id: 'all', category: 'All' }, ...(categories || [])].map(
              ({ category, id }, index) => (
                <TouchableOpacity
                  key={index}
                  className={cn(
                    'h-12 justify-center',
                    index !== 0 && 'ml-8',
                    category === selectedCategoryTab.name &&
                      'border-b border-b-brand-60'
                  )}
                  onPress={() => setSelectedCategoryTab({ id, name: category })}
                >
                  <MdBoldLabel
                    className={cn(
                      'text-grey-50 dark:text-grey-60',
                      category === selectedCategoryTab.name &&
                        'text-brand-60 dark:text-brand-60'
                    )}
                  >
                    {category}
                  </MdBoldLabel>
                </TouchableOpacity>
              )
            )}
          </ScrollView>

          {isLoading ? (
            <EventsLoading />
          ) : (
            <View className="flex-1 gap-4 py-4">
              <List
                data={eventsData?.events}
                estimatedItemSize={74}
                ListEmptyComponent={<EmptyState />}
                renderItem={({ item, index }) => (
                  <PortraitEventCard
                    {...item}
                    id={item.id.toString()}
                    isEven={index % 2 === 0}
                    startTime={item.startTime}
                    bannerUrl={
                      item.bannerUrl ||
                      require('~/assets/gradient-bg/portrait-event-gradient.png')
                    }
                    title={item.title}
                    hasPhysicalLocation={
                      item.eventFormat !== EventFormat.ONLINE
                    }
                  />
                )}
                keyExtractor={(item) => item.id.toString()}
                ItemSeparatorComponent={() => <View className="size-4 " />}
                numColumns={2}
              />
            </View>
          )}
        </ScrollView>
        {/* </Animated.ScrollView> */}
        <Modal
          ref={modal.ref}
          index={0}
          snapPoints={snapPoints}
          backgroundStyle={{
            backgroundColor: isDark ? colors.grey[100] : colors.white,
          }}
        >
          <View className="gap-6 p-4">
            <H2>Pricing</H2>
            <View className="gap-2">
              <P>
                Price between:{' '}
                <P className="text-accent-moderate dark:text-accent-moderate">
                  N0 – N700,000
                </P>
              </P>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={0}
                maximumValue={1}
                maximumTrackTintColor={
                  isDark ? colors.brand[90] : colors.brand[20]
                }
                minimumTrackTintColor={colors.brand[60]}
                thumbTintColor={colors.brand[60]}
              />
              {/* <RangeSlider
              style={styles.slider}
              min={0}
              max={5000000}
              step={1}
              renderThumb={renderThumb}
              renderRail={renderRail}
              renderRailSelected={renderRailSelected}
              onValueChanged={handleValueChange}
              renderLabel={renderLabel}
            /> */}
            </View>
            <Button
              label="Continue"
              textClassName="text-brand-70 dark:text-brand-40"
              className="bg-brand-20 dark:bg-brand-90"
            />
          </View>
        </Modal>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
