import { FontAwesome6, Ionicons } from '@expo/vector-icons';
import {
  BottomSheetScrollView,
  BottomSheetView,
  useBottomSheetModal,
} from '@gorhom/bottom-sheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import * as Location from 'expo-location';
import { useFocusEffect, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useCallback, useMemo, useReducer, useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Linking } from 'react-native';
import { type Point } from 'react-native-google-places-autocomplete';
import MapView, { Marker, type Region } from 'react-native-maps';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { type UserObjectData } from '@/api/auth';
import { type IEvent, useGetEvents } from '@/api/events';
import {
  type LiveSessionResponse,
  useGetNearbySessions,
  useJoinLiveSession,
} from '@/api/session';
import { useGetFarawayUsers } from '@/api/user';
import { RegularAvatar } from '@/components/avatars/regular-avatar';
import { LocationAccessDialog } from '@/components/dialogs/location-access';
import { MapEventCard } from '@/components/map/card/event';
import { MapSessionCard } from '@/components/map/card/session';
import { EmptyState } from '@/components/ui';
import {
  Button,
  ChatIcon,
  colors,
  FocusAwareStatusBar,
  H3,
  H4,
  HeartFilledIcon,
  HeartIcon,
  Image,
  MdRegularLabel,
  Modal,
  Pressable,
  Skeleton,
  SmRegularLabel,
  Text,
  View,
  XsBoldLabel,
  XsRegularLabel,
} from '@/components/ui';
import { useModal } from '@/components/ui/modal';
import {
  cn,
  mapCustomStyle,
  pxToSnapPoint,
  useAccountFavourite,
  useLocation,
  useLoggedInUser,
} from '@/lib';
import { useSession } from '@/lib/session';
import { formatDateTime } from '@/lib/utils/formatDateTime';

import { TAB_HEIGHT } from './_layout';

const MODAL_TYPES = ['explore', 'event', 'people', 'session'] as const;
type ModalType = (typeof MODAL_TYPES)[number];
type ModalKeysState = Record<ModalType, number>;
type ModalKeysAction =
  | { type: 'INCREMENT_ALL' }
  | { type: 'INCREMENT_SINGLE'; modal: ModalType }
  | { type: 'RESET' };

const initialModalKeys: ModalKeysState = MODAL_TYPES.reduce(
  (acc, modal) => ({ ...acc, [modal]: 0 }),
  {} as ModalKeysState
);

const modalKeysReducer = (
  state: ModalKeysState,
  action: ModalKeysAction
): ModalKeysState => {
  switch (action.type) {
    case 'INCREMENT_ALL':
      return Object.keys(state).reduce((acc, key) => {
        acc[key as ModalType] = state[key as ModalType] + 1;
        return acc;
      }, {} as ModalKeysState);

    case 'INCREMENT_SINGLE':
      return {
        ...state,
        [action.modal]: state[action.modal] + 1,
      };

    case 'RESET':
      return Object.keys(state).reduce((acc, key) => {
        acc[key as ModalType] = 0;
        return acc;
      }, {} as ModalKeysState);

    default:
      return state;
  }
};

const tabs = ['all', 'people', 'events', 'live'] as const;

type Tab = (typeof tabs)[number];

const distance = 100;
const distanceInMeters = distance * 1000;
const OFFSET = 0.02;

const ExploreScreen = () => {
  const { data: user } = useLoggedInUser();
  const insets = useSafeAreaInsets();

  const base = pxToSnapPoint(TAB_HEIGHT + insets.bottom + 32);
  const pxBase = TAB_HEIGHT + insets.bottom + 32;
  const router = useRouter();

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [selectedTab, setSelectedTab] = useState<Tab>('all');

  const snapPoints = useMemo(() => {
    switch (selectedTab) {
      case 'all':
        return [base, '45%', '80%'];
      case 'events':
        return [base, pxToSnapPoint(250 + pxBase)];
      case 'people':
        return [base, pxToSnapPoint(180 + pxBase)];
      case 'live':
        return [base, pxToSnapPoint(300 + pxBase)];
      default:
        return [base];
    }
  }, [selectedTab, base, pxBase]);

  const [modalKeys, dispatchModalKeys] = useReducer(
    modalKeysReducer,
    initialModalKeys
  );

  const exploreModal = useModal();

  const { location, locationAccessible } = useLocation();

  const mapRef = React.useRef<MapView>(null!);

  const { dismissAll: dismissAllModals } = useBottomSheetModal();

  useFocusEffect(
    useCallback(() => {
      dispatchModalKeys({ type: 'INCREMENT_ALL' });

      const id = requestAnimationFrame(() => {
        exploreModal.present();
      });

      return () => {
        cancelAnimationFrame(id);
        dismissAllModals();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
  );

  const [locationAccessVisible, setLocationAccessVisible] =
    React.useState(false);

  const filterDistance = locationAccessible ? 10 : 5000;

  const checkLocationAccess = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      setLocationAccessVisible(true);
    } else {
      setLocationAccessVisible(false);
      location?.coordinates &&
        mapRef.current?.animateToRegion({
          latitude: location.coordinates.lat - OFFSET,
          longitude: location.coordinates.lng,
          latitudeDelta: 0.005,
          longitudeDelta: 0.005,
        });
    }
  };

  React.useEffect(() => {
    checkLocationAccess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const {
    data: eventsData,
    isLoading: isEventsLoading,
    isPending: isEventsPending,
  } = useGetEvents({
    variables: {
      maxDistance: String(distanceInMeters),
      coordinates: location?.coordinates
        ? JSON.stringify({
            lat: location.coordinates.lat,
            lng: location.coordinates.lng,
          })
        : undefined,
    },
    enabled: !!location?.coordinates,
    refetchInterval: 60 * 1000,
  });

  const eventModal = useModal();

  const [selectedEvent, setSelectedEvent] = useState<IEvent>();

  const delta = useMemo(() => filterDistance / 500, [filterDistance]);

  const {
    data: farawayUsers,
    isLoading: isLoadingUsers,
    isPending: isPendingUsers,
  } = useGetFarawayUsers({
    variables: {
      minDistance: String(0 * 1000),
      // ...(filterDistance !== 100 && {
      //   maxDistance: String(filterDistance * 1000),
      // }),
    },
    enabled: !!location?.coordinates,
    refetchInterval: 60 * 1000,
  });

  const [quester, setQuester] = React.useState<UserObjectData>();
  const [currentRegion, setCurrentRegion] = useState<Region | null>(null);

  const chatRoomId = useMemo(() => {
    if (!user?.id || !quester?.id) return '';
    return [user.id, quester.id].sort().join('_');
  }, [user?.id, quester?.id]);

  function handleItemPress<
    T extends { location?: { coordinates: Point } | null | undefined },
  >(
    item: T,
    modal: ReturnType<typeof useModal>,
    setSelected: React.Dispatch<React.SetStateAction<T | undefined>>,
    offset?: number
  ) {
    setSelected(item);
    item.location?.coordinates &&
      mapRef.current?.animateToRegion({
        latitude: item.location?.coordinates.lat - (offset || OFFSET),
        longitude: item.location?.coordinates.lng,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
    modal.present();
  }

  const { favourited, toggleFavourite } = useAccountFavourite(
    quester?.id || ''
  );

  const questerModal = useModal();

  const {
    data: sessions,
    isLoading: isSessionsLoading,
    isPending: isSessionsPending,
  } = useGetNearbySessions({
    variables: {
      maxDistance: String(distanceInMeters),
      status: 'LIVE',
    },
  });

  const setJoinedSessionId = useSession.use.setJoinedSessionId();
  const { mutate: joinLiveSession } = useJoinLiveSession();

  const handleJoinLiveSession = React.useCallback(
    (sessionId: string) => {
      joinLiveSession(
        { sessionId },
        {
          onSuccess: () => {
            setJoinedSessionId(sessionId);
            router.push({
              pathname: '/session/[id]',
              params: { id: sessionId },
            });
          },
          onError: (error) => toast.error(error.message),
        }
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [joinLiveSession, setJoinedSessionId]
  );

  const sessionModal = useModal();

  const [selectedSession, setSelectedSession] = useState<LiveSessionResponse>();

  const isWithinThreshold = (a: number, b: number, threshold = 0.005) =>
    Math.abs(a - b) <= threshold;

  const onMoveToLocation = <
    T extends { location?: { coordinates: Point } | null | undefined },
  >({
    item,
    setSelected,
    offset = OFFSET,
    modal,
  }: {
    item: T;
    modal?: ReturnType<typeof useModal>;
    setSelected?: React.Dispatch<React.SetStateAction<T | undefined>>;
    offset?: number;
  }) => {
    const { lat, lng } = item.location?.coordinates || { lat: 0, lng: 0 };
    const targetLatitude = lat - offset;
    const targetLongitude = lng;
    if (
      currentRegion &&
      isWithinThreshold(currentRegion.latitude, targetLatitude) &&
      isWithinThreshold(currentRegion.longitude, targetLongitude)
    ) {
      setSelected?.(item);
      modal?.present();
    } else {
      mapRef.current?.animateToRegion({
        latitude: lat - offset,
        longitude: lng,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
    }
    exploreModal?.snapToIndex(1);
  };

  if (!user || !location) return null;

  return (
    <BottomSheetModalProvider>
      <View style={StyleSheet.absoluteFill}>
        <FocusAwareStatusBar />
        <View className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
          <MapView
            ref={mapRef}
            region={{
              latitude: location.coordinates.lat - OFFSET,
              longitude: location.coordinates.lng,
              latitudeDelta: delta,
              longitudeDelta: delta,
            }}
            onRegionChangeComplete={(region) => {
              setCurrentRegion(region);
            }}
            maxZoomLevel={15}
            cameraZoomRange={{
              minCenterCoordinateDistance: 20000,
              animated: true,
            }}
            style={StyleSheet.absoluteFill}
            showsPointsOfInterest={false}
            showsBuildings={false}
            mapType={Platform.OS === 'ios' ? 'mutedStandard' : 'standard'}
            userInterfaceStyle={isDark ? 'dark' : 'light'}
            customMapStyle={
              isDark && Platform.OS === 'android' ? mapCustomStyle : []
            }
            showsUserLocation={true}
            showsMyLocationButton
            loadingEnabled
          >
            {(selectedTab === 'events' || selectedTab === 'all') &&
              (Array.isArray(eventsData?.events) ? eventsData.events : []).map(
                (event, index) => {
                  if (!event.location?.coordinates) return null;
                  return (
                    <Marker
                      key={`event-${index}`}
                      coordinate={{
                        latitude: event.location.coordinates.lat,
                        longitude: event.location.coordinates.lng,
                      }}
                      anchor={{ x: 0.5, y: 1.0 }}
                      onPress={() =>
                        handleItemPress<IEvent>(
                          event,
                          eventModal,
                          setSelectedEvent,
                          0.03
                        )
                      }
                    >
                      <View className="items-center justify-center">
                        <View className="relative h-[38px] w-8">
                          <Image
                            source={require('~/assets/images/explore/event-pin.png')}
                            className="h-[38px] w-8"
                          />
                          <View className="absolute inset-x-0 top-1 items-center">
                            <Image
                              source={require('~/assets/images/explore/event-icon.png')}
                              className="size-[26px] rounded-full"
                            />
                          </View>
                        </View>
                      </View>
                    </Marker>
                  );
                }
              )}
            {(selectedTab === 'live' || selectedTab === 'all') &&
              sessions?.djSessions.map((session, index) => {
                if (!session.location?.coordinates) return null;
                return (
                  <Marker
                    key={`live-${index}`}
                    coordinate={{
                      latitude: session.location.coordinates.lat,
                      longitude: session.location.coordinates.lng,
                    }}
                    anchor={{ x: 0.5, y: 1.0 }}
                    onPress={() =>
                      handleItemPress<LiveSessionResponse>(
                        session,
                        sessionModal,
                        setSelectedSession,
                        0.03
                      )
                    }
                  >
                    <View className="items-center justify-center">
                      <View className="relative h-[38px] w-8">
                        <Image
                          source={require('~/assets/images/explore/live-pin.png')}
                          className="h-[38px] w-8"
                        />
                        <View className="absolute inset-x-0 top-1 items-center">
                          <Image
                            source={require('~/assets/images/explore/live-icon.png')}
                            className="size-[26px] rounded-full"
                          />
                        </View>
                      </View>
                    </View>
                  </Marker>
                );
              })}
            {(selectedTab === 'people' || selectedTab === 'all') &&
              farawayUsers?.users.map(({ user: farawayUser }, index) => {
                if (!farawayUser.location?.coordinates) return null;
                return (
                  <Marker
                    key={`user-${farawayUser.id}-${index}`}
                    coordinate={{
                      latitude: farawayUser.location.coordinates.lat,
                      longitude: farawayUser.location.coordinates.lng,
                    }}
                    anchor={{ x: 0.5, y: 1.0 }}
                    onPress={() =>
                      farawayUser.location?.coordinates &&
                      handleItemPress<UserObjectData>(
                        farawayUser,
                        questerModal,
                        setQuester
                      )
                    }
                  >
                    <View className="items-center justify-center">
                      <View className="relative h-[38px] w-8">
                        <Image
                          source={require('~/assets/images/explore/pin.png')}
                          className="h-[38px] w-8"
                        />
                        <View className="absolute inset-x-0 top-1 items-center">
                          <Image
                            source={
                              farawayUser.profileImageUrl
                                ? { uri: farawayUser.profileImageUrl }
                                : require('~/assets/images/avatar.png')
                            }
                            className="size-[26px] rounded-full"
                            priority="high"
                          />
                        </View>
                      </View>
                    </View>
                  </Marker>
                );
              })}
          </MapView>

          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior="padding"
            keyboardVerticalOffset={10}
          >
            <View className="flex-1">
              <View
                className="flex flex-row items-start justify-center px-4"
                style={{ paddingTop: insets.top + 8 }}
              >
                <Pressable
                  className="mb-2 flex h-12 flex-1 flex-row items-center justify-start gap-4 rounded-xl bg-bg-subtle-light px-4 text-base/100 text-fg-subtle-light dark:bg-bg-subtle-dark dark:text-white"
                  onPress={() => router.push({ pathname: '/explore/search' })}
                >
                  <Ionicons
                    name="search"
                    size={18}
                    color={isDark ? colors.white : colors.grey[100]}
                  />
                  <MdRegularLabel className="text-fg-subtle-light dark:text-fg-subtle-dark">
                    Search
                  </MdRegularLabel>
                </Pressable>
              </View>
              <View className="flex-row items-center gap-x-2 px-4">
                {tabs.map((tab) => {
                  const isSelected = selectedTab === tab;

                  return (
                    <Pressable
                      key={tab}
                      onPress={() => {
                        setSelectedTab(tab);
                        exploreModal.snapToIndex(1);
                      }}
                      className="flex-1"
                    >
                      <View
                        className={`items-center rounded-full py-2 transition-all duration-300 ${
                          selectedTab === tab
                            ? 'scale-105 bg-accent-moderate dark:bg-accent-moderate'
                            : 'scale-100 bg-accent-subtle-light dark:bg-accent-subtle-dark'
                        }`}
                      >
                        <Text
                          className={`text-sm font-medium ${
                            isSelected
                              ? 'text-accent-on-accent dark:text-accent-on-accent'
                              : 'text-fg-muted-light dark:text-fg-muted-dark'
                          }`}
                        >
                          {tab.charAt(0).toUpperCase() + tab.slice(1)}
                        </Text>
                      </View>
                    </Pressable>
                  );
                })}
              </View>
            </View>
          </KeyboardAvoidingView>
          <Modal
            key={`explore-${modalKeys.explore}`}
            ref={exploreModal.ref}
            snapPoints={snapPoints}
            index={1}
            enablePanDownToClose={false}
            enableContentPanningGesture={
              Platform.OS === 'android' ? false : undefined
            }
            enableDismissOnClose={false}
            backdropComponent={() => null}
          >
            <BottomSheetScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingBottom: TAB_HEIGHT + insets.bottom + 16,
              }}
            >
              {!location ? (
                <View className="gap-y-2 px-4">
                  <H3>Events and live sessions near you</H3>
                  <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                    Set your location to discover events and live sessions
                    nearby.
                  </Text>
                </View>
              ) : (
                <>
                  {(selectedTab === 'all' || selectedTab === 'people') && (
                    <View className="gap-y-4 py-2">
                      <View className="gap-y-2 px-4">
                        <H3>People</H3>
                        <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                          Connect with people around you
                        </Text>
                      </View>
                      <View className="gap-x-4 py-3">
                        {isLoadingUsers || isPendingUsers ? (
                          <View className="px-4">
                            <Skeleton className="h-56 w-full rounded-lg px-4" />
                          </View>
                        ) : (
                          farawayUsers?.users.length === 0 && (
                            <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                              No user found close to you.
                            </Text>
                          )
                        )}
                        {farawayUsers && farawayUsers.users.length > 0 && (
                          <View className="gap-y-2">
                            <ScrollView
                              horizontal
                              showsHorizontalScrollIndicator={false}
                              contentContainerStyle={{ paddingHorizontal: 4 }}
                            >
                              <View className="flex-row items-center gap-x-4">
                                {farawayUsers.users.map(
                                  ({ user: farawayUser, distance }, index) => {
                                    if (!farawayUser.location?.coordinates)
                                      return null;
                                    const distanceText =
                                      distance < 1000
                                        ? `${Math.round(distance)}m away`
                                        : `${Math.round(distance / 1000)}km away`;
                                    return (
                                      <Pressable
                                        key={`${farawayUser.id}-${index}`}
                                        onPress={() => {
                                          if (
                                            farawayUser.location?.coordinates
                                          ) {
                                            onMoveToLocation<UserObjectData>({
                                              item: farawayUser,
                                              setSelected: setQuester,
                                              modal: questerModal,
                                            });
                                          }
                                        }}
                                        className={cn(
                                          'items-center gap-y-1 ml-0',
                                          index === 0 && 'ml-4'
                                        )}
                                      >
                                        <RegularAvatar
                                          avatar={
                                            farawayUser.profileImageUrl ||
                                            require('~/assets/images/avatar.png')
                                          }
                                          size={64}
                                          className="rounded-full"
                                        />
                                        <Text className="text-xs text-fg-muted-light dark:text-fg-muted-dark">
                                          {distanceText}
                                        </Text>
                                      </Pressable>
                                    );
                                  }
                                )}
                              </View>
                            </ScrollView>
                          </View>
                        )}
                      </View>
                    </View>
                  )}
                  {(selectedTab === 'all' || selectedTab === 'live') && (
                    <View className="gap-y-4 py-2">
                      <View className="gap-y-2 px-4">
                        <H3>Live</H3>
                        <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                          Join live sessions around you
                        </Text>
                      </View>
                      {isSessionsLoading || isSessionsPending ? (
                        <View className="px-4">
                          <Skeleton className="h-56 w-full rounded-lg px-4" />
                        </View>
                      ) : sessions?.djSessions.length === 0 ? (
                        <>
                          <EmptyState />
                        </>
                      ) : (
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          contentContainerStyle={{ paddingHorizontal: 4 }}
                        >
                          <View className="flex-row items-center gap-x-4">
                            {sessions?.djSessions?.map((item, index) => (
                              <MapSessionCard
                                key={index}
                                index={index}
                                session={item}
                                userLocation={location}
                                onSelectLiveSession={() => {
                                  if (item.location.coordinates)
                                    onMoveToLocation<LiveSessionResponse>({
                                      item,
                                      setSelected: setSelectedSession,
                                      modal: sessionModal,
                                    });
                                }}
                                handleJoinLiveSession={() => {
                                  if (item.location.coordinates)
                                    onMoveToLocation<LiveSessionResponse>({
                                      item,
                                      setSelected: setSelectedSession,
                                      modal: sessionModal,
                                    });
                                }}
                              />
                            ))}
                          </View>
                        </ScrollView>
                      )}
                    </View>
                  )}
                  {(selectedTab === 'all' || selectedTab === 'events') && (
                    <View>
                      <View className="gap-y-2 px-4">
                        <H3>Events near you</H3>
                        <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                          Some events close to you
                        </Text>
                      </View>
                      <View className="gap-y-4 py-3">
                        {isEventsLoading || isEventsPending ? (
                          <View className="px-4">
                            <Skeleton className="h-56 w-full rounded-lg" />
                          </View>
                        ) : eventsData?.events.length === 0 ? (
                          <EmptyState />
                        ) : (
                          <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={{ paddingHorizontal: 4 }}
                          >
                            <View className="flex-row items-center gap-x-4">
                              {eventsData?.events?.map((item, index) => (
                                <MapEventCard
                                  key={index}
                                  event={item}
                                  userLocation={location}
                                  onSelectEvent={() => {
                                    if (item.location.coordinates)
                                      onMoveToLocation<IEvent>({
                                        item,
                                        setSelected: setSelectedEvent,
                                        modal: eventModal,
                                      });
                                  }}
                                  className={cn('ml-0', index === 0 && 'ml-4')}
                                />
                              ))}
                            </View>
                          </ScrollView>
                        )}
                      </View>
                    </View>
                  )}
                </>
              )}
            </BottomSheetScrollView>
          </Modal>
          <Modal
            key={`people-${modalKeys.people}`}
            ref={questerModal.ref}
            backgroundStyle={{
              backgroundColor: 'transparent',
            }}
            hasHandle={false}
            enableDynamicSizing
            enablePanDownToClose={false}
            enableDismissOnClose={false}
            backdropComponent={() => null}
          >
            <BottomSheetView
              className="min-h-30 gap-4 px-4"
              style={{
                paddingBottom: TAB_HEIGHT + insets.bottom + 16,
                paddingTop: 16,
              }}
            >
              <View className="rounded-lg bg-bg-subtle-light dark:bg-bg-subtle-dark">
                <View className="items-center justify-center p-4">
                  <View className="items-center">
                    <View className="rounded-full border-2 border-bg-canvas-light dark:border-bg-canvas-dark">
                      <Image
                        source={
                          quester?.profileImageUrl ||
                          require('~/assets/images/avatar.png')
                        }
                        className="size-16 rounded-full"
                        priority="high"
                      />
                    </View>
                    <H4>{quester?.username}</H4>
                  </View>
                </View>

                <Pressable
                  onPress={() => {
                    exploreModal.dismiss();
                    router.push({
                      pathname: '/chats/[chatRoomId]',
                      params: {
                        chatRoomId,
                        username: quester?.username,
                        userId: quester?.id,
                        userAvatar: quester?.profileImageUrl,
                      },
                    });
                  }}
                  className="h-16 flex-row items-center justify-between px-4"
                >
                  <View className="size-6 items-center justify-center">
                    <ChatIcon
                      strokeColor={isDark ? colors.grey[60] : colors.grey[50]}
                    />
                  </View>
                  <MdRegularLabel>Chat</MdRegularLabel>
                  <View className="size-6" />
                </Pressable>
                <Pressable
                  onPress={toggleFavourite}
                  className="h-16 flex-row items-center justify-between px-4"
                >
                  <View className="size-6 items-center justify-center">
                    {favourited ? (
                      <HeartFilledIcon color={colors.red[50]} />
                    ) : (
                      <HeartIcon
                        color={isDark ? colors.grey[60] : colors.grey[50]}
                      />
                    )}
                  </View>
                  <MdRegularLabel>
                    {favourited ? 'Unfavourite' : 'Favourite'} user
                  </MdRegularLabel>
                  <View className="size-4" />
                </Pressable>
                <Pressable
                  onPress={() => {
                    exploreModal.dismiss();
                    router.push({
                      pathname: `/users/[id]`,
                      params: {
                        id: quester?.id || '',
                      },
                    });
                  }}
                  className="h-16 flex-row items-center justify-between px-4"
                >
                  <View className="size-6 items-center justify-center">
                    <FontAwesome6
                      name="user"
                      size={16}
                      color={isDark ? colors.grey[60] : colors.grey[50]}
                    />
                  </View>
                  {/* <UserIcon color={isDark ? colors.grey[60] : colors.grey[50]} /> */}
                  <MdRegularLabel>View profile</MdRegularLabel>
                  <View className="size-6" />
                </Pressable>
              </View>
              <Button
                label="Cancel"
                className="h-16 rounded-full bg-bg-subtle-light dark:bg-bg-subtle-dark"
                variant="secondary"
                onPress={() => exploreModal.present()}
              />
            </BottomSheetView>
          </Modal>
          <Modal
            key={`session-${modalKeys.session}`}
            ref={sessionModal.ref}
            enableDynamicSizing
            enablePanDownToClose={false}
            enableDismissOnClose={false}
            backdropComponent={() => null}
          >
            <BottomSheetView
              className="min-h-40 flex-1 gap-y-6 px-4 py-6"
              style={{
                paddingBottom: TAB_HEIGHT + insets.bottom + 16,
                paddingTop: 16,
              }}
            >
              <Pressable
                className="relative gap-y-4"
                onPress={() => {
                  exploreModal.dismiss();
                  handleJoinLiveSession(selectedSession?.id || '');
                }}
              >
                <Image
                  className="h-[200px] w-full rounded-lg"
                  source={{ uri: selectedSession?.bannerUrl || '' }}
                />

                <View className="absolute size-full items-center justify-center">
                  <View className="bg-primary rounded-full px-6 py-2">
                    <Text className="font-semibold text-white">Join</Text>
                  </View>
                </View>

                {/* "Live" label top-right */}
                <View className="absolute right-2 top-2 rounded-full bg-red-600 px-3 py-1">
                  <Text className="text-xs font-bold uppercase text-white">
                    Live
                  </Text>
                </View>
              </Pressable>

              <View className="flex-row gap-x-2.5">
                <Image
                  source={
                    selectedSession?.dj?.profileImageUrl ||
                    require('~/assets/images/avatar.png')
                  }
                  className="size-16 rounded-full"
                  priority="high"
                />
                <H4>{selectedSession?.dj?.username}</H4>
                <XsRegularLabel>viewers</XsRegularLabel>
              </View>
              <Button
                label="Cancel"
                variant="destructive"
                onPress={() => exploreModal.present()}
              />
            </BottomSheetView>
          </Modal>
          <Modal
            key={`event-${modalKeys.event}`}
            ref={eventModal.ref}
            enableDynamicSizing
            enablePanDownToClose={false}
            enableDismissOnClose={false}
            backdropComponent={() => null}
          >
            {selectedEvent && (
              <BottomSheetView
                className="min-h-96 flex-1 gap-y-6 px-4 py-6"
                style={{
                  paddingBottom: TAB_HEIGHT + insets.bottom + 16,
                  paddingTop: 16,
                }}
              >
                <View className="gap-y-2">
                  <XsBoldLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                    {formatDateTime(new Date(selectedEvent.startTime))}
                  </XsBoldLabel>
                  <H4>{selectedEvent.title}</H4>
                </View>

                <Image
                  className="h-[200px] w-full rounded-md"
                  source={{ uri: selectedEvent.bannerUrl }}
                  priority="high"
                />
                <View className="gap-y-1">
                  {selectedEvent.isTicketed ? (
                    <>
                      <XsBoldLabel>Ticket starting from</XsBoldLabel>
                      <XsRegularLabel>
                        ₦
                        {
                          Object.values(
                            selectedEvent.ticketCategories ?? {}
                          ).reduce(
                            (cheapest, current) =>
                              current.cost < cheapest.cost ? current : cheapest,
                            { cost: 0 }
                          ).cost
                        }
                      </XsRegularLabel>
                    </>
                  ) : (
                    <XsBoldLabel>Free event</XsBoldLabel>
                  )}
                </View>
                <View className="gap-y-4">
                  <SmRegularLabel>{selectedEvent.description}</SmRegularLabel>
                  <Button
                    label="View details"
                    className="rounded-full"
                    onPress={() =>
                      router.push({
                        pathname: '/events/[id]',
                        params: {
                          id: selectedEvent.id,
                          slug: selectedEvent.slug,
                        },
                      })
                    }
                  />
                  <Button
                    label="Cancel"
                    className="rounded-full"
                    variant="secondary"
                    onPress={() => {
                      exploreModal.present();
                    }}
                  />
                </View>
              </BottomSheetView>
            )}
          </Modal>
        </View>
      </View>
      <LocationAccessDialog
        visible={locationAccessVisible}
        title="Find your vibe nearby!"
        message="To help you discover the best events, live sessions, and people near you, Popla needs access to your location. Don’t worry—we only use your location to personalize your experience and never share it without your permission."
        onCancel={() => setLocationAccessVisible(false)}
        onConfirm={async () => await Linking.openSettings()}
        dismissmLabel="Later"
        confirmLabel="Allow access"
      />
    </BottomSheetModalProvider>
  );
};
export default ExploreScreen;
