import React from 'react';

import { useGetUserFavourites } from '@/api/user';
import { AvatarWithLetter } from '@/components/avatars/avatar-with-letter';
import LoadingScreen from '@/components/loading';
import { FavouriteCreatorsTab } from '@/components/tab-views/favourite-creators-tab';
import { FavouriteEventsTab } from '@/components/tab-views/favourite-events-tab';
import {
  AppTab,
  FocusAwareStatusBar,
  H1,
  P,
  SafeAreaView,
  View,
} from '@/components/ui';
import { type TabScreenItem } from '@/types';

export default function Favourites() {
  const [index, setIndex] = React.useState(0);

  const { isLoading: isEventsLoading, data: events } = useGetUserFavourites({
    variables: {
      type: 'EVENT',
    },
    enabled: index === 0,
  });

  const { isLoading: isAccountsLoading, data: accounts } = useGetUserFavourites(
    {
      variables: {
        type: 'ACCOUNT',
      },
      enabled: index === 1,
    }
  );

  const tabItems: TabScreenItem[] = React.useMemo(() => {
    return [
      {
        key: 'EVENT',
        title: 'Events',
        component: () => <FavouriteEventsTab events={events} index={index} />,
      },
      {
        key: 'ACCOUNT',
        title: 'Accounts',
        component: () => (
          <FavouriteCreatorsTab accounts={accounts} index={index} />
        ),
      },
    ];
  }, [accounts, events]);

  if (isEventsLoading || isAccountsLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1 ">
      <FocusAwareStatusBar />
      <View className="px-4 py-3">
        <AvatarWithLetter className="self-start" />
      </View>

      {/* Title and subtitle */}
      <View className="gap-1 px-4 pb-4">
        <H1>Favourites</H1>
        <P className="text-grey-60 dark:text-grey-50">
          All your favourite events and accounts in one place.
        </P>
      </View>
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </SafeAreaView>
  );
}
