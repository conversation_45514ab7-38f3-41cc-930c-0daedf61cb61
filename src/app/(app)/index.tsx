import { Feather } from '@expo/vector-icons';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import LottieView from 'lottie-react-native';
import { useColorScheme } from 'nativewind';
import { useCallback, useMemo, useState } from 'react';
import React from 'react';
import { Platform, Pressable, RefreshControl } from 'react-native';
import MapView from 'react-native-maps';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import type { Point } from '@/api/auth';
import { useSearchEvents } from '@/api/events';
import { useJoinLiveSession } from '@/api/session';
import { useGetHomeData, useGetUserFavourites } from '@/api/user';
import { AvatarWithLetter } from '@/components/avatars/avatar-with-letter';
import { LiveUserAvatar } from '@/components/avatars/live-avatar';
import { HomeCompletionCard } from '@/components/cards/home-completion-card';
import { LiveUserCard } from '@/components/cards/live-user-card';
import EventCarousel from '@/components/carousel/event-carousel';
import { FloatingActionPopover } from '@/components/create';
import LoadingScreen from '@/components/loading';
import { HomeLoading } from '@/components/loading/home';
import { EmptyState } from '@/components/ui';
import {
  colors,
  FocusAwareStatusBar,
  H1,
  H5,
  Image,
  List,
  ScrollView,
  Skeleton,
  TouchableOpacity,
  View,
} from '@/components/ui';
import {
  cn,
  CREATOR_COMPLETION_CARDS,
  mapCustomStyle,
  QUESTER_COMPLETION_CARDS,
  useAppSession,
  useLoggedInUser,
  useNotificationStore,
} from '@/lib';
import { useLocation } from '@/lib';
import { getCombinedSessionAndDjSessionsList } from '@/lib/home/<USER>';
import { useSession } from '@/lib/session';
import { getLiveSession } from '@/lib/session/utils';

import { TAB_HEIGHT } from './_layout';

export default function Home() {
  const { colorScheme } = useColorScheme();

  const notifications = useNotificationStore.use.notifications();

  const hasNotifs = notifications.length > 0;

  const isDark = colorScheme === 'dark';

  const queryClient = useQueryClient();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);

    Promise.all([
      queryClient.invalidateQueries({
        queryKey: ['getUserFavourites'],
        exact: true,
      }),
      queryClient.invalidateQueries({
        queryKey: ['getHomeData'],
        exact: true,
      }),
      queryClient.invalidateQueries({
        queryKey: ['searchEvents'],
        exact: false,
      }),
      queryClient.invalidateQueries({
        queryKey: ['getTrendingCreators'],
        exact: true,
      }),
      queryClient.invalidateQueries({
        queryKey: ['getUser'],
        exact: false,
      }),
    ])
      .then(() => new Promise((resolve) => setTimeout(resolve, 1000)))
      .finally(() => {
        setRefreshing(false);
      });
  }, [queryClient]);

  const activeSession = getLiveSession();

  const { data: currentUser } = useLoggedInUser();

  const setJoinedSessionId = useSession.use.setJoinedSessionId();
  const { mutate: joinLiveSession, isPending: isJoiningSession } =
    useJoinLiveSession();
  const insets = useSafeAreaInsets();
  const { location, locationAccessible } = useLocation();

  const currentCoordinate: Pick<Point, 'lat' | 'lng'> = {
    lat: location?.coordinates.lat || 0,
    lng: location?.coordinates.lng || 0,
  };

  const { data, isLoading } = useGetHomeData({
    variables: {
      ...currentCoordinate,
      userCurrency: currentUser?.wallet?.currency || 'NGN',
    },
    enabled: currentCoordinate.lat !== 0 && currentCoordinate.lng !== 0,
    refetchInterval: 60 * 1000,
  });

  const { data: eventData } = useSearchEvents({
    variables: { skip: 0, take: 8, isFeatured: true },
  });

  const { data: favourites = [] } = useGetUserFavourites({
    variables: {
      type: 'ACCOUNT',
    },
  });

  const isCreator = currentUser?.role !== 'QUESTER';

  const creatorCompletedSteps = useMemo(
    () =>
      Number(Boolean(currentUser?.bio)) +
      Number(Boolean(currentUser?.profileImageUrl)) +
      Number(Boolean(currentUser?.category)) +
      // Number(Boolean(currentUser?.genres?.length)) +
      Number(Boolean(favourites?.length)),
    [
      currentUser?.bio,
      currentUser?.profileImageUrl,
      currentUser?.category,
      // currentUser?.genres?.length,
      favourites?.length,
    ]
  );

  const questerCompletedSteps = useMemo(
    () =>
      Number(Boolean(currentUser?.bio)) +
      Number(Boolean(currentUser?.profileImageUrl)) +
      // Number(Boolean(currentUser?.genres?.length)) +
      Number(Boolean(favourites?.length)),
    [
      currentUser?.bio,
      currentUser?.profileImageUrl,
      // currentUser?.genres?.length,
      favourites?.length,
    ]
  );

  const cards = isCreator
    ? CREATOR_COMPLETION_CARDS.filter((card) => {
        if (card.id === 'bio') return !currentUser?.bio;
        if (card.id === 'avatar') return !currentUser?.profileImageUrl;
        if (card.id === 'category') return !currentUser?.category;
        // if (card.id === 'preferences') return !currentUser?.genres.length;
        if (card.id === 'inspirations') return !favourites?.length;
        return true;
      })
    : QUESTER_COMPLETION_CARDS.filter((card) => {
        if (card.id === 'bio') return !currentUser?.bio;
        if (card.id === 'avatar') return !currentUser?.profileImageUrl;
        // if (card.id === 'preferences') return !currentUser?.genres.length;
        if (card.id === 'inspirations') return !favourites?.length;
        return true;
      });

  const { hideTodo, setHideTodo } = useAppSession();

  const hasTodo = useMemo(() => {
    if (!currentUser) return false;

    if (isCreator) {
      return creatorCompletedSteps < CREATOR_COMPLETION_CARDS.length;
    } else {
      return questerCompletedSteps < QUESTER_COMPLETION_CARDS.length;
    }
  }, [creatorCompletedSteps, isCreator, questerCompletedSteps, currentUser]);

  const handleJoinLiveSession = useCallback(
    (sessionId: string) => {
      const alreadyInSession = activeSession?.id === sessionId;
      const inAnotherSession = !!activeSession?.id && !alreadyInSession;

      if (inAnotherSession) {
        toast.info('Already in a live session', {
          description: 'Please exit your current session to join a new one',
        });
        return;
      }

      joinLiveSession(
        { sessionId },
        {
          onSuccess: () => {
            setJoinedSessionId(sessionId);
            toast.success('Successfully joined live session');
            router.push({
              pathname: '/session/[id]',
              params: { id: sessionId },
            });
          },
          onError: (error) => toast.error(error.message),
        }
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeSession?.id, joinLiveSession, setJoinedSessionId]
  );

  if (isJoiningSession) return <LoadingScreen />;

  if (isLoading || !data) return <HomeLoading />;

  const { djSessions, trendingDJs, user } = data;

  const combinedSessionAndTrendingList = getCombinedSessionAndDjSessionsList({
    activeSession: activeSession?.id || '',
    activeSessionCreator: activeSession?.dj?.username || '',
    djSessions: [...djSessions].reverse(),
    trendingDJs,
    user,
  });

  return (
    <View className="relative flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark">
      <FocusAwareStatusBar
        colorSchemeProp={Platform.OS === 'android' ? 'auto' : undefined}
      />
      <View
        style={{
          paddingTop: insets.top,
        }}
        className="gap-4 rounded-b-xl bg-brand-20 pb-6 dark:bg-brand-90"
      >
        <View className="h-14 flex-row items-center justify-between px-4">
          <View className="flex-row gap-4">
            <AvatarWithLetter />
            <H1>Home</H1>
          </View>
          <TouchableOpacity
            className="relative size-8 items-center justify-center rounded-full border border-brand-70 dark:border-brand-40"
            onPress={() => router.push(`/notifications`)}
          >
            <Feather name="bell" size={16} color={colors.brand[40]} />
            {hasNotifs && (
              <View className="absolute right-0 top-0 size-2 rounded-full bg-red-50" />
            )}
          </TouchableOpacity>
        </View>
        <List
          data={combinedSessionAndTrendingList}
          estimatedItemSize={12}
          horizontal
          showsHorizontalScrollIndicator={false}
          renderItem={({ item, index }) => {
            if ('dj' in item) {
              return (
                <LiveUserAvatar
                  username={item.dj.username}
                  avatar={item.dj.profileImageUrl}
                  className={cn('ml-0', index === 0 && 'ml-4')}
                  onPress={() => handleJoinLiveSession(item.id)}
                  hideStatusPill={item.status !== 'LIVE'}
                />
              );
            } else {
              return (
                <LiveUserAvatar
                  username={item.name}
                  onPress={() => router.push(`/users/${item.id}`)}
                  avatar={item.avatar}
                  className={cn('ml-0', index === 0 && 'ml-4')}
                  hideStatusPill
                />
              );
            }
          }}
          ItemSeparatorComponent={() => <View className="size-4" />}
          keyExtractor={(item) => String(item.id)}
        />
      </View>
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View
          className="flex-1"
          style={{
            marginBottom: TAB_HEIGHT + insets.bottom,
          }}
        >
          <View className="gap-4 py-4">
            <View className="gap-4 px-4">
              <H5>Happening nearby</H5>
              <View className="relative h-[203px] overflow-hidden rounded-lg">
                {currentCoordinate.lat && currentCoordinate.lng ? (
                  <MapView
                    initialRegion={{
                      latitude: currentCoordinate.lat,
                      longitude: currentCoordinate.lng,
                      latitudeDelta: locationAccessible ? 0.02 : 10,
                      longitudeDelta: locationAccessible ? 0.02 : 10,
                    }}
                    style={{ width: '100%', height: '100%' }}
                    showsPointsOfInterest={false}
                    showsBuildings={false}
                    mapType={
                      Platform.OS === 'ios' ? 'mutedStandard' : 'standard'
                    }
                    showsTraffic={false}
                    userInterfaceStyle={isDark ? 'dark' : 'light'}
                    customMapStyle={
                      isDark && Platform.OS === 'android' ? mapCustomStyle : []
                    }
                  />
                ) : (
                  <Skeleton className="h-56 w-full rounded-lg" />
                )}

                <Pressable
                  className="absolute inset-0 z-10 items-center justify-center"
                  onPress={() => {
                    router.push('/explore/map-view');
                  }}
                >
                  <View className="relative items-center justify-center">
                    <LottieView
                      autoPlay
                      style={{ width: 150, height: 150 }}
                      source={require('~/assets/animations/circle.json')}
                    />
                    <Image
                      className="absolute size-20"
                      source={require('~/assets/images/popla.png')}
                      contentFit="cover"
                    />
                  </View>
                </Pressable>
              </View>
            </View>
            {!hideTodo && hasTodo && (
              <View className="gap-y-4 py-2 pl-4">
                <View className="flex-row items-center justify-between pr-4">
                  <H5 className="">To Do</H5>

                  <TouchableOpacity onPress={() => setHideTodo(true)}>
                    <H5 className="text-fg-link dark:text-fg-link">Hide</H5>
                  </TouchableOpacity>
                </View>

                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerClassName="gap-x-4"
                >
                  {cards.map((card, index) => (
                    <HomeCompletionCard
                      key={index}
                      onPress={() =>
                        router.push(`/profile/completion/${card.id}`)
                      }
                      title={card.title}
                      imageSource={card.imageSource}
                      className={index === cards.length - 1 ? 'pr-4' : ''}
                    />
                  ))}
                </ScrollView>
              </View>
            )}

            {djSessions.length > 0 && (
              <View className="gap-4">
                <H5 className="px-4">Join now</H5>
                <List
                  data={djSessions}
                  estimatedItemSize={12}
                  key={djSessions?.length === 0 ? `empty-list` : 'normal-list'}
                  horizontal={djSessions.length > 0 ? true : false}
                  showsHorizontalScrollIndicator={false}
                  ListEmptyComponent={<EmptyState />}
                  renderItem={({ item, index }) => (
                    <LiveUserCard
                      status="LIVE"
                      username={item.dj.username}
                      avatar={item.dj.profileImageUrl}
                      bgImage={
                        index % 2 === 0
                          ? require('~/assets/gradient-bg/gradient-card-bg.png')
                          : require('~/assets/gradient-bg/gradient-card-bg-2.png')
                      }
                      className={cn('ml-0', index === 0 && 'ml-4')}
                      onPress={() => handleJoinLiveSession(item.id)}
                    />
                  )}
                  ItemSeparatorComponent={() => <View className="size-4" />}
                  keyExtractor={(item) => String(item.id)}
                />
              </View>
            )}

            <View className="gap-4">
              <H5 className="px-4">Featured events</H5>
              {eventData && eventData.events.length > 0 ? (
                <EventCarousel events={eventData?.events} />
              ) : (
                <EmptyState />
              )}
            </View>
          </View>
        </View>
      </ScrollView>
      {user.role === 'CREATOR' && <FloatingActionPopover />}
    </View>
  );
}
