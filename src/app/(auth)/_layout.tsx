import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';

import { AccountSetupProvider, useAccountSetupForm } from '@/lib';

export default function AccountCreationLayout() {
  const {
    formMethods,
    usernameRefinement,
    isValidatingUsername,
    setIsValidatingUsername,
  } = useAccountSetupForm();

  return (
    <AccountSetupProvider
      value={{
        usernameRefinement,
        isValidatingUsername,
        setIsValidatingUsername,
      }}
    >
      <FormProvider {...formMethods}>
        <Stack>
          <Stack.Screen name="register" options={{ headerShown: false }} />
        </Stack>
      </FormProvider>
    </AccountSetupProvider>
  );
}
