import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { View } from '@/components/ui';
import { type FormType as AccountSetupFormType, USER_ROLE_CARDS } from '@/lib';

export default function AccountSelection() {
  const { watch, setValue } = useFormContext<AccountSetupFormType>();

  return (
    <View className="mt-4 gap-4">
      {USER_ROLE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('role') === card.id}
          onPress={() => setValue('role', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </View>
  );
}
