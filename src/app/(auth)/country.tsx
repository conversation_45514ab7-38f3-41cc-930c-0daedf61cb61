import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  colors,
  Image,
  Modal,
  Pressable,
  Text,
  useModal,
  View,
} from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  SUPPORTED_COUNTRIES,
} from '@/lib';

export default function Country() {
  const { watch, setValue } = useFormContext<AccountSetupFormType>();

  const countryModal = useModal();

  const selectedCountry = watch('country');

  const countryLabel = SUPPORTED_COUNTRIES.find(
    (country) => country.value === selectedCountry
  )?.label;

  const onSelect = (selectedCountry: string) => {
    setValue('country', selectedCountry as string);
  };

  return (
    <View className="gap-4">
      <View>
        <Pressable
          testID="country-select"
          className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
          onPress={countryModal.present}
        >
          <View className="flex-row items-center gap-x-1">
            {selectedCountry && (
              <Image
                source={{
                  uri: `https://flagcdn.com/128x96/${
                    SUPPORTED_COUNTRIES.find((c) => c.value === selectedCountry)
                      ?.code
                  }.png`,
                }}
                className="size-6 rounded-xl"
              />
            )}
            <Text
              className={
                countryLabel
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {countryLabel ?? 'Select Country'}
            </Text>
          </View>
          <Ionicons name="chevron-down" size={24} color={colors.grey[70]} />
        </Pressable>
      </View>

      <Modal ref={countryModal.ref} snapPoints={['30%']}>
        <View className="px-4 pb-4">
          {SUPPORTED_COUNTRIES.map((country) => (
            <Pressable
              key={country.value}
              className="flex-row items-center border-b border-neutral-300 dark:border-neutral-700"
              onPress={() => {
                countryModal.dismiss();
                onSelect(country.value);
              }}
            >
              <View className="p-2">
                <Image
                  source={{
                    uri: `https://flagcdn.com/128x96/${country.code}.png`,
                  }}
                  className="size-10 gap-2 rounded-xl"
                />
              </View>

              <Text className="flex-1 dark:text-neutral-100">
                {country.label}
              </Text>
              {selectedCountry === country.value && (
                <Ionicons
                  name="checkmark"
                  size={20}
                  color={colors.brand['60']}
                />
              )}
            </Pressable>
          ))}
        </View>
      </Modal>
    </View>
  );
}
