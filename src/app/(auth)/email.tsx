import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  colors,
  ControlledInput,
  MessageFilledIcon,
  MessageIcon,
} from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  useFieldBlurAndFilled,
} from '@/lib';

export default function Email() {
  const { control } = useFormContext<AccountSetupFormType>();
  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<AccountSetupFormType>(['email']);

  return (
    <ControlledInput
      testID="email-input"
      control={control}
      name="email"
      label="Email"
      handleFieldBlur={() => handleFieldBlur('email')}
      handleFieldUnBlur={() => handleFieldUnBlur('email')}
      icon={
        fieldStates.email.isFilledAndBlurred ? (
          <MessageFilledIcon color={colors.brand['60']} />
        ) : (
          <MessageIcon color={colors.brand['60']} />
        )
      }
    />
  );
}
