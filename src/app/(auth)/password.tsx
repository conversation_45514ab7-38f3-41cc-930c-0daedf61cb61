import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  colors,
  ControlledInput,
  PasswordFilledIcon,
  PasswordIcon,
  Small,
  View,
} from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  PASSWORD_RESTRICTIONS,
  useFieldBlurAndFilled,
} from '@/lib';

export default function Password() {
  const { control } = useFormContext<AccountSetupFormType>();
  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<AccountSetupFormType>([
      'password',
      'confirmPassword',
    ]);

  return (
    <>
      <View className="gap-0.5">
        <ControlledInput
          testID="password-input"
          control={control}
          name="password"
          label="Password"
          handleFieldBlur={() => handleFieldBlur('password')}
          handleFieldUnBlur={() => handleFieldUnBlur('password')}
          isPassword
          icon={
            fieldStates.password.isFilledAndBlurred ? (
              <PasswordFilledIcon color={colors.brand['60']} />
            ) : (
              <PasswordIcon color={colors.brand['60']} />
            )
          }
        />
        {!fieldStates.password.isValid && (
          <View>
            {PASSWORD_RESTRICTIONS.map((restriction, index) => (
              <View
                key={index}
                style={{ flexDirection: 'row', marginBottom: 8 }}
              >
                <Small className="text-grey-50 dark:text-grey-60">
                  {'\u2022' + ' '}
                </Small>
                <Small className="text-grey-50 dark:text-grey-60">
                  {restriction}
                </Small>
              </View>
            ))}
          </View>
        )}
      </View>
      <ControlledInput
        testID="confirm-password-input"
        control={control}
        name="confirmPassword"
        label="Confirm password"
        handleFieldBlur={() => handleFieldBlur('confirmPassword')}
        handleFieldUnBlur={() => handleFieldUnBlur('confirmPassword')}
        isPassword
        icon={
          fieldStates.confirmPassword.isFilledAndBlurred ? (
            <PasswordFilledIcon color={colors.brand['60']} />
          ) : (
            <PasswordIcon color={colors.brand['60']} />
          )
        }
      />
    </>
  );
}
