import React from 'react';
import { type Control, useFormContext } from 'react-hook-form';
import { ActivityIndicator, TouchableWithoutFeedback } from 'react-native';
import { toast } from 'sonner-native';
import * as z from 'zod';

import { useRequestOtp } from '@/api/auth';
import {
  colors,
  ControlledInputWithoutContext,
  H3,
  P,
  View,
} from '@/components/ui';
import { type FormType as AccountSetupFormType } from '@/lib';

interface VerifyEmailProps {
  control: Control<FormType>;
}

export const OtpSchema = z.object({
  code: z
    .string({
      required_error: 'Verification code is required',
    })
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, { message: 'Code must contain only numbers' }),
});

export type FormType = z.infer<typeof OtpSchema>;

export default function VerifyEmail({ control }: VerifyEmailProps) {
  const { getValues } = useFormContext<AccountSetupFormType>();
  const email = getValues('email');
  const { mutate: requestOtp, isPending: isRequesting } = useRequestOtp();

  const onResendOtp = () =>
    requestOtp(
      { email },
      {
        onSuccess: () => {
          toast.success('OTP resent successfully', {
            position: 'top-center',
            richColors: true,
          });
        },
        onError: (error) => toast.error(error.message),
      }
    );

  return (
    <View className="flex-1 justify-start">
      <View className="gap-[17px]">
        <H3>Ok, check your inbox!{'\n'}we sent you a verification code.</H3>
        <ControlledInputWithoutContext
          testID="verification-code-input"
          control={control}
          name="code"
          label="Verification code"
        />
        <P className="text-grey-50 dark:text-grey-60">
          Didn’t receive it?{' '}
          <TouchableWithoutFeedback onPress={onResendOtp}>
            {isRequesting ? (
              <ActivityIndicator
                size="small"
                className="mt-5"
                color={colors.brand['60']}
              />
            ) : (
              <P className="text-brand-60 dark:text-brand-60">Tap to resend</P>
            )}
          </TouchableWithoutFeedback>
        </P>
      </View>
    </View>
  );
}
