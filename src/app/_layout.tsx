import '../../global.css';

import { Env } from '@env';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { getMessaging } from '@react-native-firebase/messaging';
import { ThemeProvider } from '@react-navigation/native';
import { PortalHost } from '@rn-primitives/portal';
import { useQueryClient } from '@tanstack/react-query';
import { Stack, useRouter } from 'expo-router';
import { usePathname } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import React from 'react';
import {
  AppState,
  type AppStateStatus,
  LogBox,
  StyleSheet,
} from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { PaystackProvider } from 'react-native-paystack-webview';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';
import { toast, Toaster } from 'sonner-native';

import { APIProvider, createOrUpdateFirebaseUser } from '@/api';
import {
  type CreateLiveSessionResponse,
  type DJLiveSession,
  type GetSessionViewersResponse,
  type LiveSessionInfoResponse,
  type QuesterSessionInfo,
  useGetLiveSession,
  useGetLiveSessionQueues,
  useGetLiveSessionViewers,
  type UserLiveSession,
} from '@/api/session';
import {
  hydrateAuth,
  loadSelectedTheme,
  ProfileImageProvider,
  SocketProvider,
  useAuth,
  useLoggedInUser,
  useSocket,
} from '@/lib';
import { AppSessionProvider, LocationProvider } from '@/lib';
import { useInitializeFcmToken } from '@/lib/hooks/use-initialize-fcm-token';
import { useSession } from '@/lib/session';
import { removeLiveSession } from '@/lib/session/utils';
import { useThemeConfig } from '@/lib/use-theme-config';
import { type NotificationExtraData, type TypedRemoteMessage } from '@/types';
import { type QueryKey } from '@/types/query';

import {
  type NotificationData,
  updateNotificationList,
  useNotificationStore,
} from '../lib/notifications';

// export { ErrorBoundary } from 'expo-router';

LogBox.ignoreLogs([
  'Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead.',
]);

// This is the default configuration
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false, // Reanimated runs in strict mode by default
});

export const unstable_settings = {
  initialRouteName: '(app)',
};

hydrateAuth();
loadSelectedTheme();
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();
// Set the animation options. This is optional.
SplashScreen.setOptions({
  duration: 500,
  fade: true,
});

getMessaging().setBackgroundMessageHandler(
  async (remoteMessage: TypedRemoteMessage<NotificationExtraData>) => {
    console.log('Message handled in the background!', remoteMessage);

    const today = new Date();
    const notificationData = {
      id: today.getTime(),
      text: `${remoteMessage.notification?.title ?? 'New Message'} - ${
        remoteMessage.notification?.body
      }`,
      imgUrl:
        remoteMessage.data?.userAvatar ||
        remoteMessage.notification?.image ||
        remoteMessage.notification?.android?.imageUrl ||
        remoteMessage.notification?.icon,
      date: today,
      ...remoteMessage.data,
    } as NotificationData;

    updateNotificationList(notificationData);
  }
);

export default function RootLayout() {
  return (
    <Providers>
      <MainStack />
    </Providers>
  );
}

function Providers({ children }: { children: React.ReactNode }) {
  const theme = useThemeConfig();
  const user = useAuth.use.user();
  return (
    <GestureHandlerRootView
      style={styles.container}
      className={theme.dark ? 'dark' : 'undefined'}
    >
      <KeyboardProvider>
        <ThemeProvider value={theme}>
          <APIProvider>
            <ProfileImageProvider>
              <LocationProvider user={user ? user : undefined}>
                <AppSessionProvider>
                  {/* Wrap in auth provider? */}
                  <SocketProvider>
                    <PaystackProvider
                      publicKey={Env.PAYSTACK_PUBLIC_KEY}
                      currency="NGN" // Default
                      defaultChannels={[
                        'apple_pay',
                        'bank',
                        'bank_transfer',
                        'card',
                        'eft',
                        'mobile_money',
                        'qr',
                        'ussd',
                      ]}
                    >
                      <BottomSheetModalProvider>
                        {children}
                        <Toaster />
                      </BottomSheetModalProvider>
                      <PortalHost />
                    </PaystackProvider>
                  </SocketProvider>
                </AppSessionProvider>
              </LocationProvider>
            </ProfileImageProvider>
          </APIProvider>
          <FlashMessage position="top" />
        </ThemeProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
}

function MainStack() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const currentPathname = usePathname();
  const { data: currentUser } = useLoggedInUser();
  const joinedSessionId = useSession.use.joinedSessionId();
  const resetDjSession = useSession.use.resetDjSession();
  const updateNotificationList =
    useNotificationStore.use.updateNotificationList();

  const { socket } = useSocket();

  const joinedSessionIdRef = React.useRef(joinedSessionId);
  const pathnameRef = React.useRef(currentPathname);

  React.useEffect(() => {
    pathnameRef.current = currentPathname;
  }, [currentPathname]);

  React.useEffect(() => {
    joinedSessionIdRef.current = joinedSessionId;
  }, [joinedSessionId]);

  useInitializeFcmToken();

  React.useEffect(() => {
    if (!currentUser) return;
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        await createOrUpdateFirebaseUser(currentUser, 'online');
      } else if (nextAppState.match(/inactive|background/)) {
        await createOrUpdateFirebaseUser(currentUser, 'offline');
      }
    };
    AppState.addEventListener('change', handleAppStateChange);
  }, [currentUser]);

  React.useEffect(() => {
    const unsubscribe = getMessaging().onMessage(
      async (remoteMessage: TypedRemoteMessage<NotificationExtraData>) => {
        const isInChatScreen = pathnameRef.current?.includes('/chats/');
        const isInCurrentUserChat =
          isInChatScreen &&
          pathnameRef.current?.includes(remoteMessage.data?.userId || '');

        if (!isInCurrentUserChat) {
          toast.info(remoteMessage.notification?.title || 'New Message', {
            description: remoteMessage.notification?.body,
          });
        }

        const today = new Date();
        const notificationData = {
          id: today.getTime(),
          text: `${remoteMessage.notification?.title ?? 'New Message'} - ${
            remoteMessage.notification?.body
          }`,
          imgUrl:
            remoteMessage.notification?.image ||
            remoteMessage.notification?.android?.imageUrl ||
            remoteMessage.notification?.icon,
          date: today,
          ...remoteMessage.data,
        } as NotificationData;

        !remoteMessage.data?.excludeInSavedNotifications &&
          updateNotificationList(notificationData);
      }
    );

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    if (!socket || !currentUser?.id) return;

    const onConnected = () => console.log('socket connected');
    const onDisconnected = () => console.log('socket disconnected');
    const onMyQueueEvent = async (sessionDetails: UserLiveSession) => {
      const sessionId = joinedSessionIdRef.current;
      const queryKey = useGetLiveSessionQueues.getKey({
        sessionId,
      }) as QueryKey;
      queryClient.setQueryData(queryKey, sessionDetails);
    };
    const onDjQueueEvent = async (sessionDetails: DJLiveSession) => {
      const sessionId = joinedSessionIdRef.current;
      if (currentUser.id === sessionDetails.creatorId) {
        const queryKey = useGetLiveSessionQueues.getKey({
          sessionId,
        }) as QueryKey;
        queryClient.setQueryData(queryKey, sessionDetails);
      }
    };
    const onJoinSession = async (updatedSessionInfo: QuesterSessionInfo) => {
      console.log(
        '🚀 ~ onJoinSession ~ updatedSessionInfo:',
        updatedSessionInfo
      );
      const sessionId = joinedSessionIdRef.current;
      const queryKey = useGetLiveSession.getKey({
        sessionId,
        userCurrency: 'NGN',
      }) as QueryKey;
      const viewersQueryKey = useGetLiveSessionViewers.getKey({
        sessionId,
      }) as QueryKey;

      const updater = (
        sessionData: LiveSessionInfoResponse
      ): LiveSessionInfoResponse =>
        sessionData
          ? {
              ...sessionData,
              questersOnSession: updatedSessionInfo.questers.count,
              profileImageUrls: updatedSessionInfo.questers.profileImageUrls,
              users: updatedSessionInfo.questers.users,
            }
          : sessionData;

      const viewersUpdater = (
        viewers: GetSessionViewersResponse[]
      ): GetSessionViewersResponse[] => {
        if (!viewers || !Array.isArray(viewers)) return viewers;

        const newViewers: GetSessionViewersResponse[] =
          updatedSessionInfo.questers.users.map((user) => {
            const existingViewer = viewers.find((v) => v.questerId === user.id);

            return {
              id: existingViewer?.id || `viewer-${user.id}`,
              questerId: user.id,
              djSessionId: sessionId,
              createdAt: existingViewer?.createdAt || new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              quester: {
                id: user.id,
                username: user.username,
                profileImageUrl: user.profileImageUrl || '',
                fullName: user.fullName,
              },
            };
          });

        return newViewers;
      };

      queryClient.setQueryData(queryKey, updater);
      queryClient.setQueryData(viewersQueryKey, viewersUpdater);
    };

    const onSessionStarted = async ({
      session,
    }: {
      session: CreateLiveSessionResponse;
    }) => {
      const isSessionOwner = session.djId === currentUser.id;
      console.log('socket - session started', session.dj.username);
      if (!isSessionOwner) {
        await queryClient.invalidateQueries({ queryKey: ['getHomeData'] });
        await queryClient.invalidateQueries({ queryKey: ['getSessions'] });

        toast.info(`Session Started by ${session.dj.username}`);
      }
    };
    const onSessionCameraToggle = ({
      isCameraHidden,
    }: {
      isCameraHidden: boolean;
      sessionCreatorId: string;
    }) => {
      const sessionId = joinedSessionIdRef.current;
      const queryKey = useGetLiveSession.getKey({
        sessionId,
        userCurrency: 'NGN',
      }) as QueryKey;

      queryClient.setQueryData(queryKey, (prev: LiveSessionInfoResponse) => {
        // if (!prev || prev.djSession.djId !== sessionCreatorId) return prev;

        return {
          ...prev,
          djSession: {
            ...prev.djSession,
            cameraHidden: isCameraHidden,
          },
        };
      });
    };
    const onSessionLeft = () => {};
    const onSessionEnded = async ({ sessionId }: { sessionId: string }) => {
      await queryClient.invalidateQueries({ queryKey: ['getHomeData'] });
      await queryClient.invalidateQueries({ queryKey: ['getSessions'] });
      removeLiveSession();
      resetDjSession();
      const isInEndedSession = pathnameRef.current?.includes(sessionId);

      if (isInEndedSession) {
        toast.info('Session Ended');
        router.dismissTo('/');
      }
    };

    socket.on('connect', onConnected);
    socket.on('disconnect', onDisconnected);

    socket.on('MY_QUEUE', onMyQueueEvent);

    socket.on('DJ_QUEUES', onDjQueueEvent);

    socket.on('QUESTERS', onJoinSession);

    socket.on('SESSION_STARTED', onSessionStarted);
    socket.on('CAMERA_CHANGE', onSessionCameraToggle);

    socket.on('LEAVE_SESSION', onSessionLeft);
    socket.on('SESSION_ENDED', onSessionEnded);

    return () => {
      socket.disconnect();
      socket.off('connect', onConnected);
      socket.off('disconnect', onDisconnected);

      socket.off('MY_QUEUE', onMyQueueEvent);

      socket.off('DJ_QUEUES', onDjQueueEvent);

      socket.off('QUESTERS', onJoinSession);

      socket.off('SESSION_STARTED', onSessionStarted);
      socket.off('CAMERA_CHANGE', onSessionCameraToggle);
      socket.off('LEAVE_SESSION', onSessionEnded);
      socket.off('SESSION_ENDED', onSessionEnded);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [socket, currentUser?.id]);

  return (
    <Stack>
      <Stack.Screen name="onboarding" options={{ headerShown: false }} />
      <Stack.Screen name="login" options={{ headerShown: false }} />
      <Stack.Screen name="(auth)" options={{ headerShown: false }} />
      <Stack.Screen name="forgot-password" options={{ headerShown: false }} />
      <Stack.Screen
        name="email-sent"
        options={{
          headerShown: false,
          presentation: 'containedModal',
        }}
      />
      <Stack.Screen name="(app)" options={{ headerShown: false }} />
      <Stack.Screen
        name="profile"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="chats"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="events"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="explore"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="notifications"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="session"
        options={{
          headerShown: false,
          animation: 'ios_from_right',
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="users"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
      <Stack.Screen
        name="webview"
        options={{ headerShown: false, animation: 'ios_from_right' }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
