/* eslint-disable unicorn/filename-case */
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import ChatMessages from '@/components/chats/ui';
import ChatScreenMoreDropdown from '@/components/chats/ui/more-dropdown';
import { BlockedView } from '@/components/chats/views/blocked';
import { RequestHintView } from '@/components/chats/views/request-hint';
import { Back } from '@/components/common/back';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import LoadingScreen from '@/components/loading';
import {
  colors,
  FocusAwareStatusBar,
  Image,
  LgBoldLabel,
  OnlineIndicator,
  SafeAreaView,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { useAccountFavourite, usePrivateChat } from '@/lib';

export default function ChatScreen() {
  const { username, userAvatar, userId } = useLocalSearchParams<{
    chatRoomId: string;
    username: string;
    userId: string;
    userAvatar: string;
  }>();
  const { push, back } = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const {
    loading,
    isBlockedByOtherUser,
    hasBlockedOtherUser,
    handleBlockUser,
    handleUnblockUser,
    messages,
    chatUser,
    onSend,
    handleTypingStatus,
    otherUserTyping,
    replyToMessage,
    setReplyMessage,
    requestAccepted,
    isChatBlocked,
    deleteChat,
    hasPendingRequest,
  } = usePrivateChat(userId, username, userAvatar);

  const handleInputTextChanged = React.useCallback(
    (text: string) => {
      handleTypingStatus(text.length > 0);
    },
    [handleTypingStatus]
  );

  const [confirmVisible, setConfirmVisible] = React.useState(false);
  const [confirmDeleteVisible, setConfirmDeleteVisible] = React.useState(false);

  const { favourited, toggleFavourite } = useAccountFavourite(userId);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="relative h-16 flex-row items-center justify-between px-2">
        <View className="flex-row items-center gap-[22px]">
          <Back />
          <TouchableOpacity
            className="relative"
            onPress={() =>
              push({ pathname: '/users/[id]', params: { id: userId } })
            }
          >
            <Image
              className="size-10 rounded-full"
              source={userAvatar || require('~/assets/images/avatar.png')}
              priority="high"
            />
            {!chatUser?.hideOnlineStatus && chatUser?.status === 'online' && (
              <View className="absolute -bottom-1 -right-1 size-5 items-center justify-center rounded-full bg-white dark:bg-grey-100">
                <OnlineIndicator
                  color={isDark ? colors.green[50] : colors.green[60]}
                  strokeColor={isDark ? colors.grey[100] : colors.white}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
        <LgBoldLabel className="absolute left-1/2 -translate-x-1/2">
          {username}
        </LgBoldLabel>
        <ChatScreenMoreDropdown
          isBlocked={hasBlockedOtherUser}
          disabled={
            isBlockedByOtherUser || (!requestAccepted && !hasPendingRequest)
          }
          isUserFavourited={favourited}
          onValueChange={(value) => {
            switch (value) {
              case 'block':
                setConfirmVisible(true);
                break;
              case 'toggle-fav':
                toggleFavourite();
                break;
              case 'report':
                push({
                  pathname: '/profile/report-problem',
                });
                break;
              case 'delete':
                setConfirmDeleteVisible(true);
                break;
              default:
                break;
            }
          }}
          itemClassName="native:px-4 native:py-2.5 justify-between h-11"
        />
      </View>
      {!requestAccepted && !hasPendingRequest && (
        <RequestHintView userName={username} />
      )}
      {isChatBlocked ? (
        <BlockedView />
      ) : (
        <ChatMessages
          messages={messages}
          onSend={onSend}
          isBlocked={isChatBlocked}
          hasPendingRequest={hasPendingRequest}
          userId={userId}
          userName={username}
          userAvatar={userAvatar}
          handlePickImage={() => {}}
          onInputTextChanged={handleInputTextChanged}
          isTyping={otherUserTyping}
          replyMessage={replyToMessage}
          setReplyMessage={setReplyMessage}
        />
      )}
      <ConfirmationDialog
        visible={confirmVisible}
        message={`Are you sure you want to ${hasBlockedOtherUser ? 'unblock' : 'block'} this account?`}
        onCancel={() => setConfirmVisible(false)}
        onConfirm={() => {
          if (hasBlockedOtherUser) {
            handleUnblockUser();
          } else {
            handleBlockUser();
          }
          setConfirmVisible(false);
        }}
      />
      <ConfirmationDialog
        visible={confirmDeleteVisible}
        message="Are you sure you want to delete this chat"
        onCancel={() => setConfirmDeleteVisible(false)}
        onConfirm={async () => {
          setConfirmDeleteVisible(false);
          await deleteChat();
          back();
        }}
      />
    </SafeAreaView>
  );
}
