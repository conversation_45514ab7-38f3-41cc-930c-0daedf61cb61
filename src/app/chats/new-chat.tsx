import * as React from 'react';

import { EmptyState } from '@/components/chats/list/empty';
import { ListLoadingComponent } from '@/components/chats/list/loading';
import { SearchListItem } from '@/components/chats/list/search-list-item';
import { BareLayout } from '@/components/layouts';
import { EmptyState as AppEmptyState } from '@/components/ui';
import { List, View } from '@/components/ui';
import { useSearchUsers } from '@/lib';

export default function StartNewChat() {
  const { filteredUsers, queryLoading, debouncedText } = useSearchUsers('', {
    mode: 'newChat',
  });
  // TODO: Include a search input
  return (
    <BareLayout title="Start new chat" contentClassName="py-0">
      <List
        data={filteredUsers}
        estimatedItemSize={12}
        showsVerticalScrollIndicator={false}
        renderItem={({ item: user, index }) => (
          <SearchListItem key={index} user={user} selectUser={() => {}} />
        )}
        ListEmptyComponent={
          debouncedText !== '' ? <EmptyState type="users" /> : <AppEmptyState />
        }
        ListFooterComponent={
          <ListLoadingComponent loading={queryLoading || false} />
        }
        keyExtractor={(item) => item.id}
        ItemSeparatorComponent={() => <View className="size-4 " />}
      />
    </BareLayout>
  );
}
