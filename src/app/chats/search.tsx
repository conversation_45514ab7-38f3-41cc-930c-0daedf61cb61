import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import React from 'react';
import { useForm } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import * as z from 'zod';

import { EmptyState } from '@/components/chats/list/empty';
import { ListLoadingComponent } from '@/components/chats/list/loading';
import { SearchItem } from '@/components/chats/list/search-item';
import { SearchListItem } from '@/components/chats/list/search-list-item';
import { Back } from '@/components/common/back';
import { ChatsLoading } from '@/components/loading/chats';
import { SearchUsersLoading } from '@/components/loading/search-users';
import {
  ControlledInputWithoutContext,
  FocusAwareStatusBar,
  List,
  Pressable,
  SafeAreaView,
  Small,
  SmBoldLabel,
  View,
  WIDTH,
} from '@/components/ui';
import { useSearchUsers } from '@/lib';

const schema = z.object({
  search: z.string(),
});
export type FormType = z.infer<typeof schema>;

export default function SearchChats() {
  const { search } = useLocalSearchParams<{
    search: string;
  }>();
  const { control, watch } = useForm<FormType>({
    defaultValues: { search },
  });
  const searchQuery = watch('search');
  const {
    filteredUsers,
    queryLoading,
    loading,
    debouncedText,
    recentSearches,
    selectUser,
    clearRecentSearches,
  } = useSearchUsers(searchQuery);

  const availableWidth = WIDTH - 32; // px-4
  const calculatedColumns = Math.floor(availableWidth / 74);

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <View className="flex-row items-center gap-4 px-4">
          <Back />
          <ControlledInputWithoutContext
            testID="search-input"
            control={control}
            name="search"
            label="Search"
            icon={<Ionicons name="search" size={20} color="white" />}
            inputClassName="rounded-full"
            containerClassName="flex-1"
            iconClassName="top-4 left-4"
          />
        </View>
        {loading ? (
          <>
            {debouncedText ? (
              <ChatsLoading />
            ) : (
              <SearchUsersLoading numColumns={calculatedColumns || 4} />
            )}
          </>
        ) : (
          <View className="flex-1">
            {/* Recent Searches */}
            <View className="flex-1 gap-y-4 p-4">
              {debouncedText ? (
                <List
                  data={filteredUsers}
                  estimatedItemSize={12}
                  renderItem={({ item: user, index }) => (
                    <SearchListItem
                      key={index}
                      user={user}
                      selectUser={selectUser}
                    />
                  )}
                  ListEmptyComponent={<EmptyState type="users" />}
                  ListFooterComponent={
                    <ListLoadingComponent loading={queryLoading || false} />
                  }
                  keyExtractor={(item) => item.id}
                  ItemSeparatorComponent={() => <View className="size-4 " />}
                />
              ) : (
                <>
                  <View className="flex-row justify-between">
                    <SmBoldLabel>Recent search</SmBoldLabel>
                    <Pressable onPress={clearRecentSearches}>
                      <Small className="text-fg-link dark:text-fg-link">
                        Clear
                      </Small>
                    </Pressable>
                  </View>
                  <List
                    data={recentSearches}
                    estimatedItemSize={10}
                    renderItem={({ item: user, index }) => (
                      <SearchItem key={index} user={user} />
                    )}
                    keyExtractor={(item) => item.id}
                    ItemSeparatorComponent={() => <View className="size-4 " />}
                    ListEmptyComponent={<EmptyState type="recent-users" />}
                    numColumns={calculatedColumns || 4}
                  />
                </>
              )}
            </View>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
