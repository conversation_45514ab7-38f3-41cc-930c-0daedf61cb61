import { useRouter } from 'expo-router';
import React from 'react';
import { View } from 'react-native';

import EmailCheckModal from '@/components/modals/email-sent';
import { FocusAwareStatusBar } from '@/components/ui';

export default function EmailSent() {
  const router = useRouter();
  const [modalVisible, setModalVisible] = React.useState(true);

  const handleCloseModal = () => {
    setModalVisible(false);
    router.back();
  };
  return (
    <View className=" flex-1" style={{ backgroundColor: '#00000045' }}>
      <FocusAwareStatusBar hidden />
      <EmailCheckModal visible={modalVisible} onClose={handleCloseModal} />
    </View>
  );
}
