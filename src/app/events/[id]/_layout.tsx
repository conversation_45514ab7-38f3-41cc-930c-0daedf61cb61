import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';

import { PurchaseTicketProvider } from '@/lib/contexts/purchase-ticket-context';
import { usePurchaseEventTicket } from '@/lib/hooks/use-purchase-ticket';

export default function EventLayout() {
  const {
    event,
    categories,
    isEventLoading,
    selectedTickets,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    hasSelectedTickets,
    formMethods,
    resetForm,
    calculateTotalCost,
    getTotalQuantity,
    eventId,
    editType,
    setEditType,
    activeDiscount,
    setActiveDiscount,
  } = usePurchaseEventTicket();

  return (
    <PurchaseTicketProvider
      value={{
        event,
        categories,
        isEventLoading,
        selectedTickets,
        handleTicketQuantityChange,
        getSelectedTicketsArray,
        hasSelectedTickets,
        resetForm,
        calculateTotalCost,
        getTotalQuantity,
        eventId,
        setEditType,
        editType,
        activeDiscount,
        setActiveDiscount,
      }}
    >
      <FormProvider {...formMethods}>
        <Stack>
          <Stack.Screen name="index" options={{ headerShown: false }} />
          <Stack.Screen name="discounts" options={{ headerShown: false }} />
          <Stack.Screen name="access" options={{ headerShown: false }} />
          <Stack.Screen name="tickets" options={{ headerShown: false }} />
          <Stack.Screen name="scan-ticket" options={{ headerShown: false }} />
          <Stack.Screen
            name="creation-success"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="message-attendees"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="analytics" options={{ headerShown: false }} />
          <Stack.Screen name="edit-ticket" options={{ headerShown: false }} />
        </Stack>
      </FormProvider>
    </PurchaseTicketProvider>
  );
}
