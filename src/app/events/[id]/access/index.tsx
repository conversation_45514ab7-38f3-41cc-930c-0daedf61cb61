import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import React from 'react';
import { toast } from 'sonner-native';

import {
  useCreateEventAccessCodes,
  useGetEventAccessCodes,
} from '@/api/events';
import Counter from '@/components/common/counter';
import { AccessCodeListItem } from '@/components/events/access/list-item';
import { BareLayout } from '@/components/layouts/bare-layout';
import LoadingScreen from '@/components/loading';
import { EmptyState } from '@/components/ui';
import {
  Button,
  ChevronLeft,
  H5,
  LgBoldLabel,
  List,
  Modal,
  Small,
  TouchableOpacity,
  useModal,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { cn } from '@/lib';

export default function CreateAccessCode() {
  const { id } = useLocalSearchParams<{
    id: string;
    slug: string;
    title: string;
  }>();
  const queryClient = useQueryClient();
  const modal = useModal();

  const { isPending: accessCodesPending, data: accessCodes } =
    useGetEventAccessCodes({
      variables: { eventId: id },
    });

  const { isPending: isGeneratingCodes, mutate: generateAccessCodes } =
    useCreateEventAccessCodes();

  const [selectedTab, setSelectedTab] = React.useState<'CODES' | 'USED'>(
    'CODES'
  );
  const [accessCodeNumber, setAccessCodeNumber] = React.useState(20);

  const { unusedCodes, usedCodes } = React.useMemo(() => {
    const unused = accessCodes?.filter((code) => !code.isUsed) ?? [];
    const used = accessCodes?.filter((code) => code.isUsed) ?? [];
    return { unusedCodes: unused, usedCodes: used };
  }, [accessCodes]);

  const dataToRender = selectedTab === 'CODES' ? unusedCodes : usedCodes;

  const handleGenerate = () => {
    generateAccessCodes(
      { eventId: id, quantity: accessCodeNumber },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ['getEventAccessCodes', { eventId: id }],
          });
          modal.dismiss();
          toast.success('Access codes generated Successfully');
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  if (accessCodesPending) return <LoadingScreen />;

  return (
    <BareLayout
      title="Access code"
      subTitle="Manage access to your event"
      contentClassName="py-0"
      footer={
        <Button
          testID="generate-access-code-button"
          label="Generate code"
          className="m-4"
          onPress={modal.present}
        />
      }
    >
      {/* Tab */}
      <View className="h-12 w-full flex-row items-center rounded-full bg-grey-10 p-0.5 dark:bg-grey-90">
        <TouchableOpacity
          className={cn(
            'text-grey-50 dark:text-grey-60 flex-1 items-center bg-transparent h-full justify-center',
            selectedTab === 'CODES' && 'bg-white dark:bg-grey-80 rounded-full'
          )}
          onPress={() => setSelectedTab('CODES')}
        >
          <XsBoldLabel
            className={cn(
              'text-grey-50 dark:text-grey-60',
              selectedTab === 'CODES' && 'text-brand-60 dark:text-brand-50'
            )}
          >
            Codes
          </XsBoldLabel>
        </TouchableOpacity>
        <TouchableOpacity
          className={cn(
            'text-fg-subtle-light dark:text-fg-subtle-dark flex-1 items-center bg-transparent h-full justify-center',
            selectedTab === 'USED' && 'bg-white dark:bg-grey-80 rounded-full'
          )}
          onPress={() => setSelectedTab('USED')}
        >
          <XsBoldLabel
            className={cn(
              'text-grey-50 dark:text-grey-60',
              selectedTab === 'USED' && 'text-brand-60 dark:text-brand-50'
            )}
          >
            Used codes
          </XsBoldLabel>
        </TouchableOpacity>
      </View>

      {/* Tab View */}
      <View className="flex-1">
        <List
          key={selectedTab}
          data={dataToRender}
          estimatedItemSize={74}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={<EmptyState />}
          renderItem={({ item }) => (
            <AccessCodeListItem key={item.id} {...item} />
          )}
          ItemSeparatorComponent={() => <View className="size-3" />}
          keyExtractor={(item) =>
            item?.id?.toString?.() ?? Math.random().toString()
          }
        />
      </View>

      {/* Generate access code modal */}
      <Modal index={0} ref={modal.ref} enableDynamicSizing>
        <BottomSheetView className="min-h-60">
          <View className="h-16 flex-row items-center gap-2 px-2">
            <ChevronLeft onPress={modal.dismiss} />
            <LgBoldLabel>Generate code</LgBoldLabel>
          </View>

          <View className="gap-6 p-4">
            <View className="flex-row items-center justify-between gap-2">
              <View className="gap-1">
                <H5>Enter number of code</H5>
                <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                  Create up to 20 codes each time
                </Small>
              </View>

              <Counter
                initialValue={20}
                minimum={1}
                maximum={20}
                onValueChange={(quantity) => setAccessCodeNumber(quantity)}
                className="border border-border-subtle-light dark:border-border-subtle-dark"
              />
            </View>

            <Button
              label="Create"
              onPress={handleGenerate}
              loading={isGeneratingCodes}
              disabled={isGeneratingCodes}
            />
          </View>
        </BottomSheetView>
      </Modal>
    </BareLayout>
  );
}
