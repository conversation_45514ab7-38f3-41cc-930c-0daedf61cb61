import dayjs from 'dayjs';
import * as React from 'react';

import { Back } from '@/components/common/back';
import {
  AnalyticsCard,
  CategoryLegend,
  ConversionRatePieChart,
  MetricDisplay,
  TicketSalesChart,
} from '@/components/events';
import {
  FocusAwareStatusBar,
  LgBoldLabel,
  SafeAreaView,
  ScrollView,
  View,
} from '@/components/ui';
import * as lib from '@/lib';

export default function EventAnalytics() {
  const { event } = lib.usePurchaseTicketContext();

  const {
    barData,
    ticketCategories: ticketCategoriess,
    analyticsMetrics: { totalSold, totalTicketQuantity, salesGrowth },
  } = lib.useAnalyticsData({
    event,
    ticketCategories: event?.ticketCategories,
    presaleConfig: event?.presaleConfig,
  });
  const { barChartConfig, pieChartConfig } = lib.useAnalyticsChartConfig();

  if (!event) return;

  const colorKeys = lib.getShuffledColorKeys();
  const spacing = lib.calculateBarChartSpacing(barData);
  const pieData = lib.generatePieChartData(ticketCategoriess, colorKeys);

  return (
    <SafeAreaView className="flex-1 gap-2">
      <FocusAwareStatusBar />

      <View className="h-16 flex-row items-center justify-start gap-2 px-2">
        <Back />
        <LgBoldLabel>Event analytics</LgBoldLabel>
      </View>

      <ScrollView
        className="flex-1 px-4 py-6"
        contentContainerClassName="gap-4"
        showsVerticalScrollIndicator={false}
      >
        {/* Ticket Sales Analytics */}
        <AnalyticsCard title="Ticket sold" subtitle={dayjs().format('MMM D')}>
          <MetricDisplay
            value={`${totalSold}/${totalTicketQuantity}`}
            change={salesGrowth}
          />
          <TicketSalesChart
            data={barData}
            config={barChartConfig}
            spacing={spacing}
          />
        </AnalyticsCard>

        {/* Conversion Rate Analytics */}
        <AnalyticsCard
          title="Conversion rate"
          subtitle={`${dayjs(event.startTime).format('MMM D')} - ${dayjs(event.endTime).format('MMM D')}`}
        >
          <CategoryLegend
            categories={ticketCategoriess}
            colorKeys={colorKeys}
          />
          <ConversionRatePieChart data={pieData} config={pieChartConfig} />
        </AnalyticsCard>

        {/* Attendance Mode Analytics */}
        {/* <AnalyticsCard
          title={`Attendance mode - ${formatEnumLabel(event.eventFormat)}`}
          subtitle={dayjs().format('MMM D')}
        >
          <AttendanceModeChart
            data={horizontalBarData}
            config={horizontalBarConfig}
          />
        </AnalyticsCard> */}
      </ScrollView>
    </SafeAreaView>
  );
}
