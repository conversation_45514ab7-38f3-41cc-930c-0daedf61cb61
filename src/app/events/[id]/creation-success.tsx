import { Feather } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useRef } from 'react';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import QRCode from 'react-native-qrcode-svg';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ShareModal } from '@/components/modals/share';
import {
  Button,
  colors,
  FocusAwareStatusBar,
  H2,
  Image,
  P,
  semanticColors,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { APP_URL } from '@/lib/constants';

export default function EventCreatedSuccess() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const shareModalRef = useRef<any>(null);
  if (!id) {
    return null;
  }
  return (
    <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView
        className="flex-1"
        behavior="padding"
        keyboardVerticalOffset={10}
      >
        <View className="flex-1">
          <View className="gap-[54px] p-4">
            <View className="items-center justify-center gap-y-6 pt-10">
              <Image
                source={require('~/assets/images/success.png')}
                className="size-[120px] self-center"
              />
              <H2 className="text-center">
                Event has been created successfully
              </H2>
            </View>
            <View className="mx-auto">
              <QRCode
                size={200}
                value={`${APP_URL}/events/${id}`}
                enableLinearGradient
                backgroundColor="transparent"
                color={colors.white}
                linearGradient={[colors.white, colors.white]}
              />
            </View>
            <View className="flex-row items-center justify-between gap-y-4 rounded-md border border-fg-muted-light px-4 py-3 dark:border-fg-muted-dark">
              <View className="flex-1">
                <P className="text-xs text-fg-muted-light dark:text-fg-muted-dark">
                  Event URL
                </P>
                <P className="text-primary">{`${APP_URL}/events/${id}`}</P>
              </View>
              <TouchableOpacity
                onPress={() => shareModalRef.current?.present()}
                className="shrink-0"
              >
                <Feather
                  color={semanticColors.accent.moderate}
                  name="share"
                  size={24}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View className="mt-auto gap-2 px-4">
          <Button
            label="Home"
            variant="secondary"
            onPress={() => router.dismissTo('/')}
          />
        </View>
        <ShareModal
          shareModalRef={shareModalRef}
          onDismiss={() => shareModalRef.current?.dismiss()}
          content={`${APP_URL}/events/${id}`}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
