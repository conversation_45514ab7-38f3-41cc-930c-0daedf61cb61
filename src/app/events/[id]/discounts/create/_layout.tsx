import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';

import { useCreateDiscountsForm } from '@/lib';

export default function CreateDiscountCodesLayout() {
  const { formMethods } = useCreateDiscountsForm();

  return (
    <FormProvider {...formMethods}>
      <Stack>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="application"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="limits"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="tickets"
          options={{
            headerShown: false,
          }}
        />
      </Stack>
    </FormProvider>
  );
}
