import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { BareLayout } from '@/components/layouts/bare-layout';
import { Button, ControlledInput, Select, View } from '@/components/ui';
import { type CreateDiscountsFormType, useFieldBlurAndFilled } from '@/lib';

export default function CreateDiscountsIndex() {
  const { watch, control, setValue } =
    useFormContext<CreateDiscountsFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateDiscountsFormType>([
    'code',
    'value',
  ]);
  const router = useRouter();
  const { id, slug } = useLocalSearchParams<{
    id: string;
    slug: string;
  }>();

  const [_codeEnabled, _setCodeEnabled] = useState(false);

  // const handleCodeStateToggle = (checked: boolean) => {
  //   if (checked) {
  //     setCodeEnabled(true);
  //     setValue(`isEnabled`, true);
  //   } else {
  //     setCodeEnabled(false);
  //     setValue(`isEnabled`, false);
  //   }
  // };

  return (
    <BareLayout
      title="Discount code"
      subTitle="You can create single or multiple discount codes."
      footer={
        <Button
          testID="go-to-application-page-button"
          label="Next"
          className="m-4"
          disabled={
            !fieldStates.code.isValid ||
            !fieldStates.value.isValid ||
            !watch('type')
          }
          onPress={() =>
            router.push({
              pathname: '/events/[id]/discounts/create/application',
              params: { id, slug },
            })
          }
        />
      }
    >
      <View className="gap-4">
        {/* <FeatureToggle
          title="Enable discount code"
          checked={codeEnabled}
          onChange={handleCodeStateToggle}
          accessibilityLabel="Offer ticket presale"
        /> */}
        <ControlledInput
          name="code"
          label="Enter code e.g., EARLYBIRD, VIP10"
          control={control}
        />
        <Select
          title="Select discount type"
          onSelect={(value) => {
            setValue('type', value as 'fixed' | 'percentage');
          }}
          placeholder="Select discount type"
          options={[
            { label: 'Percentage', value: 'percentage' },
            { label: 'Fixed amount', value: 'fixed' },
          ]}
          value={watch('type')}
        />
        <ControlledInput
          name="value"
          label={
            watch('type') === 'fixed'
              ? 'Discount amount e.g. 2000'
              : 'Discount Percentage (0-100)'
          }
          control={control}
          keyboardType="numeric"
          accessibilityLabel="Discount amount"
          accessibilityHint="Enter the amount of discount to be applied"
          onChangeText={(value) => setValue('value', Number(value))}
        />
      </View>
    </BareLayout>
  );
}
