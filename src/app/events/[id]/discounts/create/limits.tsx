import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { toast } from 'sonner-native';

import { DiscountType, useCreateEventDiscount } from '@/api/events';
import { BareLayout } from '@/components/layouts/bare-layout';
import {
  Button,
  ControlledInput,
  Pressable,
  Text,
  View,
} from '@/components/ui';
import { type CreateDiscountsFormType, usePurchaseTicketContext } from '@/lib';

export default function CreateDiscountsIndex() {
  const queryClient = useQueryClient();
  const { watch, control, setValue } =
    useFormContext<CreateDiscountsFormType>();
  const router = useRouter();
  const { id, slug } = useLocalSearchParams<{
    id: string;
    slug: string;
  }>();

  const [_codeEnabled, _setCodeEnabled] = useState(false);

  // const handleCodeStateToggle = (checked: boolean) => {
  //   if (checked) {
  //     setCodeEnabled(true);
  //     setValue(`isEnabled`, true);
  //   } else {
  //     setCodeEnabled(false);
  //     setValue(`isEnabled`, false);
  //   }
  // };

  const [openStartDatetime, setOpenStartDatetime] = useState(false);
  const [openEndDatetime, setOpenEndDatetime] = useState(false);

  const startDatetime = watch(`startDatetime`);

  const startDatetimeLabel = startDatetime
    ? startDatetime.toLocaleString()
    : 'Start date and time (optional)';

  const endDatetime = watch(`endDatetime`);

  const endDatetimeLabel = endDatetime
    ? endDatetime.toLocaleString()
    : 'End date and time (optional)';

  const { isPending, mutate: createEventDiscount } = useCreateEventDiscount();

  const { event } = usePurchaseTicketContext();

  const onSubmit = () => {
    const form = watch();
    const { endDatetime, startDatetime, limit, isForAll } = form;
    const payload = {
      discountType:
        form.type === 'fixed' ? DiscountType.AMOUNT : DiscountType.PERCENTAGE,
      eventId: event?.id || id,
      discountValue: form.value,
      applicableTicketCategories: isForAll ? undefined : form.tickets,
      code: form.code,
      name: form.code,
      ...(startDatetime && { startDateTime: startDatetime.toISOString() }),
      ...(endDatetime && { startDateTime: endDatetime.toISOString() }),
      ...(limit && { maxUsage: limit }),
    };
    console.log('🚀 ~ onSubmit ~ payload:', payload);
    createEventDiscount(payload, {
      onSuccess: () => {
        toast.success('Discount created successfully');
        queryClient.invalidateQueries({ queryKey: ['getEventDiscounts'] });
        router.dismissTo({
          pathname: '/events/[id]/discounts',
          params: { id, slug },
        });
      },
      onError: (error) => toast.error(error.message),
    });
  };

  return (
    <BareLayout
      title="Discount code"
      subTitle="You can create single or multiple discount codes."
      footer={
        <Button
          testID="go-to-application-page-button"
          label="Create"
          className="m-4"
          loading={isPending}
          disabled={isPending}
          onPress={onSubmit}
        />
      }
    >
      <View className="gap-4">
        <ControlledInput
          name="limit"
          label="Code usage limit (optional)"
          control={control}
          onChangeText={(limit) => setValue('limit', Number(limit))}
        />
        <Pressable
          testID="gender-input"
          className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
          onPress={() => setOpenStartDatetime(true)}
        >
          <Text
            className={
              startDatetime
                ? 'dark:text-neutral-100'
                : 'text-neutral-400 dark:text-neutral-500'
            }
          >
            {startDatetimeLabel}
          </Text>
        </Pressable>
        <DateTimePickerModal
          isVisible={openStartDatetime}
          mode="datetime"
          onConfirm={(date) => {
            setValue(`startDatetime`, date);
            setOpenStartDatetime(false);
          }}
          onCancel={() => setOpenStartDatetime(false)}
        />
        <Pressable
          testID="gender-input"
          className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
          onPress={() => setOpenEndDatetime(true)}
        >
          <Text
            className={
              endDatetime
                ? 'dark:text-neutral-100'
                : 'text-neutral-400 dark:text-neutral-500'
            }
          >
            {endDatetimeLabel}
          </Text>
        </Pressable>
        <DateTimePickerModal
          isVisible={openEndDatetime}
          mode="datetime"
          onConfirm={(date) => {
            setValue(`endDatetime`, date);
            setOpenEndDatetime(false);
          }}
          onCancel={() => setOpenEndDatetime(false)}
        />
      </View>
    </BareLayout>
  );
}
