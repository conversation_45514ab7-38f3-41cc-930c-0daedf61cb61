import { useLocalSearchPara<PERSON>, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  EventDiscount,
  useDeleteEventDiscount,
  useGetEventDiscounts,
} from '@/api/events';
import { BareLayout } from '@/components/layouts/bare-layout';
import LoadingScreen from '@/components/loading';
import { Button, FeatureToggle, Image, Text, View } from '@/components/ui';
import EventDiscountMoreDropdown from '@/components/events/discount/more-dropdown';
import { toast } from 'sonner-native';
import { useQueryClient } from '@tanstack/react-query';
import ConfirmationDialog from '@/components/dialogs/confirmation';

export default function CreateDiscountsIndex() {
  const router = useRouter();
  const { id, slug } = useLocalSearchParams<{ id: string; slug: string }>();
  const queryClient = useQueryClient();

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [selectedDiscount, setSelectedDiscount] =
    useState<EventDiscount | null>(null);

  const { mutate: deleteDiscount } = useDeleteEventDiscount({
    onSuccess: (_, variables) => {
      queryClient.setQueryData<EventDiscount[]>(
        useGetEventDiscounts.getKey({ eventId: id }),
        (oldData) => oldData?.filter((d) => d.id !== variables.discountId) ?? []
      );
      toast.success('Discount deleted successfully');
    },
    onError: (error) => toast.error(error.message),
    onSettled: () => {
      setConfirmVisible(false);
      setSelectedDiscount(null);
    },
  });

  const { isPending, data: discounts } = useGetEventDiscounts({
    variables: { eventId: id },
  });

  const [codeEnabled, setCodeEnabled] = useState(true); // ideally from API

  const handleCodeStateToggle = (checked: boolean) => {
    setCodeEnabled(checked);
    // TODO: call mutation to update discount state on server
    // updateEventDiscount(
    //   { isActive: true, eventId: id, discountId: '' },

    //   {
    //     onSuccess: () => {
    //       toast.success('Discount created successfully');
    //       queryClient.invalidateQueries({ queryKey: ['getEventDiscounts'] });
    //       router.push({
    //         pathname: '/events/[id]/discounts',
    //         params: { id, slug },
    //       });
    //     },
    //     onError: (error) => toast.error(error.message),
    //   }
    // );
    // setValue(`isEnabled`, checked);
  };

  if (isPending) return <LoadingScreen />;

  return (
    <BareLayout
      title="Discount code"
      subTitle="You can create single or multiple discount codes."
      footer={
        <Button
          label="Go back to events page"
          className="m-4"
          onPress={() =>
            router.dismissTo({
              pathname: '/events/[id]',
              params: { id, slug },
            })
          }
        />
      }
    >
      <View className="gap-4">
        <FeatureToggle
          title="Enable discount code"
          checked={codeEnabled}
          onChange={handleCodeStateToggle}
          accessibilityLabel="Offer ticket presale"
        />

        <View className="gap-4">
          {discounts?.map((discount) => (
            <View
              key={discount.id}
              className="relative flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark"
            >
              <View className="flex-1 flex-row items-center gap-2">
                <Image
                  source={require('~/assets/icons/events/discount.png')}
                  className="size-10"
                  alt="Ticket"
                />
                <View className="gap-2">
                  <Text className="font-bold text-fg-base-light dark:text-fg-base-dark">
                    {discount.name || discount.code}
                  </Text>
                </View>
              </View>
              <EventDiscountMoreDropdown
                onValueChange={(value) => {
                  if (value === 'delete') {
                    setSelectedDiscount(discount);
                    setConfirmVisible(true);
                  }
                }}
                triggerClassName="size-6 bg-transparent dark:bg-transparent border-transparent"
                itemClassName="native:px-4 native:py-2.5 justify-between h-11"
              />
            </View>
          ))}

          <Button
            label="Add discount code +"
            variant="secondary"
            className="w-[180px] py-2"
            size="sm"
            onPress={() =>
              router.push({
                pathname: '/events/[id]/discounts/create',
                params: { id, slug },
              })
            }
          />
        </View>
      </View>

      <ConfirmationDialog
        visible={confirmVisible}
        message={`Are you sure you want to delete ${
          selectedDiscount?.name || 'this discount'
        }?`}
        onCancel={() => {
          setConfirmVisible(false);
          setSelectedDiscount(null);
        }}
        onConfirm={() => {
          if (selectedDiscount) {
            deleteDiscount({
              eventId: id,
              discountId: selectedDiscount.id,
            });
          }
        }}
      />
    </BareLayout>
  );
}
