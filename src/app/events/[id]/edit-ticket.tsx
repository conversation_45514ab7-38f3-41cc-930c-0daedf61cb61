import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

import { type TicketCategories } from '@/api/events';
import {
  Button,
  ControlledBottomSheetInputWithoutContext,
  ControlledInput,
  CostSelector,
  FeatureToggle,
  Modal,
  P,
  Pressable,
  ScrollView,
  semanticColors,
  Text,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { formatDate, formatDateTime } from '@/lib/utils/formatDateTime';
import { ticketSchema } from '@/lib';

type TicketCategory = TicketCategories[keyof TicketCategories];

type EditTicketFormType = {
  name: string;
  price: number;
  quantity: number;
  description?: string;
  hasPresale: boolean;
  presaleName?: string;
  presalePrice?: number;
  presaleQuantity?: number;
  presaleStartDatetime?: Date;
  presaleEndDatetime?: Date;
  presalePurchaseLimit?: number;
  hasTimeline: boolean;
  startDatetime?: Date;
  endDatetime?: Date;
  hasPurchaseLimit: boolean;
  purchaseLimit?: number;
};

export default function EditEventTicket() {
  const router = useRouter();
  const { id, category } = useLocalSearchParams<{
    id: string;
    category: string;
  }>();

  // Initialize form with existing ticket data (this would come from your API/state)
  const methods = useForm<z.infer<typeof ticketSchema>>({
    defaultValues: {
      name: category || '',
      price: 0,
      quantity: 0,
      description: '',
      hasPresale: false,
      hasTimeline: false,
      hasPurchaseLimit: false,
      purchaseLimit: 10,
      presalePurchaseLimit: 10,
    },
  });

  const { watch, control, setValue, getValues, handleSubmit } = methods;

  // Watch form values
  const name = watch('name');
  const price = watch('price');
  const quantity = watch('quantity');
  const hasPresale = watch('hasPresale');
  const hasTimeline = watch('hasTimeline');
  const hasPurchaseLimit = watch('hasPurchaseLimit');

  // Validation for enabling features
  const canEnable = name && price !== undefined && quantity;

  // Date validation functions
  const isStartDateValid = (date: Date) => {
    const now = new Date();
    return date >= now;
  };

  const isEndDateValid = (startDate: Date, endDate: Date) => {
    return endDate >= startDate;
  };

  // Presale modal and state
  const presaleModal = useModal();
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] =
    useState(10);

  const enablePresale = () => {
    setValue('hasPresale', true);
    setValue('presalePrice', 0);
    setValue('presaleQuantity', 0);
    setValue('presaleName', `${name} - `);
    setValue('presaleDescription', '');
  };

  const disablePresale = () => {
    setValue('hasPresale', false);
  };

  const togglePresale = (checked: boolean) => {
    if (canEnable && checked) {
      enablePresale();
      presaleModal.present();
    } else {
      disablePresale();
      presaleModal.dismiss();
    }
  };

  const handleAddPresale = () => {
    setValue('presalePurchaseLimit', localPresalePurchaseLimit);
    presaleModal.dismiss();
  };

  const handlePresaleModalDismiss = () => {
    const presaleName = watch('presaleName');
    const presalePrice = watch('presalePrice');
    const presaleStartDatetime = watch('presaleStartDatetime');
    const presaleEndDatetime = watch('presaleEndDatetime');
    const presaleQuantity = watch('presaleQuantity');

    if (
      !presaleName ||
      !presalePrice ||
      !presaleStartDatetime ||
      !presaleEndDatetime ||
      !presaleQuantity
    ) {
      disablePresale();
    }
  };

  // Timeline modal and state
  const timelineModal = useModal();

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setValue('hasTimeline', true);
      timelineModal.present();
    } else {
      setValue('hasTimeline', false);
    }
  };

  const handleAddTimeline = () => {
    timelineModal.dismiss();
  };

  const handleTimelineModalDismiss = () => {
    const startDatetime = watch('startDatetime');
    const endDatetime = watch('endDatetime');

    if (!startDatetime || !endDatetime) {
      setValue('hasTimeline', false);
    }
  };

  // Purchase limit modal and state
  const purchaseLimitModal = useModal();
  const [localPurchaseLimit, setLocalPurchaseLimit] = useState(10);

  const togglePurchaseLimit = (checked: boolean) => {
    if (canEnable && checked) {
      setValue('hasPurchaseLimit', true);
      purchaseLimitModal.present();
    } else {
      setValue('hasPurchaseLimit', false);
    }
  };

  const handleAddPurchaseLimit = () => {
    setValue('purchaseLimit', localPurchaseLimit);
    purchaseLimitModal.dismiss();
  };

  const handlePurchaseLimitModalDismiss = () => {
    if (!watch('purchaseLimit')) {
      setValue('hasPurchaseLimit', false);
    }
  };

  // Date picker states
  const [openPresaleStartDatetime, setOpenPresaleStartDatetime] =
    useState(false);
  const [openPresaleEndDatetime, setOpenPresaleEndDatetime] = useState(false);
  const [openStartDatetime, setOpenStartDatetime] = useState(false);
  const [openEndDatetime, setOpenEndDatetime] = useState(false);

  // Date handlers
  const handlePresaleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue('presaleStartDatetime', date);
    setOpenPresaleStartDatetime(false);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch('presaleStartDatetime');
    if (!start || !isEndDateValid(start, date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue('presaleEndDatetime', date);
    setOpenPresaleEndDatetime(false);
  };

  const handleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue('startDatetime', date);
    setOpenStartDatetime(false);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch('startDatetime');
    if (!start || !isEndDateValid(start, date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue('endDatetime', date);
    setOpenEndDatetime(false);
  };

  // Form submission
  const onSubmit = (data: EditTicketFormType) => {
    console.log('Updated ticket data:', data);
    // Here you would call your API to update the ticket
    router.back();
  };

  // Auto-update presale name when ticket name changes
  const presaleNameTouched = useRef(false);

  useEffect(() => {
    if (hasPresale && name && !presaleNameTouched.current) {
      setValue('presaleName', `${name} - `);
    }
  }, [name, hasPresale, setValue]);

  return (
    <FormProvider {...methods}>
      <CreateEventLayout
        title="Edit Ticket"
        subTitle={`Make changes to ${category || 'ticket'} category.`}
        footer={
          <Button
            testID="ticket-update-button"
            label="Update Ticket"
            className="m-4"
            disabled={!name || price === undefined || !quantity}
            onPress={handleSubmit(onSubmit)}
          />
        }
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <View className="gap-4">
            <ControlledInput
              name="name"
              label="Ticket name e.g General admission, VIP"
              control={control}
            />

            <CostSelector
              control={control}
              setValue={setValue}
              name="price"
              label="Price"
              costOptions={[0, 10000, 20000, 50000]}
              testID="cost-input"
            />

            <ControlledInput
              name="quantity"
              label="Quantity available"
              control={control}
            />

            <ControlledInput
              name="description"
              label="Ticket description (optional)"
              control={control}
            />

            <FeatureToggle
              title="Enable purchase limit"
              subtitle="Limit ticket per order, default uses a maximum of 10"
              checked={hasPurchaseLimit}
              onChange={togglePurchaseLimit}
              {...(hasPurchaseLimit && {
                editText: `Maximum of ${localPurchaseLimit} ticket(s)`,
                onEditPress: () => purchaseLimitModal.present(),
              })}
              accessibilityLabel="Enable purchase limit"
            />

            <FeatureToggle
              title="Include ticket sale timeline"
              subtitle="Set specific period for ticket sale, default uses event date and time"
              checked={hasTimeline}
              onChange={toggleTimeline}
              {...(hasTimeline &&
                watch('startDatetime') &&
                watch('endDatetime') && {
                  editText: `${formatDate(watch('startDatetime')!)} - ${formatDate(watch('endDatetime')!)}`,
                  onEditPress: () => timelineModal.present(),
                })}
              accessibilityLabel="Include ticket sale timeline"
            />

            <FeatureToggle
              title="Offer ticket presale"
              checked={hasPresale}
              onChange={togglePresale}
              accessibilityLabel="Offer ticket presale"
              {...(hasPresale && {
                editText: 'Edit presale ticket',
                onEditPress: () => presaleModal.present(),
              })}
            />
          </View>
        </ScrollView>

        {/* Presale Modal */}
        <Modal
          ref={presaleModal.ref}
          enableDynamicSizing
          onDismiss={handlePresaleModalDismiss}
        >
          <BottomSheetView className="min-h-30 pb-6">
            <View className="gap-4 px-4">
              <View className="flex-row items-center justify-start gap-2">
                <TouchableOpacity
                  onPress={presaleModal.dismiss}
                  accessibilityLabel="Close modal"
                  accessibilityRole="button"
                >
                  <Ionicons
                    name="close"
                    size={32}
                    color={semanticColors.fg.subtle.dark}
                  />
                </TouchableOpacity>
                <Text className="text-lg font-bold dark:text-neutral-100">
                  Set up presale
                </Text>
              </View>

              <ControlledBottomSheetInputWithoutContext
                name="presaleName"
                label="Presale ticket name"
                onChangeText={(text) => {
                  presaleNameTouched.current = true;
                  setValue('presaleName', text);
                }}
                control={control}
              />

              <ControlledBottomSheetInputWithoutContext
                name="presalePrice"
                label="Presale price"
                control={control}
              />

              <Pressable
                testID="edit-presale-start-date-input"
                className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
                onPress={() => setOpenPresaleStartDatetime(true)}
              >
                <Text
                  className={
                    watch('presaleStartDatetime')
                      ? 'dark:text-neutral-100'
                      : 'text-neutral-400 dark:text-neutral-500'
                  }
                >
                  {watch('presaleStartDatetime')
                    ? formatDateTime(watch('presaleStartDatetime')!)
                    : 'Start date and time'}
                </Text>
              </Pressable>

              <DateTimePickerModal
                isVisible={openPresaleStartDatetime}
                mode="datetime"
                onConfirm={handlePresaleStartDateConfirm}
                onCancel={() => setOpenPresaleStartDatetime(false)}
                minimumDate={new Date()}
              />

              <Pressable
                testID="edit-presale-end-date-input"
                className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
                  watch('presaleStartDatetime')
                    ? 'border-border-subtle-light dark:border-border-subtle-dark'
                    : 'border-neutral-200 opacity-50 dark:border-neutral-700'
                }`}
                disabled={!watch('presaleStartDatetime')}
                onPress={() => {
                  if (watch('presaleStartDatetime'))
                    setOpenPresaleEndDatetime(true);
                }}
              >
                <Text
                  className={
                    watch('presaleEndDatetime')
                      ? 'dark:text-neutral-100'
                      : 'text-neutral-400 dark:text-neutral-500'
                  }
                >
                  {watch('presaleEndDatetime')
                    ? formatDateTime(watch('presaleEndDatetime')!)
                    : 'End date and time'}
                </Text>
              </Pressable>

              <DateTimePickerModal
                isVisible={openPresaleEndDatetime}
                mode="datetime"
                onConfirm={handlePresaleEndDateConfirm}
                onCancel={() => setOpenPresaleEndDatetime(false)}
                disabled={!watch('presaleStartDatetime')}
                minimumDate={watch('presaleStartDatetime')}
              />

              <ControlledBottomSheetInputWithoutContext
                name="presaleQuantity"
                label="Presale quantity available"
                control={control}
              />

              <View className="flex-row items-center justify-between">
                <Text className="text-base font-medium dark:text-neutral-100">
                  Presale purchase limit
                </Text>

                <View className="flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600">
                  <TouchableOpacity
                    onPress={() => {
                      if (localPresalePurchaseLimit > 1) {
                        setLocalPresalePurchaseLimit(
                          localPresalePurchaseLimit - 1
                        );
                      }
                    }}
                    accessibilityLabel="Decrease purchase limit"
                  >
                    <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                      -
                    </Text>
                  </TouchableOpacity>

                  <Text className="px-2 text-base font-semibold dark:text-neutral-100">
                    {localPresalePurchaseLimit}
                  </Text>

                  <TouchableOpacity
                    onPress={() => {
                      if (localPresalePurchaseLimit < 10) {
                        setLocalPresalePurchaseLimit(
                          localPresalePurchaseLimit + 1
                        );
                      }
                    }}
                    accessibilityLabel="Increase purchase limit"
                  >
                    <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                      +
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <Button
              label="Save"
              className="m-4"
              disabled={
                !watch('presaleEndDatetime') ||
                !watch('presaleStartDatetime') ||
                !watch('presalePrice') ||
                !watch('presaleQuantity')
              }
              onPress={handleAddPresale}
            />
          </BottomSheetView>
        </Modal>

        {/* Timeline Modal */}
        <Modal
          ref={timelineModal.ref}
          enableDynamicSizing
          onDismiss={handleTimelineModalDismiss}
        >
          <BottomSheetView className="min-h-1 gap-4 px-4 pb-6">
            <View className="flex-row items-center justify-start gap-2">
              <TouchableOpacity
                onPress={timelineModal.dismiss}
                accessibilityLabel="Close modal"
                accessibilityRole="button"
              >
                <Ionicons
                  name="close"
                  size={32}
                  color={semanticColors.fg.subtle.dark}
                />
              </TouchableOpacity>
              <Text className="text-lg font-bold dark:text-neutral-100">
                Include ticket sale timeline
              </Text>
            </View>

            <P className="text-sm text-neutral-500 dark:text-neutral-400">
              Set specific period for this ticket type sale
            </P>

            <Pressable
              testID="select-ticket-sale-start-date-time-button"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={() => setOpenStartDatetime(true)}
            >
              <Text
                className={
                  watch('startDatetime')
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {watch('startDatetime')
                  ? formatDateTime(watch('startDatetime')!)
                  : 'Start date and time'}
              </Text>
            </Pressable>

            <DateTimePickerModal
              isVisible={openStartDatetime}
              mode="datetime"
              onConfirm={handleStartDateConfirm}
              onCancel={() => setOpenStartDatetime(false)}
              minimumDate={new Date()}
            />

            <Pressable
              testID="select-ticket-sale-end-date-time-button"
              className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
                watch('startDatetime')
                  ? 'border-border-subtle-light dark:border-border-subtle-dark'
                  : 'border-neutral-200 opacity-50 dark:border-neutral-700'
              }`}
              disabled={!watch('startDatetime')}
              onPress={() => {
                if (watch('startDatetime')) setOpenEndDatetime(true);
              }}
            >
              <Text
                className={
                  watch('endDatetime')
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {watch('endDatetime')
                  ? formatDateTime(watch('endDatetime')!)
                  : 'End date and time'}
              </Text>
            </Pressable>

            <DateTimePickerModal
              isVisible={openEndDatetime}
              mode="datetime"
              onConfirm={handleEndDateConfirm}
              onCancel={() => setOpenEndDatetime(false)}
              disabled={!watch('startDatetime')}
              minimumDate={watch('startDatetime')}
            />

            <Button
              label="Save"
              className="mt-auto"
              onPress={handleAddTimeline}
            />
          </BottomSheetView>
        </Modal>

        {/* Purchase Limit Modal */}
        <Modal
          ref={purchaseLimitModal.ref}
          enableDynamicSizing
          onDismiss={handlePurchaseLimitModalDismiss}
        >
          <BottomSheetView className="min-h-10 gap-4 px-4 pb-6">
            <View className="flex-row items-center justify-start gap-2">
              <TouchableOpacity
                onPress={purchaseLimitModal.dismiss}
                accessibilityLabel="Close modal"
                accessibilityRole="button"
              >
                <Ionicons
                  name="close"
                  size={32}
                  color={semanticColors.fg.subtle.dark}
                />
              </TouchableOpacity>
              <Text className="text-lg font-bold dark:text-neutral-100">
                Enable purchase limit
              </Text>
            </View>

            <P className="text-sm text-neutral-500 dark:text-neutral-400">
              Set the maximum number of this ticket type a single buyer can
              include in their order
            </P>

            <View className="flex-row items-center justify-between">
              <Text className="text-base font-medium dark:text-neutral-100">
                Number of allowed purchase
              </Text>

              <View className="flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600">
                <TouchableOpacity
                  onPress={() => {
                    if (localPurchaseLimit > 1) {
                      setLocalPurchaseLimit(localPurchaseLimit - 1);
                    }
                  }}
                  accessibilityLabel="Decrease purchase limit"
                >
                  <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                    -
                  </Text>
                </TouchableOpacity>

                <Text className="px-2 text-base font-semibold dark:text-neutral-100">
                  {localPurchaseLimit}
                </Text>

                <TouchableOpacity
                  onPress={() => {
                    if (localPurchaseLimit < 10) {
                      setLocalPurchaseLimit(localPurchaseLimit + 1);
                    }
                  }}
                  accessibilityLabel="Increase purchase limit"
                >
                  <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                    +
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <Button
              label="Save"
              className="mt-auto"
              onPress={() => {
                setValue(
                  `tickets.${ticketIndex}.purchaseLimit`,
                  localPurchaseLimit
                );
                handleAddPurchaseLimit();
              }}
            />
          </BottomSheetView>
        </Modal>
        <Modal
          ref={timelineModal.ref}
          enableDynamicSizing
          onDismiss={handleTimelineModalDismiss}
        >
          <BottomSheetView className="min-h-1 gap-4 px-4 pb-6">
            <View className="flex-row items-center justify-start gap-2">
              <TouchableOpacity
                onPress={timelineModal.dismiss}
                className=""
                accessibilityLabel="Close select modal"
                accessibilityRole="button"
              >
                <Ionicons
                  name="close"
                  size={32}
                  color={semanticColors.fg.subtle.dark}
                />
              </TouchableOpacity>
              <Text className="text-lg font-bold dark:text-neutral-100">
                Include ticket sale timeline
              </Text>
            </View>
            <P className="text-sm text-neutral-500 dark:text-neutral-400">
              Set specific period for this ticket type sale
            </P>
            <Pressable
              testID="select-ticket-sale-start-date-time-button"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={() => setOpenStartDatetime(true)}
            >
              <Text
                className={
                  startDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {startDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openStartDatetime}
              mode="datetime"
              onConfirm={handleStartDateConfirm}
              onCancel={() => setOpenStartDatetime(false)}
              minimumDate={new Date()}
            />
            <Pressable
              testID="select-ticket-sale-end-date-time-button"
              className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
                startDatetime
                  ? 'border-border-subtle-light dark:border-border-subtle-dark'
                  : 'border-neutral-200 opacity-50 dark:border-neutral-700'
              }`}
              disabled={!startDatetime}
              onPress={() => {
                if (startDatetime) setOpenEndDatetime(true);
              }}
            >
              <Text
                className={
                  endDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {endDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openEndDatetime}
              mode="datetime"
              onConfirm={handleEndDateConfirm}
              onCancel={() => setOpenEndDatetime(false)}
              disabled={!startDatetime}
              minimumDate={startDatetime}
            />
            <Button
              label="Save"
              className="mt-auto"
              onPress={handleAddTimeline}
            />
          </BottomSheetView>
        </Modal>
      </CreateEventLayout>
    </FormProvider>
  );
}
