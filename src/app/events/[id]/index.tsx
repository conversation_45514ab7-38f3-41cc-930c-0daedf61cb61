/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import { Feather, Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useQueryClient } from '@tanstack/react-query';
import { isPast } from 'date-fns';
import * as Calendar from 'expo-calendar';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useRef, useState } from 'react';
import { Alert, Pressable, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import {
  EventFormat,
  type ExportListType,
  getEventGuestList,
  type GuestListResponse,
  useDeleteEvent,
  useDeleteEventArtist,
  useGetEventDiscounts,
  useGetEventGuestList,
  useReserveEventTicket,
} from '@/api/events';
import EventBannerCarousel from '@/components/carousel/event-banner';
import { Back } from '@/components/common/back';
import Counter from '@/components/common/counter';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import { EventEditModal } from '@/components/events/detail/event-edit-modal';
import { EventInfo } from '@/components/events/detail/info';
import EventDetailMoreDropdown from '@/components/events/detail/more-dropdown';
import LoadingScreen from '@/components/loading';
import { ShareModal } from '@/components/modals/share';
import { EmptyState } from '@/components/ui';
import {
  Button,
  colors,
  FocusAwareStatusBar,
  GraphChartIcon,
  H2,
  H4,
  H5,
  H6,
  Image,
  LgBoldLabel,
  List,
  Modal,
  Options,
  type OptionType,
  Pencil,
  PillOption,
  SafeAreaView,
  ScrollView,
  semanticColors,
  Small,
  TouchableOpacity,
  useModal,
  View,
  XsBoldLabel,
} from '@/components/ui';
import {
  APP_URL,
  cn,
  formatAmount,
  hasTicketCategories,
  isPresaleActive,
  openInMaps,
  toAmountInMajor,
  useAuth,
  useDownloadFile,
  useFavoriteToggle,
} from '@/lib';
import { usePurchaseTicketContext } from '@/lib/contexts/purchase-ticket-context';
import { EventTicketCard } from '@/components/events/detail/ticket-card';

const exportGuestsModalOptions: OptionType[] = [
  { label: 'PDF', value: 'pdf' },
  { label: 'Excel', value: 'excel' },
];

export default function EventDetails() {
  const queryClient = useQueryClient();
  const user = useAuth.use.user();
  const [menuVisible, setMenuVisible] = useState<string | number | null>(null);

  const [_confirmVisible, setConfirmVisible] = useState(false);

  const { id, slug } = useLocalSearchParams<{
    id: string;
    slug: string;
  }>();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const editModal = useModal();
  const modal = useModal();
  const exportOptionSelectModal = useModal();
  const shareModalRef = useRef<any>(null);
  const handleShare = () => shareModalRef.current?.present();

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [confirmRAVisible, setConfirmRAVisible] = useState(false);

  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const {
    event,
    eventId,
    categories,
    isEventLoading,
    handleTicketQuantityChange,
    hasSelectedTickets,
    selectedTickets,
    setEditType,
  } = usePurchaseTicketContext();

  const isCreator = user?.id === (event?.organizer.id || event?.organizerId);
  const isSoldOut = hasTicketCategories(event?.ticketCategories)
    ? Object.values(event?.ticketCategories).every(
        ({ quantity }) => quantity === 0
      )
    : false;

  const hasEventEnded = event?.endTime ? isPast(event.endTime) : true;

  const { mutate: deleteEvent, isPending: deletingEvent } = useDeleteEvent();

  const handleDeleteEvent = () => {
    Alert.alert('Delete Event', 'Are you sure you want to delete this event?', [
      {
        text: 'Delete',
        onPress: () =>
          deleteEvent(
            { id: event?.id || id },
            {
              onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['getEvents'] });
                toast.success('Event deleted successfully');
                router.back();
              },
              onError: (error) => toast.success(error.message),
            }
          ),
        style: 'destructive',
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  };

  const [fileType, setFileType] = useState<ExportListType>();

  const { isFetching: isExportingList } = useGetEventGuestList({
    variables: { eventId, fileType: fileType || 'excel' },
    enabled: false,
  });

  const { downloadFile, isDownloading } = useDownloadFile();

  const handleDownloadGuestList = React.useCallback(
    async (type: ExportListType) => {
      setFileType(type);

      const queryKey = useGetEventGuestList.getKey({ eventId, fileType: type });

      try {
        const data = await queryClient.fetchQuery<GuestListResponse>({
          queryKey,
          queryFn: () => getEventGuestList({ eventId, fileType: type }),
          staleTime: 5 * 60 * 1000,
        });

        if (data?.link) {
          await downloadFile({
            url: data.link,
            fileName:
              data.link.split('/').pop() || `${event?.title} - guest list`,
            mimeType:
              type === 'excel' ? 'application/vnd.ms-excel' : 'application/pdf',
          });
        }
      } catch (error) {
        console.error('❌ Error downloading guest list:', error);
      } finally {
        exportOptionSelectModal.dismiss();
        setFileType(undefined);
      }
    },
    [eventId, event?.title, queryClient, downloadFile, exportOptionSelectModal]
  );

  const onSelectOption = React.useCallback(
    (option: OptionType) => {
      handleDownloadGuestList(option.value as ExportListType);
    },
    [handleDownloadGuestList]
  );

  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: event?.isFavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
    invalidateQueries: [
      ['getUserFavourites', { type: 'EVENT' }],
      [
        'getEvents',
        {
          userId: user?.id,
          organizerId: event?.organizer.id || event?.organizerId,
        },
      ],
    ],
  });

  const eventCategory = categories?.find(
    (categories) => categories.id === event?.categoryId
  );
  const hasActivePresale = (): boolean => {
    if (!event?.presaleConfig || event.presaleConfig.length === 0) {
      return false;
    }

    return event.presaleConfig.some((presaleTicket) =>
      isPresaleActive(presaleTicket)
    );
  };

  const hasPhysicalLocation = event?.eventFormat !== EventFormat.ONLINE;

  const ticketCategories = event?.ticketCategories || {};
  const prices = Object.values(ticketCategories).map(
    (ticket) => ticket.convertedCost || ticket.cost
  );
  const startingPrice = prices.length > 0 ? Math.min(...prices) : null;
  const eventAddress = hasPhysicalLocation
    ? event?.location.landmark ||
      event?.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : event.onlineEventUrl || 'Online';

  const addToCalendar = async () => {
    const { status } = await Calendar.requestCalendarPermissionsAsync();
    if (status === Calendar.PermissionStatus.GRANTED) {
      try {
        const createdEvent = await Calendar.createEventInCalendarAsync({
          creationDate: event?.createdAt,
          startDate: event?.startTime,
          endDate: event?.endTime,
          location: eventAddress,
          // timeZone: event?.timezone,
          title: event?.title,
          notes: event?.description,
          status:
            event?.status === 'APPROVED' || event?.status === 'LIVE'
              ? Calendar.EventStatus.CONFIRMED
              : event?.status === 'SCHEDULED'
                ? Calendar.EventStatus.TENTATIVE
                : Calendar.EventStatus.NONE,
          url: `${APP_URL}/events/${slug}`,
        });
        console.log('Here is your createdEvent:');
        console.log({ createdEvent });
        if (createdEvent.action === Calendar.CalendarDialogResultActions.done) {
          toast.success('Event added to calendar');
        } else if (
          createdEvent.action === Calendar.CalendarDialogResultActions.canceled
        ) {
          toast.info('Action canceled');
        }
      } catch (error) {
        console.log('🚀 ~ addToCalendar ~ error:', error);
      }
    } else {
      toast.error(
        'You must grant calendar permission to add event to calendar'
      );
    }
  };

  const { data: discounts } = useGetEventDiscounts({
    variables: { eventId: id },
    enabled: isCreator,
  });

  const handleEventEdit = (type: 'desc' | 'date' | 'location') => {
    setEditType(type);
    editModal.present();
  };

  const { mutate: removeArtist, isPending: isRemovingArtist } =
    useDeleteEventArtist({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            'getEventWithSlug',
            { slug, targetCurrency: 'NGN', userId: user?.id },
          ],
        });
        toast.success('Artist removed from event successfully');
      },
      onError: (error) => toast.error(error.message),
    });

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useReserveEventTicket({
      onSuccess: ({ expiresAt }) => {
        modal.dismiss();
        router.push({
          pathname: '/events/[id]/tickets/checkout',
          params: {
            id,
            expiresAt,
          },
        });
      },
      onError: (error) => toast.error(error.message),
    });

  if (isEventLoading || deletingEvent || isRemovingArtist)
    return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="h-16 flex-row items-center justify-between px-2">
        <Back />
        <LgBoldLabel>Event</LgBoldLabel>
        {isCreator ? (
          <View className="flex-row gap-2">
            <TouchableOpacity
              className="size-8 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
              onPress={() =>
                router.push({
                  pathname: '/events/[id]/analytics',
                  params: { id, slug },
                })
              }
            >
              <GraphChartIcon
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            </TouchableOpacity>
            <EventDetailMoreDropdown
              onValueChange={(value) => {
                switch (value) {
                  case 'share':
                    handleShare();
                    break;
                  case 'discount':
                    router.push({
                      pathname:
                        discounts && discounts?.length > 0
                          ? '/events/[id]/discounts'
                          : '/events/[id]/discounts/create',
                      params: { id, slug },
                    });
                    break;
                  case 'message':
                    router.push({
                      pathname: '/events/[id]/message-attendees',
                      params: { id, slug },
                    });
                    break;
                  case 'export-guests':
                    exportOptionSelectModal.present();
                    break;
                  case 'delete':
                    handleDeleteEvent();
                    break;

                  default:
                    break;
                }
              }}
              itemClassName="native:px-4 native:py-2.5 justify-between h-11"
            />
          </View>
        ) : (
          <TouchableOpacity onPress={handleShare}>
            <Feather name="share" size={24} color={colors.brand[60]} />
          </TouchableOpacity>
        )}
      </View>
      <ScrollView
        className="bg-bg-canvas flex-1 dark:bg-grey-100"
        style={{
          paddingBottom: insets.bottom,
        }}
        showsVerticalScrollIndicator={false}
      >
        <EventBannerCarousel
          event={event!}
          eventCategory={eventCategory!}
          favourited={favourited}
          isCreator={isCreator}
          handleFavToggle={handleFavToggle}
        />

        <View className="flex-1 gap-4 py-6">
          <View className="flex-row items-center justify-between px-4">
            <H2>{event?.title}</H2>
            {isCreator ? (
              <View
                className={cn(
                  'h-full w-[81px] flex-row items-center justify-center rounded-full',
                  event?.eventType === 'PRIVATE'
                    ? 'bg-bg-error-light dark:bg-bg-error-dark'
                    : 'bg-bg-success-light dark:bg-bg-success-dark'
                )}
              >
                <Feather
                  name={event?.eventType === 'PRIVATE' ? 'eye-off' : 'eye'}
                  size={12}
                  color={
                    event?.eventType === 'PRIVATE'
                      ? isDark
                        ? semanticColors.fg.error.dark
                        : semanticColors.fg.error.light
                      : isDark
                        ? semanticColors.fg.success.dark
                        : semanticColors.fg.success.light
                  }
                  className="mr-1"
                />
                <XsBoldLabel
                  className={cn(
                    event?.eventType === 'PRIVATE'
                      ? 'text-fg-error-light dark:text-fg-error-dark'
                      : 'text-green-60 dark:text-green-40'
                  )}
                >
                  {event?.eventType === 'PRIVATE' ? 'Private' : 'Public'}
                </XsBoldLabel>
              </View>
            ) : (
              <View className="flex-row items-center gap-2">
                {hasActivePresale() && (
                  <View className="h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                    <XsBoldLabel className="text-green-60 dark:text-green-40">
                      Presale
                    </XsBoldLabel>
                  </View>
                )}
                {hasEventEnded && (
                  <PillOption
                    option="Ended"
                    className="border-0 border-none bg-bg-danger-primary px-2 py-1"
                    textClassName="text-xs/100"
                  />
                )}
              </View>
            )}
          </View>

          <EventInfo
            addToCalendar={addToCalendar}
            event={event}
            isCreator={isCreator}
            openInMaps={openInMaps}
            handleEventEdit={handleEventEdit}
          />

          {event?.artists && event.artists.length > 0 && (
            <>
              <H5 className="px-4 text-grey-60 dark:text-grey-50">Lineup</H5>
              <View className="flex-row items-center gap-3">
                <List
                  data={event.artists}
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  estimatedItemSize={90}
                  contentContainerStyle={{
                    paddingTop: 12,
                    paddingBottom: 12,
                    paddingRight: 12,
                  }}
                  ListEmptyComponent={<EmptyState />}
                  renderItem={({ item, index }) => (
                    <View
                      key={index}
                      className={cn(
                        'w-20 rounded-md p-2 relative',
                        index === 0 && 'ml-4'
                      )}
                    >
                      <LinearGradient
                        colors={['#FFFFFF', '#FFFFFF']}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                        style={{
                          borderRadius: 8,
                          opacity: 0.05,
                          ...StyleSheet.absoluteFillObject,
                        }}
                      />
                      {isCreator && (
                        <Pressable
                          onPress={() => {
                            setArtistToDelete(item.id);
                            setConfirmRAVisible(true);
                          }}
                          className="absolute -right-[5px] -top-2.5 z-10"
                        >
                          <View className="size-6 items-center justify-center rounded-full bg-bg-danger-primary dark:bg-bg-danger-primary">
                            <View className="size-4 items-center justify-center rounded-full border-2 border-accent-on-accent dark:border-accent-on-accent">
                              <Ionicons name="close" size={12} color="#fff" />
                            </View>
                          </View>
                        </Pressable>
                      )}
                      <View className="items-center gap-2">
                        <Image
                          className="size-[52px] rounded-full"
                          source={item.avatar}
                        />
                        <Small numberOfLines={1}>{item.artistName}</Small>
                      </View>
                    </View>
                  )}
                  ItemSeparatorComponent={() => <View className="size-2.5" />}
                  keyExtractor={(item) => String(item.id)}
                />
                {isCreator && (
                  <TouchableOpacity
                    className="size-10 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
                    onPress={() =>
                      router.push({
                        pathname: '/events/create/add-collaborators',
                        params: {
                          isEdit: 'true',
                          eventSlug: event.slug || slug,
                          eventId: event.id || id,
                        },
                      })
                    }
                  >
                    <Ionicons
                      name="add"
                      size={24}
                      color={isDark ? colors.brand[40] : colors.brand[70]}
                    />
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}

          <View className="gap-2 px-4">
            <H5 className="text-grey-60 dark:text-grey-50">About this event</H5>
            <Small>{event?.description}</Small>
            {isCreator && (
              <Button
                label="Edit"
                className="w-[62px]"
                size="xs"
                iconPosition="right"
                variant="outline"
                iconClassName="right-2"
                icon={
                  <Pencil
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                }
                onPress={() => handleEventEdit('desc')}
              />
            )}
          </View>
          {event?.ticketCategories &&
            Object.entries(event.ticketCategories).length > 0 &&
            isCreator && (
              <View className="gap-2 px-4">
                <H5 className="text-grey-60 dark:text-grey-50">Tickets</H5>
                <Pressable
                  onPressOut={() => setMenuVisible(null)}
                  className="flex-1"
                >
                  <View className="gap-4">
                    {Object.entries(event.ticketCategories).map(
                      ([key, ticket], index) => (
                        <View key={key} className="gap-4">
                          <EventTicketCard
                            ticket={{
                              ...ticket,
                              cost: toAmountInMajor(ticket.cost),
                            }}
                            category={key}
                            index={index}
                            isDark={isDark}
                            onEdit={() => {}}
                            onDelete={() => {}}
                          />
                        </View>
                      )
                    )}
                  </View>
                </Pressable>
              </View>
            )}
        </View>
      </ScrollView>
      {isCreator ? (
        <Button
          label="Scan ticket"
          className="mx-4"
          onPress={() =>
            router.push({
              pathname: '/events/[id]/scan-ticket',
              params: { id },
            })
          }
          disabled={hasEventEnded}
        />
      ) : (
        <View className="flex-row items-center justify-between border-t border-t-grey-20 p-4 dark:border-t-grey-80">
          <View>
            <H4>{formatAmount(Number(toAmountInMajor(startingPrice || 0)))}</H4>
            <Small className="text-grey-60 dark:text-grey-50">
              Starting at
            </Small>
          </View>
          <Button
            label={
              hasEventEnded ? 'Expired' : isSoldOut ? 'Sold out' : 'Get Ticket'
            }
            className="ml-auto h-10"
            onPress={modal.present}
            disabled={hasEventEnded || isSoldOut}
          />
        </View>
      )}

      <Modal index={0} ref={modal.ref} enableDynamicSizing>
        <BottomSheetView
          className="min-h-[280px] gap-2 px-4 pt-6"
          style={{
            paddingBottom: insets.bottom,
          }}
        >
          <H2>Tickets</H2>
          <View>
            {/* Prototype */}
            {Object.entries(ticketCategories).map(
              ([categoryKey, ticketCategory], index) => {
                const presaleTicket = event?.presaleConfig?.find(
                  (presale) => presale.ticketCategoryId === ticketCategory.id
                );

                const shouldUsePresale =
                  presaleTicket && isPresaleActive(presaleTicket);

                const activeTicket = shouldUsePresale
                  ? {
                      ...ticketCategory,
                      cost: presaleTicket.price,
                      quantity: presaleTicket.quantity,
                      purchaseLimit:
                        presaleTicket.purchaseLimit ||
                        ticketCategory.purchaseLimit,
                      description:
                        presaleTicket.description || ticketCategory.description,
                    }
                  : ticketCategory;

                return (
                  <View key={index} className="flex-row items-start gap-6 py-4">
                    <View className="flex-1 gap-2">
                      <View className="flex-row items-center justify-between">
                        <H4>{categoryKey}</H4>
                        {shouldUsePresale && (
                          <View className="h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                            <XsBoldLabel className="text-green-60 dark:text-green-40">
                              Presale
                            </XsBoldLabel>
                          </View>
                        )}
                      </View>
                      <H6 className="text-fg-muted-light dark:text-fg-muted-dark">
                        {formatAmount(
                          Number(
                            toAmountInMajor(
                              activeTicket.convertedCost || activeTicket.cost
                            )
                          )
                        )}
                      </H6>
                      <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                        {activeTicket.description}
                      </Small>
                    </View>
                    <Counter
                      initialValue={selectedTickets[categoryKey] || 1}
                      value={selectedTickets[categoryKey] || 1}
                      minimum={0}
                      maximum={
                        activeTicket.quantity > 10 ? 10 : activeTicket.quantity
                      }
                      onValueChange={(quantity) =>
                        handleTicketQuantityChange(categoryKey, quantity)
                      }
                      className="border border-border-subtle-light dark:border-border-subtle-dark"
                    />
                  </View>
                );
              }
            )}
          </View>
          <Button
            label="Continue"
            disabled={!hasSelectedTickets || reservingTicket}
            loading={reservingTicket}
            onPress={() =>
              reserveTicket({
                id,
                reservations: Object.entries(selectedTickets)
                  .filter(([_, quantity]) => quantity !== 0)
                  .map(([selectedTicketCategory, quantity]) => ({
                    category: selectedTicketCategory,
                    quantity,
                  })),
              })
            }
          />
        </BottomSheetView>
      </Modal>
      <ConfirmationDialog
        visible={confirmRAVisible}
        message="Are you sure you want to delete this artist?"
        onCancel={() => {
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            removeArtist({
              id: event?.id || id,
              artistId: artistToDelete,
            });
          }
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
      />
      <ShareModal
        shareModalRef={shareModalRef}
        onDismiss={() => shareModalRef.current?.dismiss()}
        content={`${APP_URL}/events/${event?.slug || slug || id}`}
      />
      {isCreator && event && (
        <>
          <EventEditModal
            event={event}
            ref={editModal.ref}
            onDismiss={() => editModal.dismiss()}
            onPresent={() => editModal.present()}
          />
          <Options
            testID="select-guests-export-option"
            ref={exportOptionSelectModal.ref}
            options={exportGuestsModalOptions}
            onSelect={onSelectOption}
            onDismiss={exportOptionSelectModal.dismiss}
            title="Select export format"
            value={fileType}
            loading={isExportingList || isDownloading}
          />
        </>
      )}
    </SafeAreaView>
  );
}
