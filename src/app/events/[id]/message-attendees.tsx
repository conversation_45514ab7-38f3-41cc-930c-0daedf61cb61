import * as React from 'react';
import { useForm } from 'react-hook-form';

import { BareLayout } from '@/components/layouts';
import { Button, ControlledInput } from '@/components/ui';

type FormType = { message: string };

export default function MessageAttendees() {
  const { control } = useForm<FormType>();

  return (
    <BareLayout
      title="Message attendees"
      subTitle="We will notify attendees via email"
      footer={
        <Button
          label="Send"
          testID="send-message-to-attendees"
          className="m-4"
        />
      }
    >
      <ControlledInput
        testID="message-attendees"
        control={control}
        name="message"
        label="Enter Message"
        multiline
        inputClassName="h-[100px]"
      />
    </BareLayout>
  );
}
