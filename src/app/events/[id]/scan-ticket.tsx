import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import {
  type BarcodeScanningResult,
  CameraView,
  useCameraPermissions,
} from 'expo-camera';
import { useLocalSearchParams } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { Alert, Linking } from 'react-native';

import {
  type TicketVerificationError,
  useGetEventTicket,
  useVerifyTicket,
} from '@/api/events';
import { BareLayout } from '@/components/layouts/bare-layout';
import LoadingScreen from '@/components/loading';
import {
  Button,
  colors,
  H2,
  Image,
  MdBoldLabel,
  Modal,
  P,
  useModal,
  View,
} from '@/components/ui';

export default function ScanTicket() {
  const modal = useModal();
  const { id: eventId } = useLocalSearchParams<{ id: string }>();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Camera and scanning state
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = React.useState(false);
  const [scannedTicketId, setScannedTicketId] = React.useState<string>('');

  const {
    data: ticket,
    error,
    isError,
    isSuccess,
    isLoading: isVerifyingTicket,
  } = useVerifyTicket({
    variables: {
      eventId,
      ticketId: scannedTicketId,
    },
    enabled: !!scannedTicketId,
    retry: false,
  });

  const { data: ticketDetails } = useGetEventTicket({
    variables: {
      eventId,
      ticketId: scannedTicketId,
    },
    enabled: !!scannedTicketId && isSuccess,
  });

  const fullName = ticket?.user?.fullName || 'N/A';
  const userFriendlyTicketId = ticketDetails?.userFriendlyTicketId || 'N/A';
  const grade = Object.keys(ticket?.breakdown || {})[0] || 'N/A';
  const quantity = ticket?.breakdown[grade] || 'N/A';
  let ticketStatus: TicketVerificationError['status'] | 'VALID' = 'INVALID';

  if (isSuccess) {
    const { isUsed } = ticket;
    ticketStatus = isUsed ? 'USED' : 'VALID';
  }
  if (isError) {
    const { status } = error;
    ticketStatus = status;
  }

  React.useEffect(() => {
    if (!permission) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  const handleBarcodeScanned = ({ data }: BarcodeScanningResult) => {
    if (scanned) return;

    setScanned(true);
    setScannedTicketId(data);
    modal.present();
  };

  const resetScanner = () => {
    console.log('🚀 ~ resetScanner');
    setScanned(false);
    setScannedTicketId('');
    modal.dismiss();
  };

  const getStatusTitle = () => {
    switch (ticketStatus) {
      case 'VALID':
        return 'Valid';
      case 'USED':
        return 'Used';
      case 'INVALID':
        return 'Invalid';
      case 'UNKNOWN':
        return 'EXPIRED';
      default:
        return 'Invalid';
    }
  };

  const getStatusDescription = () => {
    switch (ticketStatus) {
      case 'VALID':
        return 'This ticket is valid for your event';
      case 'USED':
        return 'This ticket has already been used';
      case 'INVALID':
        return 'This ticket is not valid';
      case 'UNKNOWN':
        return 'This ticket was not found';
      default:
        return 'This ticket is invalid';
    }
  };

  if (!permission || permission.status === 'undetermined') {
    return (
      <BareLayout
        title="Scan ticket"
        subTitle="Verify ticket and attendee information."
      >
        <View className="flex-1 items-center justify-center">
          <P className="text-center">Requesting camera permission...</P>
        </View>
      </BareLayout>
    );
  }

  if (permission.status !== 'granted') {
    return (
      <BareLayout
        title="Scan ticket"
        subTitle="Verify ticket and attendee information."
      >
        <View className="flex-1 items-center justify-center gap-4 p-4">
          <Ionicons
            name="camera-outline"
            size={64}
            color={isDark ? colors.white : colors.grey[100]}
          />
          <P className="text-center">
            Camera permission is required to scan tickets
          </P>
          {permission.canAskAgain ? (
            <Button label="Grant Permission" onPress={requestPermission} />
          ) : (
            <>
              <P className="text-center text-fg-muted-light dark:text-fg-muted-dark">
                Camera permission was denied. Please enable it manually in your
                device settings.
              </P>
              <Button
                variant="secondary"
                label="Open Settings"
                onPress={() => {
                  Alert.alert(
                    'Permission to access camera was denied',
                    'To use this feature, please enable camera access in your settings.',
                    [
                      {
                        text: 'Open Settings',
                        style: 'default',
                        onPress: () => Linking.openSettings(),
                      },
                      { text: 'Cancel', style: 'cancel' },
                    ]
                  );
                }}
              />
            </>
          )}
        </View>
      </BareLayout>
    );
  }

  return (
    <BareLayout
      title="Scan ticket"
      subTitle="Verify ticket and attendee information."
    >
      {/* <View className="bg-[#1A1A1A]/50 h-[488px]" /> */}
      <CameraView
        style={{ height: 488 }}
        facing="back"
        onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
        barcodeScannerSettings={{
          barcodeTypes: [
            'qr',
            'ean13',
            'ean8',
            'code128',
            'code39',
            'aztec',
            'datamatrix',
            'pdf417',
          ],
        }}
      />

      <View className="flex-row items-start gap-1 px-4">
        <Ionicons
          name="lock-closed"
          size={20}
          color={isDark ? colors.white : colors.grey[100]}
          onPress={modal.present}
        />
        <P className="flex-1 text-fg-muted-light dark:text-fg-muted-dark">
          Your privacy is important to us. This QR scan is 256-bit encrypted,
          ensuring the security of your personal data.
        </P>
      </View>

      <Modal
        ref={modal.ref}
        index={0}
        enableDynamicSizing
        onDismiss={resetScanner}
      >
        <BottomSheetView className="min-h-[555px] flex-1 gap-6 p-4">
          <H2>Ticket information</H2>

          {isVerifyingTicket ? (
            <LoadingScreen />
          ) : (
            <>
              <View className="gap-6">
                <View className="items-center">
                  <Image
                    source={
                      ticketStatus === 'VALID'
                        ? require('~/assets/images/success.png')
                        : require('~/assets/images/invalid.png')
                    }
                    className="h-52 w-[211px]"
                  />
                  <View className="items-center gap-2">
                    <H2>{getStatusTitle()}</H2>
                    <P>{getStatusDescription()}</P>
                  </View>
                </View>
                <View className="gap-6">
                  <View className="flex-row justify-between">
                    <View className="flex-1 gap-1">
                      <MdBoldLabel>Full name</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {fullName}
                      </P>
                    </View>
                    <View className="flex-1 gap-1">
                      <MdBoldLabel>Ticket ID</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {userFriendlyTicketId}
                      </P>
                    </View>
                  </View>
                  <View className="flex-row justify-between">
                    <View className="flex-1 gap-1">
                      <MdBoldLabel>Ticket grade</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {grade}
                      </P>
                    </View>
                    <View className="flex-1 gap-1">
                      <MdBoldLabel>Quantity</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {quantity}
                      </P>
                    </View>
                  </View>
                </View>
              </View>

              <Button variant="secondary" label="Done" onPress={resetScanner} />
            </>
          )}
        </BottomSheetView>
      </Modal>
    </BareLayout>
  );
}
