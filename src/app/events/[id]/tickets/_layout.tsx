import { Stack } from 'expo-router';

export default function EventTicketLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
          presentation: 'containedModal',
        }}
      />
      <Stack.Screen
        name="checkout"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="discount"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="success"
        options={{
          headerShown: false,
          presentation: 'containedModal',
        }}
      />
    </Stack>
  );
}
