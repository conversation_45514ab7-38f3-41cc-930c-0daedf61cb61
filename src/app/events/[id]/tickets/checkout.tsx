import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { queryClient } from '@/api';
import {
  usePurchaseEventTicket,
  useUpdateReserveEventTicket,
} from '@/api/events';
import { Back } from '@/components/common/back';
import GetTicketModal from '@/components/modals/get-ticket';
import CheckoutSummary from '@/components/tickets/checkout';
import { CheckoutTimer } from '@/components/tickets/checkout-timer';
import PaymentMethod from '@/components/tickets/payment-method';
import {
  Button,
  FocusAwareStatusBar,
  H1,
  Modal,
  useModal,
  View,
} from '@/components/ui';
import {
  getRemainingTimerSeconds,
  toAmountInMajor,
  useAuth,
  usePaystackCheckout,
} from '@/lib';
import { usePurchaseTicketContext } from '@/lib/contexts/purchase-ticket-context';
import {
  type AdditionalFees,
  type PurchaseTicketFormType,
} from '@/lib/hooks/use-purchase-ticket';

export default function Checkout() {
  const { expiresAt } = useLocalSearchParams<{
    expiresAt: string;
  }>();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const modal = useModal();
  const user = useAuth.use.user();
  const { watch } = useFormContext<PurchaseTicketFormType>();
  const paymentMethod = watch('paymentMethod');
  const {
    event,
    handleTicketQuantityChange,
    hasSelectedTickets,
    selectedTickets: tickets,
    getSelectedTicketsArray,
    eventId,
    calculateTotalCost,
    activeDiscount,
  } = usePurchaseTicketContext();
  const selectedTickets = getSelectedTicketsArray();

  const initialTime = getRemainingTimerSeconds({
    expiresAt,
    defaultTimer: 11 * 60,
  });

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useUpdateReserveEventTicket({
      onSuccess: modal.dismiss,
      onError: (error) => toast.error(error.message),
    });

  const [currentStep, setCurrentStep] = React.useState<'summary' | 'payment'>(
    'summary'
  );

  const additionalFees: AdditionalFees = {
    fee: 0.05,
    ...(activeDiscount && {
      discountAmount: toAmountInMajor(activeDiscount.discountAmount),
    }),
    // Add other fees as needed
  };
  const {
    // ticketSubtotal,
    // totalFees,
    total: totalWithDiscount,
  } = calculateTotalCost(additionalFees);
  // const total = ticketSubtotal + totalFees;

  const { mutate: payWithWallet, isPending } = usePurchaseEventTicket();
  const { checkout } = usePaystackCheckout({
    amount: totalWithDiscount,
    ...(activeDiscount && { discountCode: activeDiscount.code }),
  });

  const handlePayment = () => {
    if (paymentMethod === 'wallet') {
      payWithWallet(
        {
          id: event?.id || eventId,
          category: selectedTickets.map(
            ({ category, quantity, isPresale }) => ({
              category,
              quantity,
              isPresale,
            })
          ),
          userCurrency: 'NGN',
          ...(activeDiscount && { discountCode: activeDiscount.code }),
        },
        {
          onSuccess: (tickets) => {
            queryClient.invalidateQueries({
              queryKey: ['getUserTickets', user?.id],
            });
            queryClient.invalidateQueries({
              queryKey: ['getEvent', event?.id],
            });
            queryClient.invalidateQueries({
              queryKey: ['getEventWithSlug', event?.slug],
            });
            router.replace({
              pathname: '/events/[id]/tickets/success',
              params: { id: event?.id || '', ticketId: tickets[0].id },
            });
          },
          onError: (error) => toast.error(error.message),
        }
      );
    } else {
      checkout();
    }
  };

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back
          onBackPress={() => {
            if (currentStep === 'payment') {
              setCurrentStep('summary');
            } else {
              router.back();
            }
          }}
        />
      </View>
      <H1 className="px-4 pb-4">Checkout</H1>
      <View className="items-center justify-center px-6 py-2">
        <CheckoutTimer initialTime={initialTime} />
      </View>

      {currentStep === 'summary' ? (
        <CheckoutSummary openAddTicketModal={modal.present} />
      ) : (
        <PaymentMethod />
      )}

      <Button
        label="Continue"
        className="mx-4 mt-auto"
        disabled={selectedTickets.length < 1 || isPending}
        loading={isPending}
        onPress={() => {
          if (currentStep === 'payment') {
            handlePayment();
          } else {
            setCurrentStep('payment');
          }
        }}
      />

      <Modal ref={modal.ref} enableDynamicSizing hasHandle={false}>
        <BottomSheetView
          className="relative min-h-80 flex-1 items-center justify-end rounded-t-lg pt-4"
          style={{ paddingBottom: insets.bottom }}
        >
          <GetTicketModal
            visible
            onClose={modal.dismiss}
            event={event}
            handleTicketQuantityChange={handleTicketQuantityChange}
            hasSelectedTickets={hasSelectedTickets}
            selectedTickets={tickets}
            onProceed={() =>
              reserveTicket({
                id: eventId,
                reservations: selectedTickets
                  .filter(({ quantity }) => quantity !== 0)
                  .map(({ category, quantity }) => ({
                    category,
                    quantity,
                  })),
              })
            }
            isLoading={reservingTicket}
          />
        </BottomSheetView>
      </Modal>
    </SafeAreaView>
  );
}
