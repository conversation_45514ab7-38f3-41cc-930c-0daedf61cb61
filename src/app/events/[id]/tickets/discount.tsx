import { useRouter } from 'expo-router';
import { FormProvider, useFormContext } from 'react-hook-form';
import { toast } from 'sonner-native';

import { useValidateEventDiscount } from '@/api/events';
import { AddDiscountForm } from '@/components/forms/add-discount';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button } from '@/components/ui';
import { toAmountInMinor, usePurchaseTicketContext } from '@/lib';
import { type PurchaseTicketFormType } from '@/lib/hooks/use-purchase-ticket';

export default function AddDiscount() {
  const router = useRouter();
  const formMethods = useFormContext<PurchaseTicketFormType>();
  const {
    eventId,
    getSelectedTicketsArray,
    calculateTotalCost,
    setActiveDiscount,
  } = usePurchaseTicketContext();
  const selectedTickets = getSelectedTicketsArray();
  const { ticketSubtotal } = calculateTotalCost();

  const { isPending, mutate: validateDiscount } = useValidateEventDiscount({
    onSuccess: ({ discount, discountAmount }) => {
      setActiveDiscount({ ...discount, discountAmount });
      toast.success('discount validated successfully');
      router.back();
    },
    onError: (error) => toast.error(error.message),
  });

  return (
    <AuthLayout
      title="Add discount"
      footer={
        <Button
          testID="add-discount-button"
          label="Continue"
          className="my-4"
          onPress={() => {
            validateDiscount({
              code: formMethods.watch('discountCode') || '',
              eventId,
              originalPrice: toAmountInMinor(ticketSubtotal),
              ticketCategory: selectedTickets.map(({ category }) => category),
            });
          }}
          loading={isPending}
          disabled={isPending}
        />
      }
    >
      <FormProvider {...formMethods}>
        <AddDiscountForm />
      </FormProvider>
    </AuthLayout>
  );
}
