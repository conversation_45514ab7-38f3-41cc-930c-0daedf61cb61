import { useRouter } from 'expo-router';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import GetTicketModal from '@/components/modals/get-ticket';
import { FocusAwareStatusBar, View } from '@/components/ui';
import { usePurchaseTicketContext } from '@/lib/contexts/purchase-ticket-context';

export default function GetTickets() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [modalVisible, setModalVisible] = React.useState(true);

  const {
    event,
    handleTicketQuantityChange,
    hasSelectedTickets,
    selectedTickets,
  } = usePurchaseTicketContext();

  const handleCloseModal = () => {
    setModalVisible(false);
    router.back();
  };

  return (
    <View className="flex-1">
      <FocusAwareStatusBar />
      <View
        className="relative flex-1 items-center justify-end"
        style={{ paddingBottom: insets.bottom }}
      >
        <GetTicketModal
          event={event}
          handleTicketQuantityChange={handleTicketQuantityChange}
          hasSelectedTickets={hasSelectedTickets}
          visible={modalVisible}
          onClose={handleCloseModal}
          selectedTickets={selectedTickets}
          onProceed={() => {
            router.push('/events/tickets/checkout');
          }}
        />
      </View>
    </View>
  );
}
