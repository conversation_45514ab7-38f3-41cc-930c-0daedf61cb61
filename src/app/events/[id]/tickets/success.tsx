import { BlurView } from 'expo-blur';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  Button,
  colors,
  FocusAwareStatusBar,
  H2,
  Image,
  P,
  View,
} from '@/components/ui';

export default function TicketPurchaseSuccess() {
  const { ticketId } = useLocalSearchParams<{
    ticketId?: string;
  }>();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [_modalVisible, setModalVisible] = React.useState(true);

  const styles = styling(isDark);

  const handleCloseModal = () => {
    setModalVisible(false);
    router.push('/(app)');
  };

  const handleViewTicket = () => {
    setModalVisible(false);
    router.dismissAll();
    if (ticketId) {
      router.push({
        pathname: '/events/tickets/[id]',
        params: { id: ticketId },
      });
    } else {
      console.log('push to list');
      router.push('/events/tickets');
    }
  };

  return (
    <View className="flex-1">
      <FocusAwareStatusBar />
      <View
        className="relative flex-1 items-center justify-center"
        style={{ paddingBottom: insets.bottom }}
      >
        <BlurView
          tint={isDark ? 'systemMaterialLight' : 'extraLight'}
          intensity={isDark ? 20 : 70}
          style={styles.blurView}
        >
          <View style={styles.overlay} />
        </BlurView>

        <View className="flex-1 items-center justify-center">
          <Image
            source={require('~/assets/images/success.png')}
            className="h-52 w-[211px]"
          />
          <View className="items-center gap-2">
            <H2>Thank you!</H2>
            <P>Your ticket purchase was successful</P>
          </View>
        </View>

        <View className="mt-auto w-full gap-2 px-4">
          <Button label="View ticket" onPress={handleViewTicket} />
          <Button label="Home" variant="outline" onPress={handleCloseModal} />
        </View>
      </View>
    </View>
  );
}

const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
