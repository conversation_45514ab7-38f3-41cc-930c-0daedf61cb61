import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';

import { useCreateEventForm } from '@/lib';

export default function EventCreateLayout() {
  const { formMethods } = useCreateEventForm();

  return (
    <FormProvider {...formMethods}>
      <Stack>
        <Stack.Screen
          name="index"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="choose-format"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="add-ticket"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="add-address"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="choose-ticket-type"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="add-details"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="add-collaborators"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="hybrid"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        {/* <Stack.Screen
          name="zoom-generated"
          options={{
            headerShown: false,
            presentation: 'containedModal',
            animation: 'slide_from_bottom',
          }}
        /> */}
        <Stack.Screen
          name="add-banner"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="summary"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="tickets"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="edit-ticket/[id]"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="free-event"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
        <Stack.Screen
          name="free-event-next"
          options={{ headerShown: false, animation: 'slide_from_right' }}
        />
      </Stack>
    </FormProvider>
  );
}
