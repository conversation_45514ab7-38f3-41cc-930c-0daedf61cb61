import { useRouter } from 'expo-router';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { type Point } from 'react-native-google-places-autocomplete';

import LocationInput from '@/components/input/location-autocomplete';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, View } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';
import { type LocationType } from '@/types';

export default function AddAddress() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'location',
  ]);
  const router = useRouter();

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <CreateEventLayout
      title="Add event address"
      subTitle="Where will your event be taking place?"
      footer={
        <Button
          testID="go-to-choose-ticket-type"
          label="Continue"
          className="m-4"
          disabled={!fieldStates.location.isValid}
          onPress={() => router.push('/events/create/choose-ticket-type')}
        />
      }
    >
      <View className="gap-4">
        <LocationInput
          onSelectLocation={onSelectLocation}
          defaultValue={watch('location')}
        />
      </View>
    </CreateEventLayout>
  );
}
