import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { useFormContext } from 'react-hook-form';
import ImageCropPicker, {
  type ImageOrVideo,
} from 'react-native-image-crop-picker';
import { useState } from 'react';

import { AddImageCard } from '@/components/cards/add-image';
import { UploadCard } from '@/components/cards/upload-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  colors,
  Image,
  Pressable,
  Text,
  View,
  WIDTH,
} from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';

export default function AddBanner() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'bannerObj',
  ]);
  const router = useRouter();

  const height = WIDTH * (3 / 4);

  const [status, requestPermission] = ImagePicker.useMediaLibraryPermissions();
  const [userMessage, setUserMessage] = useState('');

  const bannerUri = watch('bannerObj.uri');
  const currentMedia = watch('media') || [];
  const MAX_MEDIA_COUNT = 2;
  const currentMediaCount = currentMedia.length;
  const remainingPhotos = MAX_MEDIA_COUNT - currentMediaCount;

  const getFileName = (path: string) => path.split('/').pop() || '';

  const cropAndFormatImage = async (image: ImageOrVideo) => {
    try {
      const cropped = await ImageCropPicker.openCropper({
        mediaType: 'photo',
        path: image.path,
        cropping: true,
        width: 360,
        height: 450,
        freeStyleCropEnabled: true,
        compressImageQuality: 0.8,
      });

      return {
        uri: cropped.path,
        type: cropped.mime || 'image/jpeg',
        name: getFileName(cropped.path),
        size: cropped.size,
      };
    } catch (error) {
      console.warn('Error cropping image:', error);
      setUserMessage('Failed to crop the image. Please try another one.');
      return null;
    }
  };

  const requestMediaPermission = async () => {
    if (!status?.granted) {
      const permission = await requestPermission();
      if (!permission.granted) {
        setUserMessage(
          'Permission denied. We need access to your photos to select an image.'
        );
        return false;
      }
    }
    return true;
  };

  const pickImage = async (options: {
    type: 'banner' | 'media';
    index?: number;
  }) => {
    setUserMessage('');
    const permissionGranted = await requestMediaPermission();
    if (!permissionGranted) return;

    try {
      if (options.type === 'banner') {
        const result = await ImageCropPicker.openPicker({
          mediaType: 'photo',
          multiple: false,
        });

        const imageData = await cropAndFormatImage(result);
        if (imageData) {
          setValue('bannerObj', imageData);
        }
      } else if (options.type === 'media') {
        const maxSelectable = Math.max(0, MAX_MEDIA_COUNT - currentMediaCount);
        if (maxSelectable === 0) {
          setUserMessage(
            `You have reached the maximum of ${MAX_MEDIA_COUNT} photos.`
          );
          return;
        }

        const result = await ImageCropPicker.openPicker({
          mediaType: 'photo',
          multiple: true,
          maxFiles: options.index === undefined ? maxSelectable : 1,
        });

        const imagesToProcess = Array.isArray(result) ? result : [result];
        let updatedMedia = [...currentMedia];

        for (const image of imagesToProcess) {
          const newImageData = await cropAndFormatImage(image);
          if (newImageData) {
            if (options.index !== undefined) {
              updatedMedia[options.index] = newImageData;
              break;
            } else {
              updatedMedia.push(newImageData);
            }
          }
        }
        setValue('media', updatedMedia);
      }
    } catch (pickerError) {
      console.warn('Error picking image:', pickerError);
      setUserMessage('Something went wrong. Please try again.');
    }
  };

  return (
    <CreateEventLayout
      title="Add event image"
      subTitle="Add an event image"
      footer={
        <Button
          testID="add-banner-continue-button"
          label="Continue"
          className="m-4"
          disabled={!fieldStates['bannerObj'].isValid}
          onPress={() => router.push('/events/create/choose-format')}
        />
      }
    >
      <View className="gap-4">
        {bannerUri ? (
          <View className="relative w-full overflow-hidden rounded-lg">
            <Image
              source={bannerUri}
              style={{ width: WIDTH, height, borderRadius: 12 }}
              contentFit="cover"
            />
            <Button
              onPress={() => pickImage({ type: 'banner' })}
              variant="secondary"
              label="Change"
              className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-medium"
            />
          </View>
        ) : (
          <UploadCard
            onPress={() => pickImage({ type: 'banner' })}
            icon={require('~/assets/icons/upload.png')}
            title="Upload photos of the event image"
          />
        )}
        {userMessage && (
          <Text className="text-fg-danger-light dark:text-fg-danger-dark text-sm mt-2">
            {userMessage}
          </Text>
        )}
        <View className="flex-row flex-wrap gap-x-3">
          {currentMedia.map((image, index) => (
            <AddImageCard<CreateEventFormType, 'media'>
              key={image.name}
              name="media"
              photo={image}
              photos={currentMedia}
              setValue={setValue}
              onImageChange={() =>
                pickImage({
                  type: 'media',
                  index,
                })
              }
            />
          ))}
          {remainingPhotos > 0 && bannerUri && (
            <Pressable
              className="size-20 items-center justify-end gap-y-2 rounded-lg border border-dashed border-fg-subtle-light p-2 dark:border-fg-subtle-dark"
              onPress={() => pickImage({ type: 'media' })}
            >
              <Ionicons
                name="camera-outline"
                size={16}
                color={colors.grey[70]}
              />
              <Text className="text-center text-[10px]/120 text-grey-70">
                Add up to {remainingPhotos} more photo
                {remainingPhotos !== 1 ? 's' : ''}
              </Text>
            </Pressable>
          )}
        </View>
      </View>
    </CreateEventLayout>
  );
}
