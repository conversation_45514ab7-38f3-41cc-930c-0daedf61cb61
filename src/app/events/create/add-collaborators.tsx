import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback, useMemo } from 'react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'sonner-native';

import {
  type ISingleEvent,
  useEditEvent,
  useSpotifySearchArtist,
} from '@/api/events';
import { SearchPageLayout } from '@/components/layouts/search-page-layout';
import LoadingScreen from '@/components/loading';
import {
  Divider,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { type CreateEventFormType, useAuth, useInitializeSpotify } from '@/lib';
import { getSpotifyToken } from '@/lib/session/utils';

export default function AddCollaborators() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const { isEdit, eventSlug, eventId } = useLocalSearchParams<{
    isEdit: string;
    eventSlug?: string;
    eventId?: string;
  }>();
  const isEditMode = isEdit?.toString() === 'true';

  const queryClient = useQueryClient();
  const user = useAuth.use.user();

  useInitializeSpotify();

  const spotifyData = getSpotifyToken();

  const [searchQuery, setSearchQuery] = useState('');

  const { data } = useSpotifySearchArtist({
    variables: {
      query: searchQuery,
      accessToken: spotifyData?.access_token || '',
    },
    enabled: !!spotifyData?.access_token && !!searchQuery,
  });
  const artists = data?.artists?.items ?? [];

  const selectedCollaborators = useMemo(
    () => watch('collaborators') || [],
    [watch]
  );

  const cachedEvent: ISingleEvent | undefined = queryClient.getQueryData([
    'getEventWithSlug',
    { slug: eventSlug, targetCurrency: 'NGN', userId: user?.id },
  ]);

  const { mutate: editEvent, isPending } = useEditEvent({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          'getEventWithSlug',
          { slug: eventSlug, targetCurrency: 'NGN', userId: user?.id },
        ],
      });
      toast.success('Event updated successfully');
      router.back();
    },
    onError: (error) => toast.error(error.message),
  });

  const selectCollaborator = useCallback(
    (collaborator: { id: string; name: string; avatar: string }) => {
      if (isEditMode) {
        const formData = new FormData();
        const artists = [
          ...(cachedEvent?.artists || []).map(
            ({ id, avatar, artistName: name }) => ({ id, name, avatar })
          ),
          collaborator,
        ];
        artists
          .map(({ id, avatar, name }) => ({
            spotifyArtistId: id,
            avatar,
            artistName: name,
            eventId: eventId || 'evt_123abc',
          }))
          .forEach((artist, index) => {
            Object.entries(artist).forEach(([key, value]) => {
              formData.append(`artists[${index}][${key}]`, value);
            });
          });

        editEvent({ form: formData, id: eventId || '' });
      } else {
        setValue('collaborators', [...selectedCollaborators, collaborator]);
        router.back();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setValue, selectedCollaborators, router]
  );

  const filteredCollaborators = Array.isArray(artists)
    ? artists
        .filter((artist: any) => !selectedCollaborators.includes(artist.id))
        .map((artist: any) => ({
          id: artist.id,
          name: artist.name,
          avatar: artist.images?.[0]?.url || '',
        }))
    : [];

  if (isPending) return <LoadingScreen />;

  return (
    <SearchPageLayout
      placeholder="Search artiste"
      searchValue={searchQuery}
      onSearchChange={setSearchQuery}
    >
      <Divider />
      <ScrollView
        className="flex-1 gap-4 px-4"
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-4">
          {filteredCollaborators.map((collaborator) => (
            <TouchableOpacity
              onPress={() => selectCollaborator(collaborator)}
              className="flex-row items-center gap-2"
              key={collaborator.id}
            >
              <Image
                source={{ uri: collaborator.avatar }}
                className="size-10 rounded-full"
              />
              <Text className="flex-1 dark:text-neutral-100">
                {collaborator.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SearchPageLayout>
  );
}
