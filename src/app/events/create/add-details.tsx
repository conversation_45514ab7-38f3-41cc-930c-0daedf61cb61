import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

import { type IEventCategories, useGetEventCategories } from '@/api/events';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import CollaboratorCard from '@/components/events/collaborators';
import { CreateEventLayout } from '@/components/layouts';
import LoadingScreen from '@/components/loading';
import { ControlledSelect, P, PillOption, ScrollView } from '@/components/ui';
import {
  Button,
  colors,
  ControlledInput,
  Modal,
  Pressable,
  Text,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';
import TIMEZONES_JSON from '@/lib/timezones.json';
import { formatDateTime } from '@/lib/utils/formatDateTime';

const timezones = TIMEZONES_JSON.map(({ text, value }) => ({
  label: text,
  value,
}));

export default function AddDetails() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<CreateEventFormType>([
      'title',
      'description',
      'timezone',
      'category',
      'startDatetime',
      'endDatetime',
      'collaborators',
    ]);

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const [dateValidationMessage, setDateValidationMessage] = useState('');

  const [dateTimePicker, setDateTimePicker] = useState({
    isVisible: false,
    mode: 'start' as 'start' | 'end',
  });

  const { data: categories, isLoading: categoriesLoading } =
    useGetEventCategories();
  const router = useRouter();

  const startDatetime = watch('startDatetime');
  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch('endDatetime');
  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const handleDateConfirm = (date: Date) => {
    if (dateTimePicker.mode === 'start') {
      if (date < new Date()) {
        setDateValidationMessage('Start date cannot be in the past.');
        setDateTimePicker({ ...dateTimePicker, isVisible: false });
        return;
      }
      setValue(`startDatetime`, date);
    } else {
      const start = watch(`startDatetime`);
      if (!start) {
        setDateValidationMessage(
          'You need to pick event start date and time first.'
        );
        setDateTimePicker({ ...dateTimePicker, isVisible: false });
        return;
      }
      if (start > date) {
        setDateValidationMessage('End date cannot be before the start date.');
        setDateTimePicker({ ...dateTimePicker, isVisible: false });
        return;
      }
      setValue(`endDatetime`, date);
    }
    setDateValidationMessage('');
    setDateTimePicker({ ...dateTimePicker, isVisible: false });
  };

  const categoryModal = useModal();
  const selectedCategory = watch('category');
  const timezone = watch('timezone');
  const categoryLabel = categories?.find(
    (c) => c.id === selectedCategory
  )?.category;
  const artists = watch('collaborators') || [];

  const selectCategory = useCallback(
    (category: IEventCategories) => {
      const currentCategory = watch('category');
      if (currentCategory === category.id) {
        setValue('category', '');
      } else {
        setValue('category', category.id);
      }
    },
    [setValue, watch]
  );

  const confirmCategory = useCallback(() => {
    handleFieldBlur('category');
    categoryModal.dismiss();
  }, [handleFieldBlur, categoryModal]);

  return (
    <CreateEventLayout
      title="Tell us about your event"
      subTitle="Add details to make your event stand out."
      footer={
        <Button
          testID="account-selection-button"
          label="Continue"
          className="mx-4"
          disabled={
            !fieldStates.title.isValid ||
            !fieldStates.description.isValidOptional ||
            !fieldStates.collaborators.isValidOptional ||
            !fieldStates.startDatetime.isValid ||
            !fieldStates.endDatetime.isValid ||
            !fieldStates.timezone.isValid ||
            !fieldStates.category.isValid
          }
          onPress={() => router.push('/events/create/add-banner')}
        />
      }
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="gap-4">
          <ControlledInput
            name="title"
            handleFieldBlur={() => handleFieldBlur('title')}
            handleFieldUnBlur={() => handleFieldUnBlur('title')}
            label="Event title"
            control={control}
          />
          <ControlledInput
            name="description"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            handleFieldBlur={() => handleFieldBlur('description')}
            handleFieldUnBlur={() => handleFieldUnBlur('description')}
            style={{ height: 80 }}
            label="Describe your event"
            control={control}
          />

          <Pressable
            testID="select-event-start-date-time-button"
            className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            onPress={() =>
              setDateTimePicker({ isVisible: true, mode: 'start' })
            }
          >
            <Text
              className={
                startDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {startDatetimeLabel}
            </Text>
          </Pressable>

          <Pressable
            testID="select-event-end-date-time-button"
            className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            onPress={() => setDateTimePicker({ isVisible: true, mode: 'end' })}
          >
            <Text
              className={
                endDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {endDatetimeLabel}
            </Text>
          </Pressable>

          {dateValidationMessage && (
            <P className="text-fg-danger-light dark:text-fg-danger-dark text-sm">
              {dateValidationMessage}
            </P>
          )}

          <DateTimePickerModal
            isVisible={dateTimePicker.isVisible}
            mode="datetime"
            onConfirm={handleDateConfirm}
            onCancel={() =>
              setDateTimePicker({ ...dateTimePicker, isVisible: false })
            }
            disabled={dateTimePicker.mode === 'end' && !startDatetime}
            minimumDate={
              dateTimePicker.mode === 'end'
                ? startDatetime || new Date()
                : new Date()
            }
          />

          <ControlledSelect
            control={control}
            name="timezone"
            title="Choose timezone"
            placeholder="Timezone"
            options={timezones}
            value={timezone}
          />
          <Pressable
            testID="select-categories-input"
            className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            onPress={categoryModal.present}
          >
            <View className="flex-row items-center gap-2">
              <Text
                className={
                  selectedCategory
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {categoryLabel || 'Category'}
              </Text>
            </View>
            <Ionicons name="chevron-down" size={24} color={colors.grey[70]} />
          </Pressable>
          {artists.length < 1 && (
            <Button
              testID="account-selection-button"
              label="Add artiste +"
              variant="secondary"
              className="w-[154px] py-2"
              size="sm"
              onPress={() => router.push('/events/create/add-collaborators')}
            />
          )}
        </View>

        {artists.length > 0 && (
          <View className="mt-4 gap-4">
            <P>Lineup</P>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ overflow: 'visible' }}
            >
              <View className="flex-row items-center gap-2">
                {artists.map((artist) => (
                  <CollaboratorCard
                    key={artist.id}
                    artist={artist}
                    onRemove={() => {
                      setArtistToDelete(artist.id);
                      setConfirmVisible(true);
                    }}
                  />
                ))}
              </View>
            </ScrollView>
          </View>
        )}
      </ScrollView>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this artist?"
        onCancel={() => {
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            setTimeout(() => {
              setValue(
                'collaborators',
                artists.filter((a) => a.id !== artistToDelete)
              );
            }, 0);
          }
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
      />

      <Modal ref={categoryModal.ref} enableDynamicSizing>
        {categoriesLoading ? (
          <LoadingScreen />
        ) : (
          <BottomSheetView className="min-h-20 flex-1 gap-4 px-4 pb-4">
            <View className="mb-4 flex-row items-center justify-start gap-2">
              <TouchableOpacity
                onPress={() => categoryModal.dismiss()}
                accessibilityLabel="Close category modal"
                accessibilityRole="button"
              >
                <Ionicons name="close" size={32} color={colors.grey[70]} />
              </TouchableOpacity>
              <Text className="text-lg font-bold dark:text-neutral-100">
                Select event category
              </Text>
            </View>
            <View className="flex-row flex-wrap gap-2">
              {categories?.map((category) => (
                <PillOption
                  key={category.id}
                  option={category.category}
                  selected={selectedCategory === category.id}
                  onPress={() => selectCategory(category)}
                />
              ))}
            </View>

            <View className="mt-auto pb-4">
              <Button
                label="Confirm"
                disabled={!selectedCategory}
                onPress={confirmCategory}
              />
            </View>
          </BottomSheetView>
        )}
      </Modal>
    </CreateEventLayout>
  );
}
