import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  ControlledBottomSheetInputWithoutContext,
  ControlledInput,
  CostSelector,
  FeatureToggle,
  Modal,
  P,
  Pressable,
  ScrollView,
  semanticColors,
  Text,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import {
  type CreateEventFormType,
  useCreateEventForm,
  useFieldBlurAndFilled,
} from '@/lib';
import { formatDate, formatDateTime } from '@/lib/utils/formatDateTime';

export default function AddTicket() {
  const { watch, control, setValue, getValues, subscribe } =
    useFormContext<CreateEventFormType>();
  const { defaultTicket } = useCreateEventForm();
  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<CreateEventFormType>([
      'tickets.0.name',
      'tickets.0.price',
      'tickets.0.quantity',
      'tickets.0.presaleName',
      'tickets.0.presalePrice',
      'tickets.0.presaleQuantity',
      'tickets.0.presaleStartDatetime',
      'tickets.0.presaleEndDatetime',
      'tickets.0.purchaseLimit',
      'tickets.0.description',
      'tickets.0.startDatetime',
      'tickets.0.endDatetime',
    ]);
  const { append, update } = useFieldArray({
    control,
    name: 'tickets',
  });

  const router = useRouter();

  // can enable all features
  const name = watch(`tickets.0.name`);
  const price = watch(`tickets.0.price`);
  const quantity = watch(`tickets.0.quantity`);
  const canEnable = name && price && quantity;

  // presale toggle and modal
  const [hasPresale, setHasPresale] = useState(watch(`tickets.0.hasPresale`));
  const presaleModal = useModal();
  function enablePresale() {
    setHasPresale(true);
    setValue(`tickets.0.hasPresale`, true);
    setValue(`tickets.0.presalePrice`, 0);
    setValue(`tickets.0.presaleQuantity`, 0);
    setValue('tickets.0.presaleName', getValues('tickets.0.name') + ' - ');
    setValue(`tickets.0.presaleDescription`, '');
  }

  function disablePresale() {
    if (watch(`tickets.0.hasPresale`)) {
      setHasPresale(false);
      setValue(`tickets.0.hasPresale`, false, { shouldValidate: true });
    }
  }

  const togglePresale = (checked: boolean) => {
    if (canEnable && checked) {
      enablePresale();
      presaleModal.present();
    } else {
      disablePresale();
      presaleModal.dismiss();
    }
  };

  const handleAddPresale = () => {
    presaleModal.dismiss();
  };

  const handlePresaleModalDismiss = () => {
    if (
      !watch('tickets.0.presaleName') ||
      !watch('tickets.0.presalePrice') ||
      !watch('tickets.0.presaleStartDatetime') ||
      !watch('tickets.0.presaleEndDatetime') ||
      !watch('tickets.0.presaleQuantity')
    ) {
      disablePresale();
    }
  };

  const presaleNameTouched = useRef(false);

  useEffect(() => {
    const unsub = subscribe({
      name: 'tickets.0.name',
      callback: ({ values }) => {
        const ticket = values.tickets?.[0];
        if (ticket?.hasPresale) {
          const name = ticket.name;
          const currentPresaleName =
            'presaleName' in ticket ? ticket.presaleName : undefined;
          const generatedPresaleName = name ? `${name} - ` : '';

          if (
            name &&
            !presaleNameTouched.current &&
            currentPresaleName !== generatedPresaleName
          ) {
            setValue('tickets.0.presaleName', generatedPresaleName);
          }
        }
      },
    });

    return () => unsub();
  }, [subscribe, setValue]);

  const eventEnd = watch('endDatetime');

  // presale datetimes
  const [openPresaleStartDatetime, setOpenPresaleStartDatetime] =
    useState(false);
  const [openPresaleEndDatetime, setOpenPresaleEndDatetime] = useState(false);

  const presaleStartDatetime = watch(`tickets.0.presaleStartDatetime`);

  const presaleStartDatetimeLabel = presaleStartDatetime
    ? formatDateTime(presaleStartDatetime)
    : 'Start date and time';

  const presaleEndDatetime = watch(`tickets.0.presaleEndDatetime`);

  const presaleEndDatetimeLabel = presaleEndDatetime
    ? formatDateTime(presaleEndDatetime)
    : 'End date and time';

  const handlePresaleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.0.presaleStartDatetime`, date);
    setOpenPresaleStartDatetime(false);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.0.presaleStartDatetime`);
    if (!start || date <= start) {
      alert('End date cannot be before the start date.');
      return;
    }
    if (eventEnd && date > new Date(eventEnd)) {
      alert('Presale ticket end date cannot be after event end date.');
      return;
    }
    setValue(`tickets.0.presaleEndDatetime`, date);
    setOpenPresaleEndDatetime(false);
  };

  //timeline
  const timelineModal = useModal();
  const [hasTimeline, setHasTimeline] = useState(
    watch(`tickets.0.hasTimeline`)
  );

  const handleAddTimeline = () => {
    timelineModal.dismiss();
  };

  const handleTimelineModalDismiss = () => {
    if (!watch('tickets.0.startDatetime') || !watch('tickets.0.endDatetime')) {
      setHasTimeline(false);
      setValue(`tickets.0.hasTimeline`, false);
    }
  };

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setHasTimeline(true);
      setValue(`tickets.0.hasTimeline`, true);
      timelineModal.present();
    } else {
      setHasTimeline(false);
      setValue(`tickets.0.hasTimeline`, false);
    }
  };

  // timeline datetimes
  const [openStartDatetime, setOpenStartDatetime] = useState(false);
  const [openEndDatetime, setOpenEndDatetime] = useState(false);

  const startDatetime = watch(`tickets.0.startDatetime`);

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch(`tickets.0.endDatetime`);

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const handleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.0.startDatetime`, date);
    setOpenStartDatetime(false);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch('tickets.0.startDatetime');

    if (!start || date <= start) {
      alert('End date cannot be before the start date.');
      return;
    }

    if (eventEnd && date > new Date(eventEnd)) {
      alert('Ticket end date cannot be after event end date.');
      return;
    }

    setValue('tickets.0.endDatetime', date);
    setOpenEndDatetime(false);
  };

  //Purchase limit
  const [localPurchaseLimit, setLocalPurchaseLimit] = useState(10);
  const [hasPurchaseLimit, setHasPurchaseLimit] = useState(
    watch(`tickets.0.hasPurchaseLimit`)
  );
  const purchaseLimitModal = useModal();

  const handleAddPurchaseLimit = () => {
    purchaseLimitModal.dismiss();
  };

  const handlePurchaseLimitModalDismiss = () => {
    if (!watch('tickets.0.purchaseLimit')) {
      setHasPurchaseLimit(false);
      setValue(`tickets.0.hasPurchaseLimit`, false);
    }
  };

  const togglePurchaseLimit = (checked: boolean) => {
    if (canEnable && checked) {
      setHasPurchaseLimit(true);
      setValue(`tickets.0.hasPurchaseLimit`, true);
      purchaseLimitModal.present();
    } else {
      setHasPurchaseLimit(false);
      setValue(`tickets.0.hasPurchaseLimit`, false);
    }
  };

  const handleAddTicket = () => {
    append(watch('tickets.0'));
    update(0, defaultTicket);
    router.push('/events/create/tickets');
  };

  // Presale purchase limit
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] = useState(
    watch(`tickets.0.presalePurchaseLimit`) || 10
  );

  return (
    <CreateEventLayout
      title="Set up tickets"
      subTitle="You can create multiple ticket types with varying prices."
      footer={
        <Button
          testID="go-to-tickets-page"
          label="Continue"
          className="m-4"
          disabled={
            !fieldStates['tickets.0.price'].isValid ||
            !watch(`tickets.0.name`) ||
            !watch(`tickets.0.price`) ||
            !watch(`tickets.0.quantity`)
          }
          onPress={() => {
            handleAddTicket();
          }}
        />
      }
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="gap-4">
          <ControlledInput
            name={`tickets.0.name`}
            label="Ticket name e.g General admission, VIP"
            handleFieldBlur={() => handleFieldBlur('tickets.0.name')}
            handleFieldUnBlur={() => handleFieldUnBlur('tickets.0.name')}
            control={control}
          />

          <CostSelector
            control={control}
            setValue={setValue}
            name={`tickets.0.price`}
            handleFieldBlur={() => handleFieldBlur('tickets.0.price')}
            handleFieldUnBlur={() => handleFieldUnBlur('tickets.0.price')}
            label="Price"
            costOptions={[5000, 10000, 20000, 50000]}
            testID="ticket-cost-input"
            keyboardType="numeric"
          />

          <ControlledInput
            name={`tickets.0.quantity`}
            keyboardType="numeric"
            label="Quantity available"
            handleFieldBlur={() => handleFieldBlur('tickets.0.quantity')}
            handleFieldUnBlur={() => handleFieldUnBlur('tickets.0.quantity')}
            control={control}
            onChangeText={(qty) => setValue('tickets.0.quantity', Number(qty))}
          />

          <ControlledInput
            name={`tickets.0.description`}
            label="Ticket description (optional)"
            handleFieldBlur={() => handleFieldBlur('tickets.0.description')}
            handleFieldUnBlur={() => handleFieldUnBlur('tickets.0.description')}
            control={control}
          />
          <FeatureToggle
            title="Enable purchase limit"
            subtitle="Limit ticket per order, default uses a maximum of 10"
            checked={hasPurchaseLimit}
            onChange={togglePurchaseLimit}
            {...(hasPurchaseLimit && {
              editText: `Maximum of ${localPurchaseLimit} ticket(s)`,
              onEditPress: () => purchaseLimitModal.present(),
            })}
            accessibilityLabel="Enable purchase limit"
          />
          <FeatureToggle
            title="Include ticket sale timeline"
            subtitle="Set specific period for ticket sale, default uses event date and time"
            checked={hasTimeline}
            onChange={toggleTimeline}
            {...(watch(`tickets.0.hasTimeline`) &&
              startDatetime &&
              endDatetime && {
                editText: `${formatDate(startDatetime)} - ${formatDate(endDatetime)}`,
                onEditPress: () => timelineModal.present(),
              })}
            accessibilityLabel="Include ticket sale timeline"
          />
          <FeatureToggle
            title="Offer ticket presale"
            checked={hasPresale}
            onChange={togglePresale}
            {...(watch(`tickets.0.hasPresale`) && {
              editText: 'Edit presale ticket',
              onEditPress: () => presaleModal.present(),
            })}
            accessibilityLabel="Offer ticket presale"
          />
        </View>
      </ScrollView>

      <Modal
        ref={presaleModal.ref}
        enableDynamicSizing
        onDismiss={handlePresaleModalDismiss}
      >
        <BottomSheetView className="min-h-30 pb-6">
          <View className="gap-4 px-4">
            <View className="flex-row items-center justify-start gap-2">
              <TouchableOpacity
                onPress={presaleModal.dismiss}
                className=""
                accessibilityLabel="Close select modal"
                accessibilityRole="button"
              >
                <Ionicons
                  name="close"
                  size={32}
                  color={semanticColors.fg.subtle.dark}
                />
              </TouchableOpacity>
              <Text className="text-lg font-bold dark:text-neutral-100">
                Set up presale
              </Text>
            </View>
            <ControlledBottomSheetInputWithoutContext
              name={`tickets.0.presaleName`}
              onChangeText={(text) => {
                presaleNameTouched.current = true;
                setValue('tickets.0.presaleName', text);
              }}
              label="Ticket name e.g General admission, VIP"
              handleFieldBlur={() => handleFieldBlur('tickets.0.presaleName')}
              handleFieldUnBlur={() =>
                handleFieldUnBlur('tickets.0.presaleName')
              }
              control={control}
            />
            <ControlledBottomSheetInputWithoutContext
              name={`tickets.0.presalePrice`}
              handleFieldBlur={() => handleFieldBlur('tickets.0.presalePrice')}
              handleFieldUnBlur={() =>
                handleFieldUnBlur('tickets.0.presalePrice')
              }
              label="Price"
              control={control}
            />

            <Pressable
              testID="select-presale-start-date-time-button"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={() => setOpenPresaleStartDatetime(true)}
            >
              <Text
                className={
                  presaleStartDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {presaleStartDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openPresaleStartDatetime}
              mode="datetime"
              onConfirm={handlePresaleStartDateConfirm}
              onCancel={() => setOpenPresaleStartDatetime(false)}
              minimumDate={new Date()}
            />
            <Pressable
              testID="select-presale-end-date-time-button"
              className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
                presaleStartDatetime
                  ? 'border-border-subtle-light dark:border-border-subtle-dark'
                  : 'border-neutral-200 opacity-50 dark:border-neutral-700'
              }`}
              disabled={!presaleStartDatetime}
              onPress={() => {
                if (presaleStartDatetime) setOpenPresaleEndDatetime(true);
              }}
            >
              <Text
                className={
                  presaleEndDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {presaleEndDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openPresaleEndDatetime}
              mode="datetime"
              onConfirm={handlePresaleEndDateConfirm}
              onCancel={() => setOpenPresaleEndDatetime(false)}
              disabled={!presaleStartDatetime}
              minimumDate={presaleStartDatetime}
            />

            <ControlledBottomSheetInputWithoutContext
              name={`tickets.0.presaleQuantity`}
              handleFieldBlur={() =>
                handleFieldBlur('tickets.0.presaleQuantity')
              }
              handleFieldUnBlur={() =>
                handleFieldUnBlur('tickets.0.presaleQuantity')
              }
              label="Quantity available"
              control={control}
            />
            <ControlledBottomSheetInputWithoutContext
              name={`tickets.0.presaleDescription`}
              handleFieldBlur={() =>
                handleFieldBlur('tickets.0.presaleDescription')
              }
              handleFieldUnBlur={() =>
                handleFieldUnBlur('tickets.0.presaleDescription')
              }
              label="Ticket description (optional)"
              control={control}
            />
            <View className="flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
              <Text className="text-base font-medium dark:text-neutral-100">
                Max quantity per order
              </Text>

              <View className="flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600">
                <TouchableOpacity
                  onPress={() => {
                    if (localPresalePurchaseLimit > 1) {
                      setLocalPresalePurchaseLimit(
                        localPresalePurchaseLimit - 1
                      );
                    }
                  }}
                  accessibilityLabel="Decrease purchase limit"
                >
                  <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                    -
                  </Text>
                </TouchableOpacity>

                <Text className="px-2 text-base font-semibold dark:text-neutral-100">
                  {localPresalePurchaseLimit}
                </Text>

                <TouchableOpacity
                  onPress={() => {
                    if (localPresalePurchaseLimit < 10) {
                      setLocalPresalePurchaseLimit(
                        localPresalePurchaseLimit + 1
                      );
                    }
                  }}
                  accessibilityLabel="Increase purchase limit"
                >
                  <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                    +
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <Button
            testID="save-presale-button"
            label="Save"
            className="m-4"
            disabled={
              !watch(`tickets.0.presaleEndDatetime`) ||
              !watch(`tickets.0.presaleStartDatetime`) ||
              !watch(`tickets.0.presalePrice`) ||
              !watch(`tickets.0.presaleQuantity`)
            }
            onPress={handleAddPresale}
          />
        </BottomSheetView>
      </Modal>
      <Modal
        ref={purchaseLimitModal.ref}
        enableDynamicSizing
        onDismiss={handlePurchaseLimitModalDismiss}
      >
        <BottomSheetView className="min-h-10 gap-4 px-4 pb-6">
          <View className="flex-row items-center justify-start gap-2">
            <TouchableOpacity
              onPress={purchaseLimitModal.dismiss}
              className=""
              accessibilityLabel="Close select modal"
              accessibilityRole="button"
            >
              <Ionicons
                name="close"
                size={32}
                color={semanticColors.fg.subtle.dark}
              />
            </TouchableOpacity>
            <Text className="text-lg font-bold dark:text-neutral-100">
              Enable purchase limit
            </Text>
          </View>
          <P className="text-sm text-neutral-500 dark:text-neutral-400">
            Set the maximum number of this ticket type a single buyer can
            include in their order
          </P>
          <View className="flex-row items-center justify-between">
            <Text className="text-base font-medium dark:text-neutral-100">
              Number of allowed purchase
            </Text>

            <View className="flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600">
              <TouchableOpacity
                onPress={() => {
                  if (localPurchaseLimit > 1) {
                    setLocalPurchaseLimit(localPurchaseLimit - 1);
                  }
                }}
                accessibilityLabel="Decrease purchase limit"
              >
                <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                  -
                </Text>
              </TouchableOpacity>

              <Text className="px-2 text-base font-semibold dark:text-neutral-100">
                {localPurchaseLimit}
              </Text>

              <TouchableOpacity
                onPress={() => {
                  if (localPurchaseLimit < 10) {
                    setLocalPurchaseLimit(localPurchaseLimit + 1);
                  }
                }}
                accessibilityLabel="Increase purchase limit"
              >
                <Text className="px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate">
                  +
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <Button
            label="Save"
            className="mt-auto"
            onPress={() => {
              setValue('tickets.0.purchaseLimit', localPurchaseLimit);
              handleAddPurchaseLimit();
            }}
          />
        </BottomSheetView>
      </Modal>
      <Modal
        ref={timelineModal.ref}
        enableDynamicSizing
        onDismiss={handleTimelineModalDismiss}
      >
        <BottomSheetView className="gap-4 px-4 pb-6">
          <View className="flex-row items-center justify-start gap-2">
            <TouchableOpacity
              onPress={timelineModal.dismiss}
              className=""
              accessibilityLabel="Close select modal"
              accessibilityRole="button"
            >
              <Ionicons
                name="close"
                size={32}
                color={semanticColors.fg.subtle.dark}
              />
            </TouchableOpacity>
            <Text className="text-lg font-bold dark:text-neutral-100">
              Include ticket sale timeline
            </Text>
          </View>
          <P className="text-sm text-neutral-500 dark:text-neutral-400">
            Set specific period for this ticket type sale
          </P>
          <Pressable
            testID="select-ticket-sale-start-date-time-button"
            className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            onPress={() => setOpenStartDatetime(true)}
          >
            <Text
              className={
                startDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {startDatetimeLabel}
            </Text>
          </Pressable>
          <DateTimePickerModal
            isVisible={openStartDatetime}
            mode="datetime"
            onConfirm={handleStartDateConfirm}
            onCancel={() => setOpenStartDatetime(false)}
            minimumDate={new Date()}
          />
          <Pressable
            testID="select-ticket-sale-end-date-time-button"
            className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
              startDatetime
                ? 'border-border-subtle-light dark:border-border-subtle-dark'
                : 'border-neutral-200 opacity-50 dark:border-neutral-700'
            }`}
            disabled={!startDatetime}
            onPress={() => {
              if (startDatetime) setOpenEndDatetime(true);
            }}
          >
            <Text
              className={
                endDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {endDatetimeLabel}
            </Text>
          </Pressable>
          <DateTimePickerModal
            isVisible={openEndDatetime}
            mode="datetime"
            onConfirm={handleEndDateConfirm}
            onCancel={() => setOpenEndDatetime(false)}
            disabled={!startDatetime}
            minimumDate={startDatetime}
          />
          <Button
            label="Save"
            className="mt-auto"
            onPress={handleAddTimeline}
          />
        </BottomSheetView>
      </Modal>
    </CreateEventLayout>
  );
}
