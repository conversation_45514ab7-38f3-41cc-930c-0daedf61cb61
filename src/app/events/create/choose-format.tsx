import { useRouter } from 'expo-router';
import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  ControlledBottomSheetInputWithoutContext,
  Modal,
  P,
  useModal,
  View,
} from '@/components/ui';
import {
  type CreateEventFormType,
  EVENT_FORMAT_CARDS,
  useFieldBlurAndFilled,
} from '@/lib';
import { BottomSheetView } from '@gorhom/bottom-sheet';

export default function ChooseEventFormat() {
  const { watch, setValue, control } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'eventFormat',
    'onlineUrl',
    'passcode',
  ]);
  const router = useRouter();
  const onlineModal = useModal();

  const handleContinue = () => {
    const eventType = watch('eventFormat');
    if (eventType === 'ONLINE') {
      onlineModal.present();
    } else if (eventType === 'HYBRID') {
      router.push('/events/create/hybrid');
    } else {
      router.push('/events/create/add-address');
    }
  };

  const handleOnlineSave = () => {
    onlineModal.dismiss();
    router.push('/events/create/choose-ticket-type');
  };

  return (
    <CreateEventLayout
      title="How do you want to host this event?"
      subTitle="Choose the format that suits your event."
      footer={
        <Button
          testID="go-to-add-location-page"
          label="Continue"
          className="m-4"
          disabled={!fieldStates.eventFormat.isValid}
          onPress={handleContinue}
        />
      }
    >
      {EVENT_FORMAT_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('eventFormat') === card.id}
          onPress={() => setValue('eventFormat', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}

      <Modal ref={onlineModal.ref} enableDynamicSizing>
        <BottomSheetView className="gap-2 p-4 pb-8">
          <P className="font-bold pb-4">Enter event URL</P>
          <View className="gap-4">
            <ControlledBottomSheetInputWithoutContext
              name="onlineUrl"
              control={control}
              label="Enter link (URL)"
            />
            <ControlledBottomSheetInputWithoutContext
              name="passcode"
              control={control}
              label="Link passcode (optional)"
            />

            <Button
              label="Save"
              className="mt-auto"
              disabled={!fieldStates.onlineUrl.isValid}
              onPress={handleOnlineSave}
            />
          </View>

          <Button
            label="Send link to attendees later"
            className="mt-2"
            variant="outline"
            onPress={handleOnlineSave}
          />
        </BottomSheetView>
      </Modal>
    </CreateEventLayout>
  );
}
