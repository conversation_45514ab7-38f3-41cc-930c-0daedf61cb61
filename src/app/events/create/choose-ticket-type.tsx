import { useRouter } from 'expo-router';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button } from '@/components/ui';
import { type CreateEventFormType, TICKET_TYPE_CARDS } from '@/lib';

export default function ChooseEventTicketType() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const { fields } = useFieldArray({
    control,
    name: 'tickets',
  });

  return (
    <CreateEventLayout
      title="Will your event be free or paid?"
      subTitle="Choose the ticket type that works best for your event."
      footer={
        <Button
          testID="go-to-free-event-or-add-ticket-page"
          label="Continue"
          className="m-4"
          disabled={!watch('ticketType')}
          onPress={() =>
            watch('ticketType') === 'FREE'
              ? router.push('/events/create/free-event')
              : fields.length < 2
                ? router.push('/events/create/add-ticket')
                : router.push('/events/create/tickets')
          }
        />
      }
    >
      {TICKET_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('ticketType') === card.id}
          onPress={() => setValue('ticketType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
