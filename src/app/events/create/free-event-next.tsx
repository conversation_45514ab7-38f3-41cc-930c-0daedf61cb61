import { useRouter } from 'expo-router';
import { useFormContext } from 'react-hook-form';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, ControlledInput, View } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';

export default function FreeEventDetails() {
  const { control, setValue } = useFormContext<CreateEventFormType>();

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<CreateEventFormType>(['maxAttendees']);

  const router = useRouter();

  return (
    <CreateEventLayout
      title="Set up free event"
      subTitle="Choose between requiring attendees to register for the event or just uploading you event."
      footer={
        <Button
          testID="go-to-add-banner-page-button"
          label="Continue"
          className="mx-4"
          disabled={!fieldStates.maxAttendees.isValidOptional}
          onPress={() => router.push('/events/create/summary')}
        />
      }
    >
      <View className="gap-4">
        <ControlledInput
          name="maxAttendees"
          label="Max. number of attendees"
          handleFieldBlur={() => handleFieldBlur('maxAttendees')}
          handleFieldUnBlur={() => handleFieldUnBlur('maxAttendees')}
          control={control}
          onChangeText={(str) => setValue('maxAttendees', Number(str))}
        />
      </View>
    </CreateEventLayout>
  );
}
