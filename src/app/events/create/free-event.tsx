import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useFieldArray } from 'react-hook-form';
import { Alert } from 'react-native';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { CloseButton, FeatureToggle } from '@/components/ui';
import {
  Button,
  colors,
  Modal,
  Select,
  Text,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { BottomSheetInput } from '@/components/ui/bottom-sheet-input';
import { type CreateEventFormType } from '@/lib';

const FIELD_TYPES = [
  { label: 'Alpha-numeric', value: 'alpha-numeric' },
  { label: 'Numeric', value: 'numeric' },
  { label: 'Age range selector', value: 'age-range-selector' },
  { label: 'Country selector', value: 'country-selector' },
  { label: 'Sex selector', value: 'sex-selector' },
];

export default function FreeEventDetails() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'registrationFields',
  });

  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] = useState<
    | 'alpha-numeric'
    | 'numeric'
    | 'age-range-selector'
    | 'country-selector'
    | 'sex-selector'
  >('alpha-numeric');

  const handleAddField = () => {
    if (!fieldName || !fieldType) return;

    const isDuplicate = fields.some((field) => field.name === fieldName);
    if (isDuplicate) {
      Alert.alert('Field already exists', 'Please use a unique field name.');
      return;
    }

    append({
      name: fieldName,
      type: fieldType,
    });

    setFieldName('');
    setFieldType('alpha-numeric');

    fieldsModal.dismiss();
  };

  const handleToggle = (checked: boolean) => {
    if (checked) {
      setValue('isRegistrationRequired', true);
    } else {
      setValue('isRegistrationRequired', false);
    }
  };

  const router = useRouter();

  const fieldsModal = useModal();

  return (
    <CreateEventLayout
      title="Set up free event"
      subTitle="Choose between requiring attendees to register for the event or just uploading you event."
      footer={
        <Button
          testID="account-selection-button"
          label="Continue"
          className="mx-4"
          onPress={() => router.push('/events/create/free-event-next')}
        />
      }
    >
      <View className="gap-4">
        <FeatureToggle
          onChange={handleToggle}
          accessibilityLabel="Enable registration"
          checked={watch('isRegistrationRequired') ? true : false}
          title="Enable registration"
        />
        {watch('isRegistrationRequired') && (
          <>
            <View className="min-h-12 flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
              <Text className="text-neutral-400 dark:text-neutral-500">
                Full name
              </Text>
              <Text className="text-neutral-400 dark:text-neutral-500">
                (Required)
              </Text>
            </View>
            <View className="min-h-12 flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
              <Text className="text-neutral-400 dark:text-neutral-500">
                Email
              </Text>
              <Text className="text-neutral-400 dark:text-neutral-500">
                (Required)
              </Text>
            </View>
            {fields.map((field, index) => (
              <View
                key={field.name}
                className="relative min-h-12 flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              >
                <Text className="text-neutral-400 dark:text-neutral-500">
                  {field.name}
                </Text>
                {(field.type === 'age-range-selector' ||
                  field.type === 'country-selector' ||
                  field.type === 'sex-selector') && (
                  <View>
                    <Ionicons
                      name="chevron-down"
                      size={20}
                      color={colors.grey[70]}
                    />
                  </View>
                )}
                <View className="absolute -right-1 -top-1">
                  <CloseButton onPress={() => remove(index)} className="z-10" />
                </View>
              </View>
            ))}

            <Button
              testID="add-field-modal-button"
              label="Add field +"
              variant="secondary"
              className="w-[154px] py-2"
              size="sm"
              disabled={!watch('isRegistrationRequired')}
              onPress={() => fieldsModal.present()}
            />
          </>
        )}
      </View>

      <Modal ref={fieldsModal.ref} snapPoints={['40%']}>
        <View className="flex-1 gap-4 px-4 pb-4">
          <View className="mb-4 flex-row items-center justify-start gap-2">
            <TouchableOpacity
              onPress={() => fieldsModal.dismiss()}
              accessibilityLabel="Close category modal"
              accessibilityRole="button"
            >
              <Ionicons name="close" size={32} color={colors.grey[70]} />
            </TouchableOpacity>
            <Text className="text-lg font-bold dark:text-neutral-100">
              Add field
            </Text>
          </View>
          <BottomSheetInput
            label="Field name"
            value={fieldName}
            onChangeText={setFieldName}
          />
          <Select
            title="Choose field type"
            value={fieldType}
            placeholder="Field type"
            options={FIELD_TYPES}
            onSelect={(value) => setFieldType(value as typeof fieldType)}
          />

          <View className="mt-auto pb-4">
            <Button
              label="Save"
              onPress={handleAddField}
              disabled={!fieldName || !fieldType}
            />
          </View>
        </View>
      </Modal>
    </CreateEventLayout>
  );
}
