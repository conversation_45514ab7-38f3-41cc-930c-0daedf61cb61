import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { type Point } from 'react-native-google-places-autocomplete';

import { IconComponent } from '@/components/common/icon-component';
import LocationInput from '@/components/input/location-autocomplete';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  colors,
  ControlledInput,
  H2,
  Image,
  Input,
  Modal,
  P,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { useModal } from '@/components/ui/modal';
import { type CreateEventFormType } from '@/lib';
import { type LocationType } from '@/types';

export default function Hybrid() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const onlineModal = useModal();
  const customLinkModal = useModal();
  const generatingLinkModal = useModal();

  const eventUrl = watch('onlineUrl');
  const location = watch('location');

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const getZoomLink = () => {
    onlineModal.dismiss();
    generatingLinkModal.present();

    setTimeout(() => {
      generatingLinkModal.dismiss();
      setTimeout(() => {
        router.push('/events/create/zoom-generated');
      }, 300);
    }, 2000);
  };

  return (
    <CreateEventLayout
      title="Add event address"
      subTitle="Choose the format that suits your event."
      footer={
        <Button
          testID="account-selection-button"
          label="Continue"
          className="my-4"
          disabled={
            !location?.address ||
            !(
              eventUrl &&
              (() => {
                try {
                  new URL(eventUrl);
                  return true;
                } catch {
                  return false;
                }
              })()
            )
          }
          onPress={() => router.push('/events/create/choose-ticket-type')}
        />
      }
    >
      <View className="mt-4 gap-4">
        <LocationInput
          onSelectLocation={onSelectLocation}
          defaultValue={watch('location')}
        />
        <ControlledInput
          name="onlineUrl"
          label="Streaming link"
          disabled
          control={control}
          onPress={() => onlineModal.present()}
          icon={
            <Ionicons
              name="link"
              size={24}
              className="text-fg-muted dark:text-fg-muted"
            />
          }
        />
      </View>
      <Modal ref={onlineModal.ref} snapPoints={['35%']}>
        <View className="gap-2 p-4">
          <H2 className="text-start">Online events</H2>
          <TouchableOpacity
            className="flex-row items-center justify-start gap-4 py-3"
            onPress={() => {
              onlineModal.dismiss();
              customLinkModal.present();
            }}
          >
            <View className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="feather" iconName="globe" size={24} />
            </View>
            <P className="flex-1 items-center justify-center">
              Use your own URL
            </P>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-start gap-4 py-3"
            onPress={getZoomLink}
          >
            <View className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="custom-svg" iconName="Zoom" size={24} />
            </View>
            <P className="flex-1 items-center justify-center">
              Generate a Zoom link
            </P>
          </TouchableOpacity>
          <Button
            label="Skip for now"
            variant="outline"
            onPress={() => router.push('/events/create/choose-ticket-type')}
          />
        </View>
      </Modal>
      <Modal ref={customLinkModal.ref} snapPoints={['30%']}>
        <View className="w-full justify-start gap-2 p-4">
          <View className="flex-row justify-start gap-2 px-2 py-4">
            <TouchableOpacity
              className="size-8"
              onPress={() => {
                customLinkModal.dismiss();
                onlineModal.present();
              }}
            >
              <Ionicons
                name="chevron-back"
                size={24}
                color={colors.brand['60']}
                className="text-accent-moderate dark:text-accent-moderate"
              />
            </TouchableOpacity>
            <P className="font-bold">Enter event URL</P>
          </View>
          <View className="gap-6">
            <Input
              label="Enter link (URL)"
              value={eventUrl ? eventUrl : 'https://'}
              onChangeText={(value) => setValue('onlineUrl', value)}
            />

            <Button
              label="Save"
              disabled={!eventUrl}
              onPress={() => {
                customLinkModal.dismiss();
                router.push('/events/create/choose-ticket-type');
              }}
            />
          </View>
        </View>
      </Modal>
      <Modal ref={generatingLinkModal.ref} snapPoints={['40%']}>
        <View className="items-center justify-center gap-2 p-4">
          <View className="size-[125px]">
            <Image
              source={require('~/assets/icons/events/loading.png')}
              className="size-[105px] self-center p-2.5"
            />
          </View>
          <P className="items-center justify-center text-fg-muted-light dark:text-fg-muted-dark">
            Generating link...
          </P>
        </View>
      </Modal>
    </CreateEventLayout>
  );
}
