import { useRouter } from 'expo-router';
import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, H2, Image, Modal, P, View } from '@/components/ui';
import { useModal } from '@/components/ui/modal';
import { type CreateEventFormType, EVENT_TYPE_CARDS } from '@/lib';
import { BottomSheetView } from '@gorhom/bottom-sheet';

export default function ChooseEventVisibility() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();

  const router = useRouter();
  const modal = useModal();

  const handleContinue = () => {
    const eventType = watch('eventType');
    if (eventType === 'PRIVATE') {
      modal.present();
    } else {
      router.push('/events/create/add-details');
    }
  };

  return (
    <CreateEventLayout
      title="What type of event are you hosting?"
      subTitle="Choose whether your event is public or private."
      footer={
        <Button
          testID="go-to-event-type-selection-button"
          label="Continue"
          className="m-4"
          disabled={!watch('eventType')}
          onPress={handleContinue}
        />
      }
    >
      {EVENT_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('eventType') === card.id}
          onPress={() => setValue('eventType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}

      <Modal ref={modal.ref} enableDynamicSizing>
        <BottomSheetView className="gap-6 px-4 pb-8 min-h-6">
          <Image
            source={require('~/assets/icons/events/confirm-private.png')}
            className="size-[120px] self-center"
          />

          <View className="gap-4">
            <H2 className="text-center">Make this event private?</H2>
            <P className="text-center text-fg-muted-light dark:text-fg-muted-dark">
              By making this event private, you will not be able to list it
              publicly. Attendees will require an access code to be part of this
              event.
            </P>
          </View>
          <View className="flex-row justify-between gap-2 py-4">
            <Button
              label="Cancel"
              variant="secondary"
              onPress={modal.dismiss}
              className="flex-1"
            />
            <Button
              label="Make private"
              onPress={() => {
                modal.dismiss();
                router.push('/events/create/add-details');
              }}
              className="flex-1"
            />
          </View>
        </BottomSheetView>
      </Modal>
    </CreateEventLayout>
  );
}
