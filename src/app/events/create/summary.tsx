import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useFieldArray } from 'react-hook-form';
import { Pressable } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { queryClient } from '@/api';
import {
  type PresaleConfig,
  type TicketCategoriesType,
  useCreateEvent,
} from '@/api/events';
import { TicketCard } from '@/components/cards/ticket-card';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import CollaboratorCard from '@/components/events/collaborators';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  colors,
  H2,
  H5,
  Image,
  Pencil,
  ScrollView,
  semanticColors,
  Small,
  Tiny,
  View,
  <PERSON>sBoldLabel,
} from '@/components/ui';
import {
  type CreateEventFormType,
  toAmountInMinor,
  useFieldBlurAndFilled,
} from '@/lib';
import {
  formatDate,
  formatDateTime,
  formatTime,
} from '@/lib/utils/formatDateTime';

export default function EventDetails() {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  const { watch, reset, control, getValues, setValue } =
    useFormContext<CreateEventFormType>();

  const { fields, remove } = useFieldArray({
    control,
    name: 'tickets',
  });

  const ticketValues = watch('tickets');
  const location = watch('location');
  const onlineEventUrl = watch('onlineUrl');

  const artists = getValues('collaborators') || [];
  useFieldBlurAndFilled<CreateEventFormType>([
    'title',
    'description',
    'timezone',
    'category',
    'startDatetime',
    'endDatetime',
    'collaborators',
    'tickets',
  ]);

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [menuVisible, setMenuVisible] = useState<string | number | null>(null);

  const [confirmRAVisible, setConfirmRAVisible] = useState(false);

  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<number | null>(null);

  const { mutate, isPending } = useCreateEvent();
  return (
    <CreateEventLayout
      title="Review your event"
      subTitle="Make sure everything looks good before publishing."
      footer={
        <Button
          testID="account-selection-button"
          label="Create Event"
          className="m-4"
          loading={isPending}
          disabled={isPending}
          onPress={() => {
            const {
              category,
              description,
              endDatetime,
              eventFormat,
              eventType,
              startDatetime,
              ticketType,
              tickets,
              timezone,
              title,
              bannerObj,
              isRegistrationRequired,
              maxAttendees,
              passcode,
              media,
            } = watch();

            const formDataPayload = new FormData();
            formDataPayload.append('title', title);
            formDataPayload.append('description', description);
            // @ts-ignore
            formDataPayload.append('banner', bannerObj);
            media?.forEach((file) => {
              formDataPayload.append('media', file as any);
            });

            artists
              .map(({ id, avatar, name }) => ({
                spotifyArtistId: id,
                avatar,
                artistName: name,
              }))
              .forEach((artist, index) => {
                Object.entries(artist).forEach(([key, value]) => {
                  formDataPayload.append(`artists[${index}][${key}]`, value);
                });
              });
            // @ts-ignore
            location &&
              location.address !== '' &&
              formDataPayload.append(
                'location',
                JSON.stringify({
                  city: location.city,
                  state: location.state,
                  street: String(location.street),
                  country: location.country,
                  address: location.address,
                  coordinates: {
                    lat: location.coordinates.lat,
                    lng: location.coordinates.lng,
                  },
                  ...(location.landmark && {
                    landmark: location.landmark,
                  }),
                })
              );
            // @ts-ignore
            formDataPayload.append('startTime', startDatetime.toISOString());
            // @ts-ignore
            formDataPayload.append('endTime', endDatetime.toISOString());
            formDataPayload.append('categoryId', category);
            formDataPayload.append('timezone', timezone);
            formDataPayload.append('eventFormat', eventFormat);
            formDataPayload.append('eventType', eventType);
            formDataPayload.append('ticketType', ticketType);
            formDataPayload.append(
              'registrationRequired',
              // @ts-ignore
              isRegistrationRequired || false
            );
            formDataPayload.append(
              'isTicketed',
              // @ts-ignore
              ticketType === 'PAID'
            );
            maxAttendees &&
              formDataPayload.append(
                'maxAttendees',
                // @ts-ignore
                maxAttendees
              );
            onlineEventUrl &&
              onlineEventUrl !== 'https://' &&
              formDataPayload.append('onlineEventUrl', onlineEventUrl);
            passcode && formDataPayload.append('passcode', passcode);
            // @ts-ignore
            // formDataPayload.append('zoomMeetingId', zoomMeetingId);

            const initial: TicketCategoriesType = {};
            const presaleTickets: Omit<PresaleConfig, 'eventId'>[] = [];

            const ticketCategories = tickets.slice(1).reduce((prev, item) => {
              const {
                name,
                price,
                quantity,
                id,
                hasPresale,
                hasPurchaseLimit,
                hasTimeline,
                description,
                endDatetime,
                purchaseLimit,
                startDatetime,
              } = item;

              const costInMinor = toAmountInMinor(price);

              prev[name] = {
                cost: costInMinor,
                quantity: Number(quantity),
                id,
                hasPresale,
                hasPurchaseLimit,
                hasTimeline,
                description,
                endDateTime: endDatetime,
                purchaseLimit,
                startDateTime: startDatetime,
              };

              if (hasPresale) {
                const {
                  presalePrice,
                  presaleName,
                  presaleQuantity,
                  presalePurchaseLimit,
                  presaleDescription,
                  presaleStartDatetime,
                  presaleEndDatetime,
                } = item;

                const presaleConfig: Omit<PresaleConfig, 'eventId'> = {
                  name: presaleName,
                  price: toAmountInMinor(presalePrice),
                  quantity: Number(presaleQuantity),
                  ticketCategoryId: id,
                  totalTickets: Number(presaleQuantity),
                  description: presaleDescription,
                  purchaseLimit:
                    presalePurchaseLimit || Number(presaleQuantity),
                  currency: 'NGN', // You may want to make this configurable
                  startDateTime: presaleStartDatetime.toISOString(),
                  // dayjs(presaleEndDatetime).format('YYYY-MM-DD')
                  endDateTime: presaleEndDatetime.toISOString(),
                };

                presaleTickets.push(presaleConfig);
              }

              return prev;
            }, initial);

            formDataPayload.append(
              'ticketCategories',
              JSON.stringify(ticketCategories)
            );

            presaleTickets.forEach((p, index) => {
              Object.entries(p).forEach(([key, value]) => {
                formDataPayload.append(
                  `presaleConfig[${index}][${key}]`,
                  // @ts-ignore
                  value
                );
              });
            });

            mutate(formDataPayload, {
              onSuccess: (response) => {
                reset();
                toast.success('Event created successfully');

                router.push({
                  pathname: '/events/[id]/creation-success',
                  params: { id: response.slug },
                });
                queryClient.invalidateQueries({ queryKey: ['getEvents'] });
              },
              onError: (error) => toast.error(error.message),
            });
          }}
        />
      }
    >
      <ScrollView
        className="bg-bg-canvas relative flex-1 dark:bg-grey-100"
        style={{
          paddingBottom: insets.bottom,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="relative h-[234px] w-full">
          <Image
            source={
              getValues('bannerObj.uri') ||
              require('~/assets/gradient-bg/landscape-event-bg.png')
            }
            className="absolute inset-x-0 top-0 h-full w-screen"
            contentFit="cover"
          />
        </View>

        <View className="flex-1 gap-4 py-6">
          <View className="flex-row items-center justify-between">
            <H2>{getValues('title')}</H2>
            {getValues('eventType') === 'PRIVATE' ? (
              <View className="h-full w-[81px] flex-row items-center justify-center rounded-full bg-fg-danger-light dark:bg-fg-danger-dark">
                <Feather
                  name="eye-off"
                  size={12}
                  color="#EA5455"
                  className="mr-1"
                />
                <XsBoldLabel className="text-danger-60 dark:text-danger-40">
                  Private
                </XsBoldLabel>
              </View>
            ) : (
              <View className="h-full w-[81px] flex-row items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                <Feather
                  name="eye"
                  size={12}
                  color="#28C76F"
                  className="mr-1"
                />
                <XsBoldLabel className="text-green-60 dark:text-green-40">
                  Public
                </XsBoldLabel>
              </View>
            )}
          </View>
          <View className="flex-row justify-between gap-3">
            <View className="flex-row gap-3">
              <Feather
                name="calendar"
                size={24}
                color={isDark ? colors.white : colors.grey[100]}
              />
              <View className="flex-row items-center justify-between gap-2">
                <View className="gap-1">
                  <H5>
                    {formatDate(getValues('startDatetime')) ===
                    formatDate(getValues('endDatetime')) ? (
                      <>
                        {formatDate(getValues('startDatetime'))}
                        {'\n'}
                        {formatTime(getValues('startDatetime'))} -{' '}
                        {formatTime(getValues('endDatetime'))}
                      </>
                    ) : (
                      <>
                        {formatDateTime(getValues('startDatetime'))} -{'\n'}
                        {formatDateTime(getValues('endDatetime'))}
                      </>
                    )}
                  </H5>
                </View>
              </View>
            </View>
            <Button
              label="Edit"
              className="w-[62px]"
              size="xs"
              iconPosition="right"
              onPress={() => router.push('/events/create/add-details')}
              variant="outline"
              iconClassName="right-2"
              icon={
                <Pencil
                  color={
                    isDark
                      ? semanticColors.accent.bold.dark
                      : semanticColors.accent.bold.light
                  }
                />
              }
            />
          </View>
          {(getValues('onlineUrl') || getValues('location')) && (
            <View className="flex-row justify-between gap-3">
              <View className="flex-row gap-3">
                <Feather
                  name="map-pin"
                  size={24}
                  color={isDark ? colors.white : colors.grey[100]}
                />

                <View className="gap-2">
                  <View className="gap-2">
                    {getValues('eventFormat') === 'IN_PERSON' && (
                      <View className="gap-1">
                        <H5>{getValues('location').landmark}</H5>
                        <Tiny className="text-grey-60 dark:text-grey-50">
                          {getValues('location').state +
                            ', ' +
                            getValues('location').country}
                        </Tiny>
                      </View>
                    )}

                    {getValues('eventFormat') === 'ONLINE' && (
                      <View className="gap-1">
                        <H5>Event URL</H5>
                        <Tiny className="text-grey-60 dark:text-grey-50">
                          {getValues('onlineUrl')}
                        </Tiny>
                      </View>
                    )}

                    {getValues('eventFormat') === 'HYBRID' && (
                      <>
                        <View className="gap-1">
                          <H5>{getValues('location').address}</H5>
                          <Tiny className="text-grey-60 dark:text-grey-50">
                            {getValues('location').address}
                          </Tiny>
                        </View>
                        <View className="gap-1">
                          <H5>Event URL</H5>
                          <Tiny className="text-grey-60 dark:text-grey-50">
                            {getValues('onlineUrl')}
                          </Tiny>
                        </View>
                      </>
                    )}
                  </View>
                </View>
              </View>
              <Button
                label="Edit"
                className="w-[62px]"
                onPress={() => {
                  if (getValues('eventFormat') === 'ONLINE') {
                    router.push('/events/create/choose-format');
                  } else if (getValues('eventFormat') === 'IN_PERSON') {
                    router.push('/events/create/add-address');
                  } else {
                    router.push('/events/create/hybrid');
                  }
                }}
                size="xs"
                iconPosition="right"
                variant="outline"
                iconClassName="right-2"
                icon={
                  <Pencil
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                }
              />
            </View>
          )}

          {artists.length > 0 && (
            <>
              <H5 className="text-grey-60 dark:text-grey-50">Lineup</H5>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={{ marginTop: 16, gap: 16, overflow: 'visible' }}
              >
                <View className="flex-row flex-wrap gap-2">
                  {artists.map((artist) => (
                    <CollaboratorCard
                      key={artist.id}
                      artist={artist}
                      onRemove={() => {
                        setArtistToDelete(artist.id);
                        setConfirmVisible(true);
                      }}
                    />
                  ))}
                </View>
              </ScrollView>
            </>
          )}
          {getValues('description') && (
            <View className="gap-2">
              <H5 className="text-grey-60 dark:text-grey-50">
                About this event
              </H5>
              <Small>{getValues('description')}</Small>
              <Button
                label="Edit"
                className="w-[62px]"
                size="xs"
                onPress={() => router.push('/events/create/add-details')}
                iconPosition="right"
                variant="outline"
                iconClassName="right-2"
                icon={
                  <Pencil
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                }
              />
            </View>
          )}

          {getValues('tickets')?.length > 1 && (
            <View className="gap-2">
              <H5 className="text-grey-60 dark:text-grey-50">Tickets</H5>
              <Pressable
                onPressOut={() => setMenuVisible(null)}
                className="flex-1"
              >
                <View className="gap-4">
                  {fields.map((field, index) => {
                    if (index === 0) return null;
                    const ticket = ticketValues?.[index];

                    return (
                      <View key={field.id} className="gap-4">
                        <TicketCard
                          ticket={ticket}
                          fieldId={field.id}
                          isMenuOpen={menuVisible === field.id}
                          setMenuVisible={setMenuVisible}
                          isDark={isDark}
                          onEdit={() => {
                            const id = ticket?.id;
                            router.push(`/events/create/edit-ticket/${id}`);
                            setMenuVisible(null);
                          }}
                          onDelete={() => {
                            setTicketToDelete(index);
                            setConfirmVisible(true);
                            setMenuVisible(null);
                          }}
                        />
                      </View>
                    );
                  })}
                  <Button
                    testID="add-new-ticket-button"
                    label="Add new ticket +"
                    variant="secondary"
                    className="w-[154px] py-2"
                    size="sm"
                    onPress={() => router.push('/events/create/add-ticket')}
                  />
                </View>
              </Pressable>
            </View>
          )}
        </View>
      </ScrollView>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this ticket?"
        onCancel={() => {
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
        onConfirm={() => {
          if (ticketToDelete !== null) {
            remove(ticketToDelete);
          }
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
      />
      <ConfirmationDialog
        visible={confirmRAVisible}
        message="Are you sure you want to delete this artist?"
        onCancel={() => {
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            setTimeout(() => {
              setValue(
                'collaborators',
                artists.filter((a) => a.id !== artistToDelete)
              );
            }, 0);
          }
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
      />
    </CreateEventLayout>
  );
}
