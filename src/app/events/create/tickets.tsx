import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useFieldArray } from 'react-hook-form';
import { Pressable } from 'react-native';

import { TicketCard } from '@/components/cards/ticket-card';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, View } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';

export default function Tickets() {
  const { watch, control } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const { fields, remove } = useFieldArray({
    control,
    name: 'tickets',
  });
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [menuVisible, setMenuVisible] = useState<string | number | null>(null);

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<number | null>(null);

  return (
    <CreateEventLayout
      title="Set up tickets"
      subTitle="You can create multiple ticket types with varying prices."
      onBackPress={() => router.push('/events/create/choose-ticket-type')}
      footer={
        <Button
          testID="continue-button"
          label="Continue"
          className="m-4"
          disabled={watch('tickets')?.length < 2}
          onPress={() => router.push('/events/create/summary')}
        />
      }
    >
      <Pressable onPressOut={() => setMenuVisible(null)} className="flex-1">
        <View className="gap-4">
          {fields.map((field, index) => {
            if (index === 0) return null;
            const ticket = watch(`tickets.${index}`);
            if (!ticket) return null;
            return (
              <View key={field.id} className="relative gap-4">
                <TicketCard
                  ticket={ticket}
                  fieldId={field.id}
                  isMenuOpen={menuVisible === field.id}
                  setMenuVisible={setMenuVisible}
                  isDark={isDark}
                  onEdit={() => {
                    const id = ticket?.id;
                    router.push(`/events/create/edit-ticket/${id}`);
                    setMenuVisible(null);
                  }}
                  onDelete={() => {
                    setTicketToDelete(index);
                    setConfirmVisible(true);
                    setMenuVisible(null);
                  }}
                />
              </View>
            );
          })}
          <Button
            testID="add-new-ticket-button"
            label="Add new ticket +"
            variant="secondary"
            className="w-[154px] py-2"
            size="sm"
            onPress={() => router.push('/events/create/add-ticket')}
          />
        </View>
      </Pressable>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this ticket?"
        onCancel={() => {
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
        onConfirm={() => {
          if (ticketToDelete !== null) {
            remove(ticketToDelete);
          }
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
      />
    </CreateEventLayout>
  );
}
