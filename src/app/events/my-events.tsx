import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import React from 'react';
import { useCallback, useState } from 'react';

import { type IEvent, useGetEvents } from '@/api/events';
import { Back } from '@/components/common/back';
import { CreatorEventsTab } from '@/components/tab-views/my-events-tab';
import {
  AppTab,
  FocusAwareStatusBar,
  LgBoldLabel,
  MdBoldLabel,
  SafeAreaView,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { useLoggedInUser } from '@/lib';
import { type TabScreenItem } from '@/types';
import { useRouter } from 'expo-router';

export default function MyEvents() {
  const { push } = useRouter();
  const { data: currentUser, isLoading: userLoading } = useLoggedInUser();
  const [index, setIndex] = React.useState(0);

  const { data: eventData, isPending } = useGetEvents({
    variables: {
      userId: currentUser?.id,
      organizerId: currentUser?.id,
    },
  });

  const { upcomingEvents, pastEvents } = React.useMemo(() => {
    if (!eventData) return { upcomingEvents: [], pastEvents: [] };
    const { events } = eventData;
    const now = dayjs();

    const upcomingEvents: IEvent[] = [];
    const pastEvents: IEvent[] = [];

    events.forEach((event) => {
      const eventEndTime = dayjs(event.endTime);

      if (eventEndTime.isAfter(now)) {
        upcomingEvents.push(event);
      } else {
        pastEvents.push(event);
      }
    });

    return { upcomingEvents, pastEvents };
  }, [eventData]);

  const [refreshing, setRefreshing] = useState(false);
  const queryClient = useQueryClient();

  const onRefresh = useCallback(() => {
    setRefreshing(true);

    setTimeout(() => {
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['getEvents'],
          exact: false,
        }),
      ]).finally(() => {
        setRefreshing(false);
      });
    }, 1000);
  }, [queryClient]);

  const tabItems: TabScreenItem[] = React.useMemo(() => {
    return [
      {
        key: 'Upcoming',
        title: 'Upcoming',
        component: () => (
          <CreatorEventsTab
            events={upcomingEvents}
            index={0}
            isPending={isPending || userLoading}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        ),
      },
      {
        key: 'Past',
        title: 'Past',
        component: () => (
          <CreatorEventsTab
            events={pastEvents}
            index={1}
            isPending={isPending || userLoading}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        ),
      },
    ];
  }, [upcomingEvents, pastEvents]);

  return (
    <SafeAreaView className="flex-1 ">
      <FocusAwareStatusBar />
      <View className="h-16 flex-row justify-between items-center px-2">
        <View className="flex-row items-center gap-2">
          <Back />
          <LgBoldLabel>My Events</LgBoldLabel>
        </View>
        <TouchableOpacity
          className="flex-row items-center justify-center rounded-full bg-brand-20 px-4 py-2 dark:bg-brand-90"
          onPress={() => {
            push({ pathname: '/events/create' });
          }}
        >
          <MdBoldLabel className="text-brand-70 dark:text-brand-40">
            Create event
          </MdBoldLabel>
        </TouchableOpacity>
      </View>
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </SafeAreaView>
  );
}
