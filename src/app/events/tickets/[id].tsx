import { Feather } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import { useLocalSearchParams } from 'expo-router';
import * as Sharing from 'expo-sharing';
import React from 'react';
import { ActivityIndicator, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import type ViewShot from 'react-native-view-shot';
import { captureRef } from 'react-native-view-shot';

import { type ITicketExtension } from '@/api/events';
import { type IEventsWithTicket, useGetUserTickets } from '@/api/user';
import { TicketCarousel } from '@/components/carousel/ticket-detail';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import {
  colors,
  FocusAwareStatusBar,
  LgBoldLabel,
  P,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { removeSpacesAndSymbols, useLoggedInUser } from '@/lib';

export default function EventTicket() {
  const { data: user } = useLoggedInUser();
  const [viewShotRefs, setViewShotRefs] = React.useState<
    React.RefObject<ViewShot>[]
  >([]);
  const [isCapturing, setIsCapturing] = React.useState(false);

  const { id, transactionId } = useLocalSearchParams<{
    id: string;
    transactionId?: string;
  }>();

  const { data: ticketsData, isLoading } = useGetUserTickets({
    variables: { id: user?.id || '' },
    enabled: !!user?.id,
  });

  const mergedTickets = React.useCallback(
    (eventsWithTicket?: IEventsWithTicket[]): ITicketExtension[] => {
      if (!eventsWithTicket) return [];
      return eventsWithTicket.reduce((tickets, event) => {
        const eventTickets = event.tickets
          .filter((ticket) => ticket.userId === user?.id)
          .map((ticket) => ({
            ...ticket,
            title: event.title,
            bannerUrl: event.bannerUrl,
            startTime: event.startTime,
            location: event.location,
            organizer: event.organizer,
            eventId: event.id,
            event,
            user: ticket.user,
          }));

        return [...tickets, ...eventTickets];
      }, [] as ITicketExtension[]);
    },
    [user?.id]
  );

  const ticketsList = React.useMemo(
    () => mergedTickets(ticketsData?.eventsWithTicket),
    [ticketsData?.eventsWithTicket, mergedTickets]
  );

  const displayTickets = React.useMemo(() => {
    if (transactionId) {
      const ticketsByTransaction = ticketsList?.filter(
        ({ transactionId: txId }) => txId === transactionId
      );
      return ticketsByTransaction.length > 0 ? ticketsByTransaction : [];
    } else {
      const singleTicket = ticketsList.find(
        ({ id: ticketId }) => ticketId === id
      );
      return singleTicket ? [singleTicket] : [];
    }
  }, [ticketsList, transactionId, id]);

  const [currentIndex, setCurrentIndex] = React.useState(0);
  const currentTicket = displayTickets[currentIndex];
  React.useEffect(() => {
    setViewShotRefs((refs) =>
      Array(displayTickets.length)
        .fill(null)
        .map((_, i) => refs[i] || React.createRef<ViewShot>())
    );
  }, [displayTickets.length]);

  const handleTicketChange = (index: number) => setCurrentIndex(index);

  const captureAndShare = async (): Promise<void> => {
    const currentRef = viewShotRefs[currentIndex];
    if (!currentRef?.current || !currentTicket) {
      Alert.alert('Error', 'Unable to capture ticket');
      return;
    }

    try {
      setIsCapturing(true);

      // Capture the view
      const uri = await captureRef(currentRef.current, {
        format: 'jpg',
        quality: 0.9,
        result: 'tmpfile',
      });

      // Generate filename
      const fileName = removeSpacesAndSymbols(
        currentTicket.title.toLowerCase()
      );
      const timestamp = Date.now();
      const finalFileName = `${fileName}_${timestamp}.jpg`;

      // Create final file path
      const finalUri = `${FileSystem.documentDirectory}${finalFileName}`;

      // Move/copy file to final location
      await FileSystem.copyAsync({
        from: uri,
        to: finalUri,
      });

      await shareImage(finalUri);

      // Clean up
      await FileSystem.deleteAsync(finalUri, { idempotent: true });
    } catch (error) {
      console.error('Error capturing component:', error);
      Alert.alert('Error', 'Failed to capture ticket. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  const shareImage = async (uri: string): Promise<void> => {
    try {
      const isAvailable = await Sharing.isAvailableAsync();

      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return;
      }

      await Sharing.shareAsync(uri, {
        mimeType: 'image/jpeg',
        dialogTitle: currentTicket?.title || 'Event Ticket',
      });
    } catch (error) {
      console.error('Error sharing image:', error);
      Alert.alert('Error', 'Failed to share ticket.');
    }
  };

  if (isLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="h-16 flex-row items-center justify-between px-2">
        <Back />
        <View className="flex-row items-center gap-2">
          <LgBoldLabel>Ticket details</LgBoldLabel>
          {displayTickets.length > 1 && (
            <View className="rounded-full bg-brand-10 px-2 py-1 dark:bg-brand-80">
              <P className="text-xs text-brand-60 dark:text-brand-40">
                {currentIndex + 1} of {displayTickets.length}
              </P>
            </View>
          )}
        </View>
        <TouchableOpacity onPress={captureAndShare} disabled={isCapturing}>
          {isCapturing ? (
            <ActivityIndicator color={colors.brand[60]} />
          ) : (
            <Feather name="download" size={32} color={colors.brand[60]} />
          )}
        </TouchableOpacity>
      </View>
      <TicketCarousel
        tickets={displayTickets}
        onTicketChange={handleTicketChange}
        currentIndex={currentIndex}
        viewShotRefs={viewShotRefs}
      />
    </SafeAreaView>
  );
}
