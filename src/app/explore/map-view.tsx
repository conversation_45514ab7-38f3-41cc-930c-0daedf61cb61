import {
  BottomSheetScrollView,
  BottomSheetView,
  useBottomSheetModal,
} from '@gorhom/bottom-sheet';
import * as Location from 'expo-location';
import { useFocusEffect, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import {
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import {
  KeyboardAvoidingView,
  Linking,
  Platform,
  Pressable,
  StyleSheet,
} from 'react-native';
import { type Point } from 'react-native-google-places-autocomplete';
import MapView, { Marker, type Region } from 'react-native-maps';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { type IEvent, useGetEvents } from '@/api/events';
import {
  type LiveSessionResponse,
  useGetNearbySessions,
  useJoinLiveSession,
} from '@/api/session';
import { Back } from '@/components/common/back';
import { LocationAccessDialog } from '@/components/dialogs/location-access';
import ExploreLocationInput from '@/components/input/explore-location-input';
import { MapEventCard } from '@/components/map/card/event';
import { MapSessionCard } from '@/components/map/card/session';
import {
  Button,
  FocusAwareStatusBar,
  H3,
  H4,
  Image,
  List,
  Modal,
  ScrollView,
  Skeleton,
  SmRegularLabel,
  Text,
  View,
  XsBoldLabel,
  XsRegularLabel,
} from '@/components/ui';
import { useModal } from '@/components/ui/modal';
import {
  cn,
  getDistanceFromLatLonInKm,
  mapCustomStyle,
  pxToSnapPoint,
  useLocation,
  useLoggedInUser,
} from '@/lib';
import { useSession } from '@/lib/session';
import { getLiveSession } from '@/lib/session/utils';
import { formatDateTime } from '@/lib/utils/formatDateTime';
import { type LocationType } from '@/types';

const MODAL_TYPES = ['explore', 'event', 'session'] as const;
type ModalType = (typeof MODAL_TYPES)[number];
type ModalKeysState = Record<ModalType, number>;
type ModalKeysAction =
  | { type: 'INCREMENT_ALL' }
  | { type: 'INCREMENT_SINGLE'; modal: ModalType }
  | { type: 'RESET' };

const initialModalKeys: ModalKeysState = MODAL_TYPES.reduce(
  (acc, modal) => ({ ...acc, [modal]: 0 }),
  {} as ModalKeysState
);

const modalKeysReducer = (
  state: ModalKeysState,
  action: ModalKeysAction
): ModalKeysState => {
  switch (action.type) {
    case 'INCREMENT_ALL':
      return Object.keys(state).reduce(
        (acc, key) => ({
          ...acc,
          [key]: state[key as ModalType] + 1,
        }),
        {} as ModalKeysState
      );
    case 'INCREMENT_SINGLE':
      return {
        ...state,
        [action.modal]: state[action.modal] + 1,
      };
    case 'RESET':
      return Object.keys(state).reduce(
        (acc, key) => ({ ...acc, [key]: 0 }),
        {} as ModalKeysState
      );
    default:
      return state;
  }
};

const OFFSET = 0.02;

export default function HomeMapView() {
  const { data: user } = useLoggedInUser();
  const router = useRouter();
  const thingsModal = useModal();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { location, setLocation, locationAccessible } = useLocation();

  const mapRef = useRef<MapView | null>(null);
  const [selectedTab, setSelectedTab] = useState<'all' | 'events' | 'live'>(
    'all'
  );

  const snapPoints = useMemo(() => {
    const base = pxToSnapPoint(insets.bottom + 16);
    const pxBase = insets.bottom + 16;

    switch (selectedTab) {
      case 'all':
        return [base, '45%'];
      case 'events':
        return [base, pxToSnapPoint(240 + pxBase)];
      case 'live':
        return [base, pxToSnapPoint(300 + pxBase)];
      default:
        return [base];
    }
  }, [selectedTab, insets.bottom]);

  const [locationAccessVisible, setLocationAccessVisible] = useState(false);

  const checkLocationAccess = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      setLocationAccessVisible(true);
    }
  };

  useEffect(() => {
    checkLocationAccess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const distance = 100; // In KM
  const distanceInMeters = distance * 1000;
  const {
    data: eventsData,
    isLoading: isEventsLoading,
    isPending: isEventsPending,
  } = useGetEvents({
    variables: {
      maxDistance: String(distanceInMeters),
      ...(location?.coordinates && {
        coordinates: JSON.stringify({
          lat: location.coordinates.lat,
          lng: location.coordinates.lng,
        }),
      }),
    },
    enabled: !!location?.coordinates,
    refetchInterval: 60 * 1000,
  });

  const filteredEvents = useMemo(() => {
    if (
      !location?.coordinates ||
      !eventsData?.events ||
      !Array.isArray(eventsData.events)
    ) {
      return [];
    }

    const userLat = location.coordinates.lat;
    const userLng = location.coordinates.lng;

    return eventsData.events
      .map((event) => {
        const coords = event.location?.coordinates;

        // Skip events without valid coordinates
        if (
          !coords ||
          typeof coords.lat !== 'number' ||
          typeof coords.lng !== 'number'
        ) {
          return null;
        }

        const distanceKm = getDistanceFromLatLonInKm(
          userLat,
          userLng,
          coords.lat,
          coords.lng
        );

        return {
          ...event,
          calculatedDistance: distanceKm,
        };
      })
      .filter((event) => event !== null) // Remove events without coordinates
      .sort((a, b) => a.calculatedDistance - b.calculatedDistance); // Sort by distance (ascending)
  }, [location, distance, eventsData]);

  const {
    data: sessionObj,
    isLoading: isSessionsLoading,
    isPending: isSessionsPending,
  } = useGetNearbySessions({
    variables: {
      maxDistance: String(distanceInMeters),
      status: 'LIVE',
    },
  });

  const filteredSessions = useMemo(() => {
    if (
      !location?.coordinates ||
      !sessionObj?.djSessions ||
      !Array.isArray(sessionObj.djSessions)
    ) {
      return [];
    }

    const userLat = location.coordinates.lat;
    const userLng = location.coordinates.lng;

    return sessionObj.djSessions
      .map((session) => {
        const coords = session?.location?.coordinates;

        // Skip sessions without valid coordinates
        if (
          !coords ||
          typeof coords.lat !== 'number' ||
          typeof coords.lng !== 'number'
        ) {
          return null;
        }

        const distanceKm = getDistanceFromLatLonInKm(
          userLat,
          userLng,
          coords.lat,
          coords.lng
        );

        return {
          ...session,
          calculatedDistance: distanceKm,
        };
      })
      .filter((session) => session !== null) // Remove sessions without coordinates
      .sort((a, b) => a.calculatedDistance - b.calculatedDistance); // Sort by distance (ascending)
  }, [location, distance, sessionObj]);

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setLocation(locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const eventModal = useModal();
  const [modalKeys, dispatchModalKeys] = useReducer(
    modalKeysReducer,
    initialModalKeys
  );
  const [selectedEvent, setSelectedEvent] = useState<IEvent>();

  const [currentRegion, setCurrentRegion] = useState<Region | null>(null);

  const isWithinThreshold = (a: number, b: number, threshold = 0.005) =>
    Math.abs(a - b) <= threshold;

  const onMoveToLocation = <
    T extends { location?: { coordinates: Point } | null | undefined },
  >({
    item,
    setSelected,
    offset = OFFSET,
    modal,
  }: {
    item: T;
    modal?: ReturnType<typeof useModal>;
    setSelected?: React.Dispatch<React.SetStateAction<T | undefined>>;
    offset?: number;
  }) => {
    const { lat, lng } = item.location?.coordinates || { lat: 0, lng: 0 };
    const targetLatitude = lat - offset;
    const targetLongitude = lng;
    if (
      currentRegion &&
      isWithinThreshold(currentRegion.latitude, targetLatitude) &&
      isWithinThreshold(currentRegion.longitude, targetLongitude)
    ) {
      setSelected?.(item);
      modal?.present();
    } else {
      mapRef.current?.animateToRegion({
        latitude: lat - offset,
        longitude: lng,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
    }
    thingsModal?.snapToIndex(1);
  };

  function handleItemPress<
    T extends { location?: { coordinates: Point } | null | undefined },
  >(
    item: T,
    modal: ReturnType<typeof useModal>,
    setSelected: React.Dispatch<React.SetStateAction<T | undefined>>,
    offset?: number
  ) {
    setSelected(item);
    item.location?.coordinates &&
      mapRef.current?.animateToRegion({
        latitude: item.location?.coordinates.lat - (offset || OFFSET),
        longitude: item.location?.coordinates.lng,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
    modal.present();
  }

  const activeSession = getLiveSession();
  const setJoinedSessionId = useSession.use.setJoinedSessionId();
  const { mutate: joinLiveSession } = useJoinLiveSession();

  const handleJoinLiveSession = useCallback(
    (sessionId: string) => {
      const alreadyInSession = activeSession?.id === sessionId;
      const inAnotherSession = !!activeSession?.id && !alreadyInSession;

      if (inAnotherSession) {
        toast.info('Already in a live session', {
          description: 'Please exit your current session to join a new one',
        });
        return;
      }

      joinLiveSession(
        { sessionId },
        {
          onSuccess: () => {
            setJoinedSessionId(sessionId);
            toast.success('Successfully joined live session');
            router.dismissAll();
            router.push({
              pathname: '/session/[id]',
              params: { id: sessionId },
            });
          },
          onError: (error) => toast.error(error.message),
        }
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeSession?.id, joinLiveSession, setJoinedSessionId]
  );

  const { dismissAll: dismissAllModals } = useBottomSheetModal();

  useFocusEffect(
    useCallback(() => {
      // Increment all modal keys to force re-render
      dispatchModalKeys({ type: 'INCREMENT_ALL' });
      const timer = requestAnimationFrame(() => {
        thingsModal.present();
      });

      return () => {
        cancelAnimationFrame(timer);
        dismissAllModals();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
  );
  if (!user || !location) return null;

  return (
    <View style={StyleSheet.absoluteFill}>
      <FocusAwareStatusBar />
      <View className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
        <MapView
          ref={mapRef}
          region={{
            latitude: location.coordinates.lat - OFFSET,
            longitude: location.coordinates.lng,
            latitudeDelta: locationAccessible ? 0.02 : 10,
            longitudeDelta: locationAccessible ? 0.02 : 10,
          }}
          onRegionChangeComplete={(region) => {
            setCurrentRegion(region);
          }}
          cameraZoomRange={{
            minCenterCoordinateDistance: 20000,
            animated: true,
          }}
          style={StyleSheet.absoluteFill}
          showsPointsOfInterest={false}
          showsBuildings={false}
          mapType={Platform.OS === 'ios' ? 'mutedStandard' : 'standard'}
          userInterfaceStyle={isDark ? 'dark' : 'light'}
          customMapStyle={
            isDark && Platform.OS === 'android' ? mapCustomStyle : []
          }
          showsUserLocation={true}
          showsMyLocationButton
          loadingEnabled
        >
          {filteredEvents.map((event, index) => {
            if (!event.location?.coordinates || selectedTab === 'live')
              return null;
            return (
              <Marker
                key={index}
                coordinate={{
                  latitude: event.location.coordinates.lat,
                  longitude: event.location.coordinates.lng,
                }}
                anchor={{ x: 0.5, y: 1.0 }}
                onPress={() =>
                  handleItemPress<IEvent>(
                    event,
                    eventModal,
                    setSelectedEvent,
                    0.03
                  )
                }
              >
                <View className="items-center justify-center">
                  <View className="relative h-[38px] w-8">
                    <Image
                      source={require('~/assets/images/explore/event-pin.png')}
                      className="h-[38px] w-8"
                    />
                    <View className="absolute inset-x-0 top-1 items-center">
                      <Image
                        source={require('~/assets/images/explore/event-icon.png')}
                        className="size-[26px] rounded-full"
                      />
                    </View>
                  </View>
                </View>
              </Marker>
            );
          })}
          {filteredSessions.map((session, index) => {
            if (!session.location?.coordinates || selectedTab === 'events')
              return null;
            return (
              <Marker
                key={index}
                onPress={() => {}}
                coordinate={{
                  latitude: session.location.coordinates.lat,
                  longitude: session.location.coordinates.lng,
                }}
                anchor={{ x: 0.5, y: 1.0 }}
              >
                <View className="items-center justify-center">
                  <View className="relative h-[38px] w-8">
                    <Image
                      source={require('~/assets/images/explore/live-pin.png')}
                      className="h-[38px] w-8"
                    />
                    <View className="absolute inset-x-0 top-1 items-center">
                      <Image
                        source={require('~/assets/images/explore/live-icon.png')}
                        className="size-[26px] rounded-full"
                      />
                    </View>
                  </View>
                </View>
              </Marker>
            );
          })}
        </MapView>

        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior="padding"
          keyboardVerticalOffset={10}
        >
          <View className="flex-1">
            <View
              className="flex flex-row items-start justify-center px-2"
              style={{ paddingTop: insets.top + 8 }}
            >
              <View className="pt-2">
                <Back />
              </View>

              <View className="ml-2 flex-1 pb-2">
                <ExploreLocationInput
                  onSelectLocation={onSelectLocation}
                  defaultValue={
                    location
                      ? {
                          ...location,
                          coordinates: {
                            lat: location.coordinates.lat,
                            lng: location.coordinates.lng,
                            latitude: location.coordinates.lat,
                            longitude: location.coordinates.lng,
                          },
                        }
                      : undefined
                  }
                  borderRadius={50}
                  locationAccessible={locationAccessible}
                  openLocationAccessModal={() => setLocationAccessVisible(true)}
                />
              </View>
            </View>
            <View className="flex-row items-center gap-x-2 px-4">
              <TabButton
                label="All"
                value="all"
                selectedTab={selectedTab}
                setSelectedTab={setSelectedTab}
                onPressExtra={() =>
                  setTimeout(() => {
                    thingsModal?.snapToIndex(1);
                  }, 300)
                }
              />
              <TabButton
                label="Events"
                value="events"
                selectedTab={selectedTab}
                setSelectedTab={setSelectedTab}
                onPressExtra={() =>
                  setTimeout(() => {
                    thingsModal?.snapToIndex(1);
                  }, 300)
                }
              />
              <TabButton
                label="Live"
                value="live"
                selectedTab={selectedTab}
                setSelectedTab={setSelectedTab}
                onPressExtra={() =>
                  setTimeout(() => {
                    thingsModal?.snapToIndex(1);
                  }, 300)
                }
              />
            </View>
          </View>
        </KeyboardAvoidingView>
        <Modal
          key={`explore-${modalKeys.explore}`}
          ref={thingsModal.ref}
          snapPoints={snapPoints}
          index={1}
          enablePanDownToClose={false}
          enableContentPanningGesture={
            Platform.OS === 'android' ? false : undefined
          }
          enableDismissOnClose={false}
          backdropComponent={() => null}
        >
          <BottomSheetScrollView className="py-4">
            {!location ? (
              <View className="gap-y-2 px-4">
                <H3>Events and live sessions near you</H3>
                <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                  Set your location to discover events and live sessions nearby.
                </Text>
              </View>
            ) : (
              <>
                {selectedTab !== 'live' && (
                  <>
                    <View className="gap-y-2 px-4">
                      <H3>Events near you</H3>
                      {/* <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                        Some events happening around you
                      </Text> */}
                    </View>
                    <View className="gap-y-4 py-3">
                      {isEventsLoading || isEventsPending ? (
                        <View className="px-4">
                          <Skeleton className="h-56 w-full rounded-lg" />
                        </View>
                      ) : (
                        filteredEvents.length === 0 && (
                          <Text className="mt-2 px-4 text-sm text-fg-muted-light dark:text-fg-muted-dark">
                            No event found close to you.
                          </Text>
                        )
                      )}

                      {filteredEvents.length > 0 && (
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          contentContainerStyle={{ paddingHorizontal: 4 }}
                        >
                          <View className="flex-row items-center gap-x-4">
                            {filteredEvents.map((item, index) => (
                              <MapEventCard
                                key={index}
                                event={item}
                                userLocation={location}
                                onSelectEvent={() => {
                                  if (item.location.coordinates)
                                    onMoveToLocation<IEvent>({
                                      item,
                                      setSelected: setSelectedEvent,
                                      modal: eventModal,
                                      offset: 0.03,
                                    });
                                }}
                                className={cn('ml-0', index === 0 && 'ml-4')}
                              />
                            ))}
                          </View>
                        </ScrollView>
                      )}
                    </View>
                  </>
                )}
                {selectedTab !== 'events' && (
                  <>
                    <View className="gap-y-2 px-4 pt-4">
                      <H3>Live sessions near you</H3>
                      {/* <Text className="text-sm text-fg-muted-light dark:text-fg-muted-dark">
                        Join live sessions around you
                      </Text> */}
                    </View>
                    <View className="gap-y-4 py-3">
                      {isSessionsLoading || isSessionsPending ? (
                        <View className="px-4">
                          <Skeleton className="h-56 w-full rounded-lg px-4" />
                        </View>
                      ) : (
                        filteredSessions.length === 0 && (
                          <Text className="mt-2 px-4 text-sm text-fg-muted-light dark:text-fg-muted-dark">
                            No live session found close to you.
                          </Text>
                        )
                      )}
                      {filteredSessions.length > 0 && (
                        <List
                          data={filteredSessions}
                          estimatedItemSize={12}
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          renderItem={({ item, index }) => (
                            <MapSessionCard
                              key={index}
                              index={index}
                              session={item}
                              userLocation={location}
                              onSelectLiveSession={() => {
                                if (item.location.coordinates)
                                  onMoveToLocation<LiveSessionResponse>({
                                    item,
                                  });
                              }}
                              handleJoinLiveSession={handleJoinLiveSession}
                            />
                          )}
                          ItemSeparatorComponent={() => (
                            <View className="size-4" />
                          )}
                          keyExtractor={(item) => String(item.id)}
                        />
                      )}
                    </View>
                  </>
                )}
              </>
            )}
          </BottomSheetScrollView>
        </Modal>
        <Modal
          key={`event-${modalKeys.event}`}
          ref={eventModal.ref}
          index={0}
          enableDynamicSizing
          enablePanDownToClose={false}
          enableDismissOnClose={false}
          backdropComponent={() => null}
        >
          {selectedEvent && (
            <BottomSheetView
              className="min-h-96 flex-1 gap-y-6 px-4 py-6"
              style={{
                paddingTop: 16,
              }}
            >
              <View className="gap-y-2">
                <XsBoldLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                  {formatDateTime(new Date(selectedEvent.startTime))}
                </XsBoldLabel>
                <H4>{selectedEvent.title}</H4>
              </View>

              <Image
                className="h-[200px] w-full rounded-md"
                source={{ uri: selectedEvent.bannerUrl }}
              />
              <View className="gap-y-1">
                {selectedEvent.isTicketed ? (
                  <>
                    <XsBoldLabel>Ticket starting from</XsBoldLabel>
                    <XsRegularLabel>
                      ₦
                      {
                        Object.values(
                          selectedEvent.ticketCategories ?? {}
                        ).reduce(
                          (cheapest, current) =>
                            current.cost < cheapest.cost ? current : cheapest,
                          { cost: 0 }
                        ).cost
                      }
                    </XsRegularLabel>
                  </>
                ) : (
                  <XsBoldLabel>Free event</XsBoldLabel>
                )}
              </View>
              <View className="gap-y-4">
                <SmRegularLabel>{selectedEvent.description}</SmRegularLabel>
                <Button
                  label="View details"
                  className="rounded-full"
                  onPress={() =>
                    router.push({
                      pathname: '/events/[id]',
                      params: {
                        id: selectedEvent.id,
                        slug: selectedEvent.slug,
                      },
                    })
                  }
                />
                <Button
                  label="Cancel"
                  className="rounded-full"
                  variant="secondary"
                  onPress={() => {
                    thingsModal.present();
                  }}
                />
              </View>
            </BottomSheetView>
          )}
        </Modal>
      </View>
      <LocationAccessDialog
        visible={locationAccessVisible}
        title="Find your vibe nearby!"
        message="To help you discover the best events, live sessions, and people near you, Popla needs access to your location. Don’t worry—we only use your location to personalize your experience and never share it without your permission."
        onCancel={() => setLocationAccessVisible(false)}
        onConfirm={async () => await Linking.openSettings()}
        dismissmLabel="Later"
        confirmLabel="Allow access"
      />
    </View>
  );
}

function TabButton({
  label,
  value,
  selectedTab,
  setSelectedTab,
  onPressExtra,
}: {
  label: string;
  value: 'all' | 'events' | 'live';
  selectedTab: 'all' | 'events' | 'live';
  setSelectedTab: (tab: 'all' | 'events' | 'live') => void;
  onPressExtra?: () => void;
}) {
  const isSelected = selectedTab === value;

  return (
    <Pressable
      onPress={() => {
        setSelectedTab(value);
        onPressExtra?.();
      }}
      className="flex-1"
    >
      <View
        className={`items-center rounded-full py-2 ${
          isSelected
            ? 'bg-accent-moderate dark:bg-accent-moderate'
            : 'bg-accent-subtle-light dark:bg-accent-subtle-dark'
        }`}
      >
        <Text
          className={`text-sm font-medium ${
            isSelected
              ? 'text-white'
              : 'text-accent-bold-light dark:text-accent-bold-dark'
          }`}
        >
          {label}
        </Text>
      </View>
    </Pressable>
  );
}
