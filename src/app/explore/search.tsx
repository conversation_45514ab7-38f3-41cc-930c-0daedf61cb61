import { useQueries } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useDebounce } from 'use-debounce';
import * as z from 'zod';

import { useSearchEvents } from '@/api/events';
import { useSearchLiveSessions } from '@/api/session';
import { useApiSearchUsers } from '@/api/user';
import { EventFavoriteCard } from '@/components/cards/event-favorite';
import { Back } from '@/components/common/back';
import { SearchListItem } from '@/components/explore/search/user-item';
import { MyEventsLoading } from '@/components/loading/my-events';
import { UsersLoading } from '@/components/loading/users';
import {
  EmptyState,
  FocusAwareStatusBar,
  List,
  MdBoldLabel,
  Pressable,
  SafeAreaView,
  ScrollView,
  SearchInput,
  SmBoldLabel,
  View,
} from '@/components/ui';
import { cn } from '@/lib';

const tabs = ['all', 'people', 'events', 'live'] as const;
type Tab = (typeof tabs)[number];

const schema = z.object({
  search: z.string(),
});
export type FormType = z.infer<typeof schema>;

export default function AppSearch() {
  const { search } = useLocalSearchParams<{ search: string }>();
  const { setValue, watch } = useForm<FormType>({ defaultValues: { search } });
  const searchQuery = watch('search');
  const [debouncedText] = useDebounce(searchQuery, 500);

  const [selectedTab, setSelectedTab] = React.useState<Tab>('all');

  const isPeopleTab = selectedTab === 'all' || selectedTab === 'people';
  const isEventsTab = selectedTab === 'all' || selectedTab === 'events';
  const isLiveTab = selectedTab === 'all' || selectedTab === 'live';
  const hasSearch = !!debouncedText;

  const [usersQuery, eventsQuery, liveQuery] = useQueries({
    queries: [
      {
        queryKey: ['searchUsers', debouncedText],
        queryFn: () =>
          useApiSearchUsers.fetcher({
            queryObj: {
              skip: 0,
              take: 5,
              query: debouncedText,
              roles: ['QUESTER', 'CREATOR'],
            },
          }),
        enabled: isPeopleTab && hasSearch,
      },
      {
        queryKey: ['searchEvents', debouncedText],
        queryFn: () =>
          useSearchEvents.fetcher({
            skip: 0,
            take: 5,
            title: debouncedText,
          }),
        enabled: isEventsTab && hasSearch,
      },
      {
        queryKey: ['searchLiveSessions', debouncedText],
        queryFn: () =>
          useSearchLiveSessions.fetcher({
            skip: '0',
            take: '5',
            title: debouncedText,
            status: 'LIVE',
          }),
        enabled: isLiveTab && hasSearch,
      },
    ],
  });

  return (
    <SafeAreaView className="flex-1 px-4">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <View className="gap-2">
          <View className="h-16 flex-row items-center gap-4">
            <Back />
            <View className="flex-1">
              <SearchInput
                value={searchQuery}
                onChangeText={(text) => setValue('search', text)}
                placeholder="Search"
                showClearButton={searchQuery !== ''}
                autoFocus
              />
            </View>
          </View>

          <View className="flex-row gap-2">
            {tabs.map((tab) => {
              const isSelected = selectedTab === tab;
              return (
                <Pressable
                  key={tab}
                  onPress={() => setSelectedTab(tab)}
                  className={cn(
                    'flex-1 items-center rounded-full py-2 transition-all duration-300',
                    isSelected
                      ? 'scale-105 bg-accent-moderate dark:bg-accent-moderate'
                      : 'scale-100 bg-accent-subtle-light dark:bg-accent-subtle-dark'
                  )}
                >
                  <MdBoldLabel
                    className={cn(
                      isSelected
                        ? 'text-accent-on-accent dark:text-accent-on-accent'
                        : 'text-accent-bold-light dark:text-accent-bold-dark'
                    )}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </MdBoldLabel>
                </Pressable>
              );
            })}
          </View>
        </View>

        <ScrollView
          contentContainerClassName="py-4 gap-4"
          showsVerticalScrollIndicator={false}
        >
          {isPeopleTab && usersQuery.data?.users && (
            <Section
              title="People"
              loading={usersQuery.isFetching}
              loadingComponent={<UsersLoading className="px-0" length={5} />}
              data={usersQuery.data.users}
              renderItem={(item) => <SearchListItem user={item} />}
              isSingleTab={selectedTab === 'people'}
            />
          )}

          {isLiveTab && liveQuery.data?.djSessions && (
            <Section
              title="Live"
              loading={liveQuery.isFetching}
              loadingComponent={<UsersLoading className="px-0" length={5} />}
              data={liveQuery.data.djSessions}
              renderItem={(item) => <SearchListItem user={item.dj} isLive />}
              isSingleTab={selectedTab === 'live'}
            />
          )}

          {isEventsTab && eventsQuery.data?.events && (
            <Section
              title="Event"
              loading={eventsQuery.isFetching}
              loadingComponent={<MyEventsLoading itemClassName="mx-0" />}
              data={eventsQuery.data.events}
              renderItem={(item) => (
                <EventFavoriteCard {...item} attendees={item.ticketsSold} />
              )}
              isSingleTab={selectedTab === 'events'}
            />
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

function Section<T extends { id: string }>({
  title,
  loading,
  loadingComponent,
  data,
  renderItem,
}: {
  title: string;
  loading: boolean;
  loadingComponent: React.ReactNode;
  data: T[];
  renderItem: (item: T) => JSX.Element;
  isSingleTab?: boolean;
}) {
  return (
    <View className="gap-3">
      <SmBoldLabel>{title}</SmBoldLabel>
      {loading ? (
        loadingComponent
      ) : (
        <List
          data={data}
          estimatedItemSize={12}
          renderItem={({ item }) => renderItem(item)}
          ListEmptyComponent={<EmptyState type="search" />}
          keyExtractor={(item) => item.id}
          ItemSeparatorComponent={() => <View className="size-3" />}
          scrollEnabled={false}
        />
      )}
    </View>
  );
}
