import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>, useRouter } from 'expo-router';
import React from 'react';
import { FormProvider, type SubmitHandler, useForm } from 'react-hook-form';
import { TouchableWithoutFeedback } from 'react-native';
import { toast } from 'sonner-native';
import * as z from 'zod';

import { queryClient } from '@/api';
import { useLogin } from '@/api/auth/use-login';
import { LoginForm } from '@/components/forms/login-form';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { <PERSON><PERSON>, P } from '@/components/ui';
import { PASSWORD_REGEX, useAuth } from '@/lib';

const schema = z.object({
  email: z
    .string({
      required_error: 'Email is required',
    })
    .email('Invalid email format'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(
      PASSWORD_REGEX.twoLowerCaseRegex,
      'Must contain at least 2 lowercase letters'
    )
    .regex(
      PASSWORD_REGEX.oneUpperCaseRegex,
      'Must contain at least 1 uppercase letter'
    )
    .regex(PASSWORD_REGEX.twoDigitsRegex, 'Must contain at least 1 digits')
    .regex(
      PASSWORD_REGEX.oneSpecialCharacter,
      'Must contain at least 1 special character'
    ),
});

export type FormType = z.infer<typeof schema>;

export default function Login() {
  const router = useRouter();
  const formMethods = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      password: '',
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const { mutate: login, isPending } = useLogin();
  const signIn = useAuth.use.signIn();

  const onSubmit: SubmitHandler<FormType> = (data) => {
    login(data, {
      onSuccess: ({ token, user }) => {
        console.log('login success', data);
        toast.success('Login successfully', {
          position: 'top-center',
          duration: 3000,
          richColors: true,
        });
        signIn(token, user);
        queryClient.invalidateQueries({ queryKey: ['getUser'] });
        router.replace('/');
      },
      onError: (error) => toast.error(error.message),
    });
  };

  const loginFooter = (
    <>
      <P className="text-center">
        Don't have an account?{' '}
        <TouchableWithoutFeedback>
          <Link
            href={{
              pathname: '/(auth)/register',
              params: { registerType: 'email' },
            }}
          >
            <P className="text-fg-link dark:text-fg-link">Sign up</P>
          </Link>
        </TouchableWithoutFeedback>
      </P>
      <Button
        testID="login-button"
        label="Login"
        className="my-4"
        disabled={
          (formMethods.formState.isDirty && !formMethods.formState.isValid) ||
          isPending
        }
        loading={isPending}
        onPress={formMethods.handleSubmit(onSubmit)}
      />
    </>
  );

  return (
    <AuthLayout title="Get back to your vibe" footer={loginFooter}>
      <FormProvider {...formMethods}>
        <LoginForm />
      </FormProvider>
    </AuthLayout>
  );
}
