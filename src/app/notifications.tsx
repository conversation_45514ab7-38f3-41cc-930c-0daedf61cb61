import dayjs from 'dayjs';
import { useRouter } from 'expo-router';
import React from 'react';

import { IconComponent } from '@/components/common/icon-component';
import { BareLayout } from '@/components/layouts';
import { EmptyState } from '@/components/ui';
import {
  colors,
  Image,
  List,
  Text,
  TouchableOpacity,
  View,
} from '@/components/ui';
import {
  isToday,
  type NotificationData,
  renderDateTitle,
  useNotificationStore,
} from '@/lib';
import { NotificationType } from '@/types';

export type SectionItemInfo = {
  title: string;
  data: NotificationData[];
};

export default function Notifications() {
  const { push } = useRouter();

  const notifications = useNotificationStore.use.notifications();
  const updateNotificationList = useNotificationStore.use.getNotificationList();

  React.useEffect(
    () => updateNotificationList(),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const handleNotificationPress = (notification: NotificationData) => {
    const { type, eventId, eventSlug, djSessionId, chatRoomId } = notification;

    switch (type) {
      case NotificationType.EVENT:
        if (eventId) {
          push({
            pathname: '/events/[id]',
            params: { id: eventId, slug: eventSlug },
          });
        }
        break;

      case NotificationType.DJ_SESSION:
        if (djSessionId) {
          // Join session?
          push({ pathname: '/session/[id]', params: { id: djSessionId } });
        }
        break;

      case NotificationType.PRIVATE_CHAT:
        if (chatRoomId) {
          push({ pathname: '/chats/[chatRoomId]', params: { chatRoomId } });
        }
        break;

      default:
        console.warn(
          'Unknown notification type or missing routing data:',
          notification
        );
        break;
    }
  };

  const formatNotificationData = (notifications: NotificationData[]) => {
    const notificationData: SectionItemInfo[] = [];
    notifications.forEach((notification) => {
      const title = renderDateTitle(notification.date);
      const pastDate = dayjs(notification.date);
      const relativeTimeFromNow = pastDate.fromNow();

      const index = notificationData.findIndex((item) => item.title === title);
      if (index === -1) {
        notificationData.push({
          title,
          data: [
            {
              ...notification,
              relativeTimeFromNow: isToday(pastDate) ? relativeTimeFromNow : '',
            },
          ],
        });
      } else {
        notificationData[index].data.push({
          ...notification,
          relativeTimeFromNow: isToday(pastDate) ? relativeTimeFromNow : '',
        });
      }
    });

    return notificationData;
  };

  const data: SectionItemInfo[] = formatNotificationData(notifications);

  return (
    <BareLayout title="Notifications">
      <List
        data={data}
        estimatedItemSize={74}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={<EmptyState />}
        renderItem={({ item: { title, data } }) => (
          <React.Fragment key={title}>
            <Text className="text-xs font-bold text-fg-muted-light dark:text-fg-muted-dark">
              {title}
            </Text>
            {data.map((notification) => (
              <TouchableOpacity
                key={notification.id}
                className="flex-row items-center gap-x-4 py-3"
                onPress={() => handleNotificationPress(notification)}
              >
                {notification.imgUrl ? (
                  <Image
                    className="size-10 rounded-full"
                    source={notification.imgUrl}
                  />
                ) : (
                  <View className="size-10 items-center justify-center rounded-full bg-grey-20">
                    <IconComponent
                      iconType="custom-svg"
                      iconName="MessageDotCircle"
                      color={colors.grey[100]}
                    />
                  </View>
                )}
                <Text className="flex-1 text-sm">{notification.text}</Text>
              </TouchableOpacity>
            ))}
          </React.Fragment>
        )}
        keyExtractor={({ title }) => title}
      />
    </BareLayout>
  );
}
