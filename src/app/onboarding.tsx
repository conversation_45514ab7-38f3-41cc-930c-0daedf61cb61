import { Env } from '@env';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import * as AppleAuthentication from 'expo-apple-authentication';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useVideoPlayer, VideoView } from 'expo-video';
import React from 'react';
import { Platform, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { toast } from 'sonner-native';

import { useSocialLogin } from '@/api/auth';
import {
  Button,
  FocusAwareStatusBar,
  GoogleIcon,
  H1,
  Small,
  View,
} from '@/components/ui';
import {
  openLinkInBrowser,
  PRIVACY_POLICY_URL,
  useAuth,
  useIsFirstTime,
} from '@/lib';

GoogleSignin.configure({ webClientId: Env.GOOGLE_WEB_CLIENT_ID });

export default function Onboarding() {
  const [_, setIsFirstTime] = useIsFirstTime();
  const router = useRouter();
  const signIn = useAuth.use.signIn();

  const onProceed = () => {
    setIsFirstTime(false);
    router.push('/login');
  };

  const { isPending: appleAuthPending, mutate: appleLogin } = useSocialLogin({
    onSuccess: ({ token, user }) => {
      toast.success('Authentication successfully', {
        position: 'top-center',
        richColors: true,
      });
      setIsFirstTime(false);
      if (user.isAccountSet) {
        signIn(token, user);
        router.replace({ pathname: '/(app)' });
      } else {
        router.replace({
          pathname: '/(auth)/register',
          params: {
            registerType: 'social',
            socialEmail: user.email,
          },
        });
      }
    },
    onError: (error) => toast.error(error.message),
  });
  const { isPending: googleAuthPending, mutate: googleLogin } = useSocialLogin({
    onSuccess: ({ token, user }) => {
      toast.success('Authentication successfully', {
        position: 'top-center',
        richColors: true,
      });
      setIsFirstTime(false);
      if (user.isAccountSet) {
        signIn(token, user);
        router.replace({ pathname: '/(app)' });
      } else {
        router.replace({
          pathname: '/(auth)/register',
          params: {
            registerType: 'social',
            socialEmail: user.email,
          },
        });
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const handleAppleAuth = async () => {
    try {
      const { identityToken } = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      appleLogin({ idToken: identityToken, type: 'apple' });
    } catch (e: any) {
      if (e.code === 'ERR_REQUEST_CANCELED') {
        // handle that the user canceled the sign-in flow
      } else {
        // handle other errors
        toast.error(e.message || 'Apple sign in failed');
      }
    }
  };

  const handleGoogleAuth = async () => {
    try {
      await GoogleSignin.signOut();
      await GoogleSignin.hasPlayServices();

      const userInfo = await GoogleSignin.signIn();
      googleLogin({ idToken: userInfo.data?.idToken, type: 'google' });
    } catch (e: any) {
      if (e.code === 'ERR_REQUEST_CANCELED') {
        // handle that the user canceled the sign-in flow
      } else {
        // handle other errors
        toast.error(e.message || 'Apple sign in failed');
        console.log('🚀 ~ handleGoogleAuth ~ error:', e.message);
      }
    }
  };

  const videoSource = require('~/assets/animations/onboarding.mp4');

  const player = useVideoPlayer(videoSource, (player) => {
    player.loop = true;
    player.play();
    player.muted = true;
    player.audioMixingMode = 'doNotMix';
    player.staysActiveInBackground = true;
  });

  return (
    <View className="flex-1">
      <FocusAwareStatusBar
        colorSchemeProp={Platform.OS === 'android' ? 'dark' : 'light'}
        defaultStatusBar={Platform.OS === 'android'}
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
      <VideoView
        style={{ width: '100%', height: '100%' }}
        player={player}
        contentFit="cover"
        nativeControls={false}
      />
      <LinearGradient
        colors={['rgba(0,0,0,0)', 'rgba(0,0,0,1)']}
        locations={[0.41, 0.97]}
        style={styles.overlay}
      />
      <View className="absolute bottom-0 w-full justify-end gap-6 px-4 py-8">
        <H1 className="text-white">Find Your Vibe, Anywhere, Anytime</H1>
        <View className="gap-4">
          <Button
            label="Continue with Apple"
            className="bg-social-apple-primary-dark dark:bg-social-apple-primary-dark"
            textClassName="text-fg-on-contrast dark:text-fg-on-contrast"
            icon={
              <FontAwesome
                name="apple"
                size={24}
                className="text-fg-on-contrast-dark"
              />
            }
            iconPosition="left"
            loading={appleAuthPending}
            disabled={appleAuthPending}
            onPress={handleAppleAuth}
          />
          <Button
            label="Continue with Google"
            className="bg-social-google-primary dark:bg-social-google-primary"
            textClassName="text-fg-static-dark dark:text-fg-static-dark"
            icon={<GoogleIcon />}
            iconPosition="left"
            loading={googleAuthPending}
            disabled={googleAuthPending}
            onPress={handleGoogleAuth}
          />
          <Button onPress={onProceed} label="Continue with email" />
        </View>
        <Small className="text-center text-white">
          By continuing you agree to our{' '}
          <TouchableWithoutFeedback
            onPress={() => router.push({ pathname: '/profile/terms' })}
          >
            <Small className="text-fg-link dark:text-fg-link">
              Terms of Use
            </Small>
          </TouchableWithoutFeedback>{' '}
          and{' '}
          <TouchableWithoutFeedback
            onPress={() => openLinkInBrowser(PRIVACY_POLICY_URL)}
          >
            <Small className="text-fg-link dark:text-fg-link">
              Privacy Policy
            </Small>
          </TouchableWithoutFeedback>
        </Small>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: { ...StyleSheet.absoluteFillObject },
});
