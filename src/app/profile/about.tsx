import { Env } from '@env';
import { type Href, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { BareLayout } from '@/components/layouts';
import {
  ChevronRight,
  Image,
  MdRegularLabel,
  semanticColors,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { openStoreLink, STORE_LINK } from '@/lib';

const OPTIONS = [
  { text: 'Follow us on social media', navigateTo: '/profile/social' },
  { text: 'Feedback', navigateTo: '/profile/feedback' },
  {
    text: 'Rate the app',
    navigateTo: '/profile/social',
    handleExternalLink: () => openStoreLink(STORE_LINK),
  },
];
export default function AboutScreen() {
  const { colorScheme } = useColorScheme();
  const { push } = useRouter();
  const isDark = colorScheme === 'dark';
  return (
    <BareLayout title="About the app">
      <View className="gap-6">
        <View className="items-center gap-3">
          <Image
            className="size-16"
            source={require('~/assets/images/popla.png')}
            contentFit="cover"
          />
          <MdRegularLabel>Popla v {Env.VERSION.toString()}</MdRegularLabel>
        </View>
        <View className="gap-2">
          {OPTIONS.map((option, index) => (
            <TouchableOpacity
              key={index}
              className="h-16 flex-row items-center justify-between gap-4"
              onPress={() =>
                option.handleExternalLink
                  ? option.handleExternalLink()
                  : push(option.navigateTo as Href)
              }
            >
              <MdRegularLabel>{option.text}</MdRegularLabel>
              <ChevronRight
                color={
                  isDark
                    ? semanticColors.fg.base.dark
                    : semanticColors.fg.base.light
                }
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </BareLayout>
  );
}
