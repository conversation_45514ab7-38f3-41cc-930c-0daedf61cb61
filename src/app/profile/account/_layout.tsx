import { Stack } from 'expo-router';

export default function AccountLayout() {
  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="type" options={{ headerShown: false }} />
      <Stack.Screen name="password" options={{ headerShown: false }} />
      <Stack.Screen
        name="confirm-delete-account"
        options={{
          headerShown: false,
          presentation: 'containedModal',
        }}
      />
    </Stack>
  );
}
