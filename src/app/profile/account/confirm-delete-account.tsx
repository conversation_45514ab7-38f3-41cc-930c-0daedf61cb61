import { useRouter } from 'expo-router';
import React from 'react';
import { View } from 'react-native';

import ConfirmDeleteAccountModal from '@/components/modals/confirm-delete-account-modal';
import { FocusAwareStatusBar } from '@/components/ui';

export default function ConfirmDeleteAccount() {
  const router = useRouter();
  const [modalVisible, setModalVisible] = React.useState(true);

  const handleCloseModal = () => {
    setModalVisible(false);
    router.back();
  };
  return (
    <View className="flex-1" style={{ backgroundColor: '#********' }}>
      <FocusAwareStatusBar hidden />
      <ConfirmDeleteAccountModal
        visible={modalVisible}
        onClose={handleCloseModal}
      />
    </View>
  );
}
