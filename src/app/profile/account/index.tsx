import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Back } from '@/components/common/back';
import { AccountItem } from '@/components/profile/account/item';
import { ProfileItem } from '@/components/profile/profile-item';
import { FocusAwareStatusBar, H1, View } from '@/components/ui';
import {
  type ColorSchemeType,
  DELETE_ACCOUNT_ITEM,
  useLoggedInUser,
} from '@/lib';

export default function AccountSettings() {
  const { colorScheme } = useColorScheme();
  const { data: user } = useLoggedInUser();
  const router = useRouter();
  if (!user) return;
  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back />
      </View>
      <H1 className="px-4 pb-4">Account settings</H1>

      <View className="mt-8 gap-2 px-4">
        {user.role === 'QUESTER' ? (
          [
            { id: 1, text: 'Change password' },
            { id: 2, text: 'Account type' },
          ].map((item, index) => (
            <AccountItem
              key={index}
              text={item.text}
              onPress={() => {
                router.navigate(
                  `/profile/account/${index === 0 ? 'password' : '/type'}`
                );
              }}
            />
          ))
        ) : (
          <AccountItem
            text="Change password"
            onPress={() => {
              router.navigate('/profile/account/password');
            }}
          />
        )}

        <ProfileItem
          item={DELETE_ACCOUNT_ITEM(colorScheme as unknown as ColorSchemeType)}
          className="px-0"
        />
      </View>
    </SafeAreaView>
  );
}
