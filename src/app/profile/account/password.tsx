import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { FormProvider, type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import * as z from 'zod';

import { useResetPassword } from '@/api/auth';
import { ForgotPasswordForm } from '@/components/forms/forgot-password-form';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button } from '@/components/ui';

const schema = z.object({
  email: z
    .string({
      required_error: 'Email is required',
    })
    .email('Invalid email format'),
});

export type FormType = z.infer<typeof schema>;

export default function ChangePassword() {
  const router = useRouter();

  const formMethods = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
    },
    mode: 'onChange',
  });

  const { isPending, mutate: resetPassword } = useResetPassword();

  const onSubmit: SubmitHandler<FormType> = (data) => {
    resetPassword(data, {
      onSuccess: () => {
        toast.success('Password reset link sent to your email!');
        router.push('/email-sent');
      },
      onError: (error) => toast.error(error.message),
    });
  };
  return (
    <AuthLayout
      title="Change your password"
      footer={
        <Button
          testID="change-password-button"
          label="Send change instructions"
          className="my-4"
          disabled={!formMethods.formState.isValid || isPending}
          loading={isPending}
          onPress={formMethods.handleSubmit(onSubmit)}
        />
      }
    >
      <FormProvider {...formMethods}>
        <ForgotPasswordForm />
      </FormProvider>
    </AuthLayout>
  );
}
