import { Stack } from 'expo-router';

export default function AccountTypeLayout() {
  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="bank" options={{ headerShown: false }} />
      <Stack.Screen name="bio" options={{ headerShown: false }} />
      <Stack.Screen name="bio-edit" options={{ headerShown: false }} />
      <Stack.Screen name="category" options={{ headerShown: false }} />
    </Stack>
  );
}
