import { useRouter } from 'expo-router';
import { useState } from 'react';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button, View } from '@/components/ui';
import { USER_ROLE_CARDS } from '@/lib';
import { useAuth } from '@/lib';

export default function AccountType() {
  const router = useRouter();
  const user = useAuth.use.user();

  const [selectedRole, setSelectedRole] = useState<string | null>(
    user?.role || null
  );

  return (
    <AuthLayout
      title="Account type"
      subTitle="Choose account type."
      footer={
        <Button
          testID="account-selection-button"
          label="Continue"
          className="my-4"
          disabled={!selectedRole || selectedRole === user?.role}
          onPress={() => {
            if (!selectedRole) return;
            if (!user) return;

            if (selectedRole === 'CREATOR') {
              router.replace('/profile/account/type/category');
            }
          }}
        />
      }
    >
      <View className="mt-4 gap-4">
        {USER_ROLE_CARDS.map((card) => (
          <GradientBorderCard
            key={card.id}
            isSelected={selectedRole === card.id}
            onPress={() => setSelectedRole(card.id)}
            icon={card.icon}
            title={card.title}
            description={card.description}
          />
        ))}
      </View>
    </AuthLayout>
  );
}
