import { <PERSON><PERSON>View } from 'react-native-gesture-handler';

import { BareLayout } from '@/components/layouts';
import { H3, H4, P, UL, View } from '@/components/ui';

export default function CancellationPolicyScreen() {
  return (
    <BareLayout title="Cancellation Policy">
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="gap-y-6">
          <View className="gap-y-2">
            <H3>Introduction</H3>
            <P>
              We understand that plans can change, and we aim to provide a clear
              and fair cancellation policy to accommodate your needs. This
              policy outlines the rules and procedures for cancellations of song
              requests and events facilitated by our app.
            </P>
          </View>
          <View className="gap-y-2">
            <H3>Cancellation of Song Requests</H3>
            <H4>User-Requested Song:</H4>
            <UL
              items={[
                'Users are currently unable to cancel a song request after confirmation of the request.',
                'If your requested song is cancelled, not played by the DJ, Event Organizer or Venue, no charges will apply.',
              ]}
            />
            <H4>Live Session Cancellation:</H4>
            <UL
              items={[
                'If a Live Session is cancelled by the DJ, Venue or Event Organizer, all associated song requests will be automatically cancelled.',
                'Users will not be charged for song requests associated with a cancelled Live Session.',
              ]}
            />
          </View>
          <View className="gap-y-2">
            <H3>Event Ticket Cancellations</H3>
            <H4>Ticket Purchase Cancellation:</H4>
            <UL
              items={[
                'Users are currently unable to cancel their event ticket purchase within the app.',
                'Event Organizer should be contacted for cancellation and refund enquiries.',
              ]}
            />
          </View>
          <View className="gap-y-2">
            <H3>Event Cancellation</H3>
            <UL
              items={[
                'If an event is cancelled by the event organizer, all associated ticket purchases will be automatically cancelled.',
                'Users should contact Event Organizer for a refund of the ticket price, subject to the refund policy of the event organizer.',
              ]}
            />
          </View>
          <View className="gap-y-2">
            <H3>Event Rescheduling</H3>
            <UL
              items={[
                'Event Organizer should be contacted for rescheduled events. New tickets may be available on the App subject to the Event Organizer.',
                'Users unable to attend the rescheduled date may be eligible for a refund if the event organizer provides such an option.',
              ]}
            />
          </View>
          <View className="gap-y-2">
            <H3>Cancellation Fees</H3>
            <H4>User-Requested Song:</H4>
            <UL items={['There are no cancellation fees for song requests.']} />
            <H4>Live Session Cancellation:</H4>
            <UL
              items={[
                'If a Live Session is cancelled by the DJ, Venue or Event Organizer, no cancellation fees will be applied to users.',
              ]}
            />
            <H4>Ticket Purchase Cancellation:</H4>
            <UL
              items={[
                'If a refund is provided for a cancelled or rescheduled event, a cancellation fee may be applied as determined by the Event Organizer or Popla.',
              ]}
            />
          </View>
          <View className="gap-y-2">
            <H3>Refunds</H3>
            <H4>User-Requested Song:</H4>
            <UL
              items={[
                'We do not issue refunds for song requests played. Users will get a full refund of any song request not played.',
              ]}
            />
            <H4>Live Session Cancellation:</H4>
            <UL
              items={[
                'In the event of a Live session cancellation, any charges related to song requests will be refunded to users.',
              ]}
            />
            <H4>Ticket Purchase Cancellation:</H4>
            <UL
              items={[
                `Popla retains the authority to levy a singular, non-refundable
        processing fee referred to as the "Popla Fee" for each ticket acquired through the Platform. You will be duly apprised of both the ticket price and any applicable Popla Fee when purchasing the ticket.`,
                'Refunds for ticket purchases will be subject to the refund policy set by the Event Organizer. These policies will be communicated by the Event Organizer.',
              ]}
            />
            <H4>Event Cancellation:</H4>
            <UL
              items={[
                'In the event of a complete event cancellation by the event organizer, users may be refunded the ticket price according to the event organizer’s refund policy.',
              ]}
            />
          </View>

          <View className="gap-y-2">
            <H3>Communication</H3>
            <P>
              We may notify users of any changes or cancellations related to
              song requests or Events via our App or email.
            </P>
          </View>
          <View className="gap-y-2">
            <H3>Changes to this Policy</H3>
            <P>
              We may update this Cancellation Policy as our App evolves. You
              will be notified of any changes, and it is your responsibility to
              review the updated policy.
            </P>
          </View>
          <View className="gap-y-2">
            <H3>Contact Us:</H3>

            <P>
              If you have any questions or concerns about our Cancellation
              Policy, please contact <NAME_EMAIL>.
            </P>
          </View>
          <P>
            By using the Popla App, you acknowledge and agree to these
            cancellation policies.
          </P>
        </View>
      </ScrollView>
    </BareLayout>
  );
}
