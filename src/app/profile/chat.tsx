import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Back } from '@/components/common/back';
import { NotificationItem } from '@/components/profile/notification/item';
import { FocusAwareStatusBar, H1, View } from '@/components/ui';
import { CHAT_OPTIONS } from '@/lib';

export default function ChatSettings() {
  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back />
      </View>
      <H1 className="px-4 pb-4">Chat preference</H1>
      <View className="mt-8 gap-2 px-4">
        {Array.from({ length: 3 }, (_, index) => ({
          id: index.toString(),
          text: CHAT_OPTIONS[index],
          type: CHAT_OPTIONS[index],
        })).map((item, index) => (
          <NotificationItem
            key={index}
            text={item.text}
            type={item.type}
            nativeID={`${CHAT_OPTIONS[index].toLowerCase()}`}
          />
        ))}
      </View>
    </SafeAreaView>
  );
}
