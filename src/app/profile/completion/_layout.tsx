import { Stack } from 'expo-router';

export default function UserProfileCompletionLayout() {
  return (
    <Stack>
      <Stack.Screen name="preferences" options={{ headerShown: false }} />
      <Stack.Screen name="category" options={{ headerShown: false }} />
      <Stack.Screen name="avatar" options={{ headerShown: false }} />
      <Stack.Screen name="bio" options={{ headerShown: false }} />
      <Stack.Screen name="inspirations" options={{ headerShown: false }} />
    </Stack>
  );
}
