import { useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { Platform } from 'react-native';
import { toast } from 'sonner-native';

import { useUploadProfileImage } from '@/api/user';
import { ProfileCompletionLayout } from '@/components/layouts';
import {
  Button,
  H2,
  Image,
  Modal,
  P,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { useAuth } from '@/lib';

export default function Avatar() {
  const router = useRouter();
  const user = useAuth.use.user();
  const queryClient = useQueryClient();
  type AvatarObj = {
    uri: string;
    type: string;
    name: string;
    size: number | undefined;
  };

  const [avatarObj, setAvatarObj] = useState<AvatarObj | undefined>();
  const photoModal = useModal();

  const { mutate: updateAvatar, isPending } = useUploadProfileImage();

  const [status, requestPermission] = ImagePicker.useMediaLibraryPermissions();

  const pickImage = async () => {
    if (!status) {
      requestPermission();
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      let fileName;
      if (Platform.OS === 'android') {
        fileName = result.assets[0].uri.substring(
          result.assets[0].uri.lastIndexOf('/') + 1
        );
      } else {
        fileName =
          result.assets[0].fileName || result.assets[0].uri.split('/').pop();
      }
      const imageData = {
        uri: result.assets[0].uri,
        type: result.assets[0].mimeType || 'image/jpeg',
        name: fileName || 'image.jpg',
        size: result.assets[0].fileSize,
      };
      setAvatarObj(imageData);
    }
    photoModal.dismiss();
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      alert('Sorry, we need camera permissions to make this work!');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      let fileName;
      if (Platform.OS === 'android') {
        fileName = result.assets[0].uri.substring(
          result.assets[0].uri.lastIndexOf('/') + 1
        );
      } else {
        fileName =
          result.assets[0].fileName || result.assets[0].uri.split('/').pop();
      }
      const imageData = {
        uri: result.assets[0].uri,
        type: result.assets[0].mimeType || 'image/jpeg',
        name: fileName || 'image.jpg',
        size: result.assets[0].fileSize,
      };
      setAvatarObj(imageData);
    }

    photoModal.dismiss();
  };

  if (!user) {
    return;
  }

  const handleSubmit = () => {
    if (!user) return;
    const formDataPayload = new FormData();
    // @ts-ignore
    formDataPayload.append('profileImage', avatarObj);
    updateAvatar(
      {
        userId: user.id,
        payload: formDataPayload,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.dismissAll();
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return (
    <ProfileCompletionLayout
      title="Add a profile picture."
      subTitle="Add a profile picture so your fans know it is you. Everyone can see your profile picture."
      footer={
        <>
          {!avatarObj ? (
            <Button
              testID="add-avatar-button"
              label="Add picture"
              className="mb-2 mt-4"
              onPress={() => photoModal.present()}
            />
          ) : (
            <>
              <Button
                testID="done-avatar-button"
                label="Save"
                loading={isPending}
                className="mb-2"
                onPress={handleSubmit}
              />
              <Button
                testID="edit-avatar-button"
                label="Change picture"
                variant="secondary"
                onPress={() => photoModal.present()}
              />
            </>
          )}
        </>
      }
    >
      <View className="items-center justify-center gap-4">
        <Image
          source={
            avatarObj
              ? { uri: avatarObj.uri }
              : require('~/assets/images/avatar.png')
          }
          className="size-40 rounded-full"
        />
      </View>
      <Modal ref={photoModal.ref} snapPoints={['30%']}>
        <View className="gap-4 px-4 pb-4">
          <View className="justify-start">
            <H2 className="mb-2 px-4">Upload profile picture</H2>
          </View>
          <View className="w-full flex-row justify-center gap-16">
            <TouchableOpacity
              onPress={pickImage}
              className="items-center gap-2.5"
            >
              <View className="items-center justify-center p-3">
                <Image
                  source={require('~/assets/icons/choose-picture.png')}
                  className="size-[84px]"
                  contentFit="contain"
                />
              </View>
              <P>Gallery</P>
            </TouchableOpacity>

            <TouchableOpacity onPress={takePhoto} className="items-center">
              <View className="size-[108px] items-center justify-center gap-2.5 p-3">
                <Image
                  source={require('~/assets/icons/take-picture.png')}
                  className="size-[84px]"
                  contentFit="contain"
                />
              </View>
              <P>Camera</P>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ProfileCompletionLayout>
  );
}
