import Ionicons from '@expo/vector-icons/Ionicons';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useCallback, useState } from 'react';

import { useUpdateProfile } from '@/api/user';
import { BareLayout } from '@/components/layouts';
import BioEditModal from '@/components/modals/edit-bio';
import {
  Button,
  colors,
  H2,
  Modal,
  Pressable,
  Text,
  useModal,
  View,
} from '@/components/ui';
import { useAuth } from '@/lib';

export default function Bio() {
  const router = useRouter();
  const user = useAuth.use.user();
  const queryClient = useQueryClient();

  const genderOptions: {
    label: string;
    value: 'male' | 'female' | 'non-binary';
  }[] = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Non binary', value: 'non-binary' },
  ];

  const genderModal = useModal();

  const [isBioModalVisible, setIsBioModalVisible] = useState(false);

  const [userBio, setUserBio] = useState<string>(user?.bio || '');

  const [userGender, setUserGender] = useState<string>('male');

  const openBioEditor = useCallback(() => {
    setIsBioModalVisible(true);
  }, []);

  const genderLabel =
    genderOptions.find((option) => option.value === userGender)?.label ||
    'Gender';

  const selectGender = useCallback(
    (option: { label: string; value: 'male' | 'female' | 'non-binary' }) => {
      setUserGender(option.value);
      genderModal.dismiss();
    },
    [genderModal]
  );

  const { mutate: updateProfile, isPending } = useUpdateProfile();

  const handleSubmit = () => {
    if (!user) return;

    updateProfile(
      {
        userId: user.id,
        payload: { bio: userBio, gender: userGender },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.dismissAll();
        },
      }
    );
  };

  if (!user) return null;

  return (
    <BareLayout
      title="Tell your audience about you"
      subTitle="We just need a few details to personalize your experience."
      footer={
        <Button
          testID="submit-bio-button"
          label="Continue"
          loading={isPending}
          className="m-4"
          disabled={userBio === '' || userGender === ''}
          onPress={handleSubmit}
        />
      }
    >
      <View className="gap-4">
        <Pressable
          testID="bio-input"
          className="min-h-12 items-start rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
          onPress={openBioEditor}
        >
          <Text
            className={
              userBio
                ? 'dark:text-neutral-100'
                : 'text-neutral-400 dark:text-neutral-500'
            }
            numberOfLines={3}
          >
            {userBio || 'Bio'}
          </Text>
        </Pressable>

        <Pressable
          testID="gender-input"
          className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
          onPress={() => {
            genderModal.present();
          }}
        >
          <Text
            className={
              userGender
                ? 'dark:text-neutral-100'
                : 'text-neutral-400 dark:text-neutral-500'
            }
          >
            {genderLabel}
          </Text>
          <Ionicons name="chevron-down" size={20} color={colors.grey[70]} />
        </Pressable>
      </View>

      <Modal
        ref={genderModal.ref}
        snapPoints={['35%']}
        backgroundStyle={{
          backgroundColor: '#131214',
        }}
      >
        <View className="items-start gap-4 px-4">
          <H2 className="font-bold leading-120">Select gender</H2>
          {genderOptions.map((option) => (
            <Pressable
              key={option.value}
              className="w-full flex-row items-center justify-between"
              onPress={() => selectGender(option)}
            >
              <Text className="py-4 text-base font-bold dark:text-neutral-100">
                {option.label}
              </Text>
            </Pressable>
          ))}
        </View>
      </Modal>
      <BioEditModal
        visible={isBioModalVisible}
        bioText={userBio}
        onConfirm={(newBio) => {
          setUserBio(newBio);
          setIsBioModalVisible(false);
        }}
        onCancel={() => {
          setIsBioModalVisible(false);
        }}
      />
    </BareLayout>
  );
}
