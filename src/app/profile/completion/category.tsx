import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useState } from 'react';

import { useUpdateProfile } from '@/api/user';
import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { ProfileCompletionLayout } from '@/components/layouts';
import { Button, ScrollView } from '@/components/ui';
import { CREATOR_CATEGORY_CARDS } from '@/lib';
import { useAuth } from '@/lib';

export default function Category() {
  const router = useRouter();
  const user = useAuth.use.user();
  const queryClient = useQueryClient();

  const [category, setCategory] = useState<string | null>(null);

  const { mutate: updateProfile, isPending } = useUpdateProfile();

  const handleSubmit = () => {
    if (!category) return;

    if (!user) return;

    updateProfile(
      {
        userId: user.id,
        payload: { category },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.dismissAll();
        },
      }
    );
  };

  return (
    <ProfileCompletionLayout
      title="What best describes you?"
      subTitle="Choose a category one that best represents your role."
      footer={
        <Button
          testID="category-selection-button"
          label="Continue"
          loading={isPending}
          className="my-4"
          disabled={!category}
          onPress={handleSubmit}
        />
      }
    >
      <ScrollView>
        {CREATOR_CATEGORY_CARDS.map((card) => (
          <GradientBorderCard
            key={card.id}
            isSelected={category === card.id}
            onPress={() => setCategory(card.id)}
            icon={card.icon}
            title={card.title}
            description={card.description ?? undefined}
          />
        ))}
      </ScrollView>
    </ProfileCompletionLayout>
  );
}
