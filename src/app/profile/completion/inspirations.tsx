import React from 'react';

import { useGetTrendingCreators } from '@/api/user';
import { FavouriteCard } from '@/components/cards/favourite-card';
import { CompletionItemLayout } from '@/components/layouts/completion-item';
import LoadingScreen from '@/components/loading';
import { List, View } from '@/components/ui';

export default function Inspirations() {
  const { data: trendingCreators, isPending: creatorsPending } =
    useGetTrendingCreators();

  if (creatorsPending) return <LoadingScreen />;

  return (
    <CompletionItemLayout
      title="Get inspiration"
      subTitle="Favourite other creators to gain insight on the type of content they share and how they engage with their audience."
      showDoneButton
    >
      <View className="flex-1 rounded-[20px] bg-grey-20 p-4 dark:bg-slate-800">
        <List
          data={trendingCreators}
          estimatedItemSize={12}
          showsVerticalScrollIndicator={false}
          renderItem={({ item: card }) => (
            <FavouriteCard
              key={card.id}
              id={card.id}
              image={card.profileImageUrl}
              name={card.fullName}
              username={card.username}
            />
          )}
          ItemSeparatorComponent={() => <View className="size-4" />}
          keyExtractor={(item) => String(item.id)}
        />
      </View>
    </CompletionItemLayout>
  );
}
