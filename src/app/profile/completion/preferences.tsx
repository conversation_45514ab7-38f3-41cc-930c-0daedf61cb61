import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useState } from 'react';

import { useUpdateProfile } from '@/api/user';
import GenreItem from '@/components/genre/genre-item';
import { ProfileCompletionLayout } from '@/components/layouts';
import { Button, View } from '@/components/ui';
import { GENRES, useAuth } from '@/lib';

export default function Preferences() {
  const router = useRouter();
  const user = useAuth.use.user();
  const queryClient = useQueryClient();

  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);

  const handleToggleGenre = (genreId: string) => {
    let newSelectedGenres;

    if (selectedGenres.includes(genreId)) {
      newSelectedGenres = selectedGenres.filter((id) => id !== genreId);
    } else {
      newSelectedGenres = [...selectedGenres, genreId];
    }

    setSelectedGenres(newSelectedGenres);
  };

  const { mutate: updateProfile, isPending } = useUpdateProfile();

  const handleSubmit = () => {
    if (!user) return;
    updateProfile(
      {
        userId: user.id,
        payload: { genres: selectedGenres },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.dismissAll();
        },
      }
    );
  };

  return (
    <ProfileCompletionLayout
      title="What’s your vibe?"
      subTitle="Tell us what you love to discover events and artists tailored just for you."
      footer={
        <Button
          testID="preferences-button"
          label="Continue"
          className="my-4"
          disabled={selectedGenres.length === 0 || isPending}
          loading={isPending}
          onPress={handleSubmit}
        />
      }
    >
      <View className="flex-row flex-wrap justify-between gap-x-4 gap-y-6 px-2">
        {GENRES.map((genre) => (
          <GenreItem
            key={genre.id}
            genre={genre.name}
            imageSource={genre.image}
            isSelected={selectedGenres.includes(genre.id)}
            onToggle={() => handleToggleGenre(genre.id)}
          />
        ))}
      </View>
    </ProfileCompletionLayout>
  );
}
