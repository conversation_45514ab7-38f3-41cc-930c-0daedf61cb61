import { useRouter } from 'expo-router';
import React from 'react';
import { View } from 'react-native';

import ConfirmLogoutModal from '@/components/modals/confirm-logout';
import { FocusAwareStatusBar } from '@/components/ui';

export default function ConfirmLogout() {
  const router = useRouter();
  const [modalVisible, setModalVisible] = React.useState(true);

  const handleCloseModal = () => {
    setModalVisible(false);
    router.back();
  };
  return (
    <View className="flex-1" style={{ backgroundColor: '#00000045' }}>
      <FocusAwareStatusBar hidden />
      <ConfirmLogoutModal visible={modalVisible} onClose={handleCloseModal} />
    </View>
  );
}
