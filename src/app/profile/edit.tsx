import { useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { Platform, StyleSheet } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { useUploadProfileImage } from '@/api/user';
import { Back } from '@/components/common/back';
import { EditUserDetailsForm } from '@/components/forms/edit-user-details';
import { SocialMediaForm } from '@/components/forms/social-media-form';
import LoadingScreen from '@/components/loading';
import { SectionHeader } from '@/components/profile/section-header';
import {
  Button,
  colors,
  FocusAwareStatusBar,
  Image,
  LgBoldLabel,
  MdBold<PERSON>abel,
  ScrollView,
  TouchableOpacity,
  View,
} from '@/components/ui';
import {
  AccountSetupProvider,
  useEditAccountForm,
  useLoggedInUser,
  useProfileImage,
} from '@/lib';

export default function EditProfile() {
  const { data: currentUser } = useLoggedInUser();
  const queryClient = useQueryClient();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { imageKey, refreshImage, getProfileImageUrl } = useProfileImage();

  const {
    formMethods,
    usernameRefinement,
    isValidatingUsername,
    setIsValidatingUsername,
    onSubmit,
    accountUpdating,
  } = useEditAccountForm();

  const { mutate: updateAvatar, isPending } = useUploadProfileImage();

  const [status, requestPermission] = ImagePicker.useMediaLibraryPermissions();

  const pickImage = async () => {
    if (!status) {
      requestPermission();
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      let fileName;
      if (Platform.OS === 'android') {
        fileName = result.assets[0].uri.substring(
          result.assets[0].uri.lastIndexOf('/') + 1
        );
      } else {
        fileName =
          result.assets[0].fileName || result.assets[0].uri.split('/').pop();
      }
      const imageData = {
        uri: result.assets[0].uri,
        type: result.assets[0].mimeType || 'image/jpeg',
        name: fileName || 'image.jpg',
        size: result.assets[0].fileSize,
      };
      const formDataPayload = new FormData();
      // @ts-ignore
      formDataPayload.append('profileImage', imageData);
      updateAvatar(
        {
          userId: currentUser?.id || '',
          payload: formDataPayload,
        },
        {
          onSuccess: () => {
            refreshImage();
            queryClient.invalidateQueries({ queryKey: ['getUser'] });
          },
          onError: (error) => toast.error(error.message),
        }
      );
    }
  };

  return (
    <View className="flex-1">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <View className="h-[359px] bg-white">
          {/* Bg Image + Gradient */}
          {isPending ? (
            <LoadingScreen />
          ) : (
            <Image
              key={imageKey || 'profile-image'}
              className="size-full flex-1 overflow-hidden rounded-lg"
              source={
                getProfileImageUrl(currentUser?.profileImageUrl) ||
                require('~/assets/images/avatar.png')
              }
              contentFit="cover"
              priority="high"
            />
          )}
          <LinearGradient
            colors={[
              'transparent',
              isDark ? colors.grey[100] : colors.grey[10],
            ]}
            locations={[0, 0.93]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={styles.overlay}
          >
            {/* Header */}
            <View
              className="h-16 flex-row items-center justify-between px-2"
              style={{
                marginTop: insets.top,
              }}
            >
              <Back />
              <LgBoldLabel>Edit profile</LgBoldLabel>
              <View className="size-8" />
            </View>
            <TouchableOpacity
              className="mx-auto mb-4 mt-auto px-4 py-2"
              onPress={pickImage}
            >
              <MdBoldLabel className="text-brand-70 dark:text-brand-40">
                Change profile image
              </MdBoldLabel>
            </TouchableOpacity>
          </LinearGradient>
        </View>
        <AccountSetupProvider
          value={{
            usernameRefinement,
            isValidatingUsername,
            setIsValidatingUsername,
          }}
        >
          <FormProvider {...formMethods}>
            <ScrollView
              className="bg-bg-canvas flex-1 dark:bg-grey-100"
              showsVerticalScrollIndicator={false}
            >
              <View
                className="gap-4"
                style={{
                  paddingBottom: insets.bottom,
                }}
              >
                <EditUserDetailsForm />
                <SectionHeader title="Social media" />
                <SocialMediaForm />
                <Button
                  testID="save-profile-edit-button"
                  label="Save"
                  className="mx-4"
                  disabled={isValidatingUsername || accountUpdating}
                  loading={accountUpdating}
                  onPress={formMethods.handleSubmit(onSubmit)}
                />
              </View>
            </ScrollView>
          </FormProvider>
        </AccountSetupProvider>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: { ...StyleSheet.absoluteFillObject },
  card: { justifyContent: 'space-between', borderRadius: 8, padding: 16 },
});
