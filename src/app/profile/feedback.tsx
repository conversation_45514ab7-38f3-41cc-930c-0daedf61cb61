import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import { z } from 'zod';

import { useSendFeedback } from '@/api/user';
import { BareLayout } from '@/components/layouts';
import { Button, ControlledInputWithoutContext } from '@/components/ui';
import { useAuth } from '@/lib';

const schema = z.object({
  feedback: z.string().trim().min(1, 'Feedback is required'),
});

type FeedbackFormType = z.infer<typeof schema>;

export default function Feedback() {
  const user = useAuth.use.user();
  const router = useRouter();

  const form = useForm<FeedbackFormType>({
    defaultValues: { feedback: '' },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const { control, watch, handleSubmit } = form;
  const feedback = watch('feedback');

  const { mutate: sendFeedback, isPending } = useSendFeedback();

  const onSubmit = (data: FeedbackFormType) => {
    if (!user) return;

    sendFeedback(
      {
        userId: user.id,
        payload: {
          email: user.email,
          fullName: user.fullName,
          message: data.feedback,
        },
      },
      {
        onError: () => toast.error('Error sending feedback'),
        onSuccess: () => {
          toast.success('Feedback sent successfully');
          router.back();
        },
      }
    );
  };

  if (!user) return null;

  return (
    <BareLayout
      title="Feedback"
      subTitle="Tell us about your experience with the Popla app."
      footer={
        <Button
          testID="submit-feedback-button"
          label="Submit"
          className="m-4"
          disabled={!feedback || isPending}
          loading={isPending}
          onPress={handleSubmit(onSubmit)}
        />
      }
    >
      <ControlledInputWithoutContext
        name="feedback"
        label="What's on your mind?"
        control={control}
        multiline
        numberOfLines={5}
        inputClassName="h-[100px]"
      />
    </BareLayout>
  );
}
