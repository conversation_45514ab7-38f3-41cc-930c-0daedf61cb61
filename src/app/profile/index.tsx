import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Back } from '@/components/common/back';
import { ProfileGradientBg, styling } from '@/components/profile/gradient-bg';
import { ProfileItem } from '@/components/profile/profile-item';
import { SectionHeader } from '@/components/profile/section-header';
import {
  FocusAwareStatusBar,
  H4,
  Image,
  ScrollView,
  SmBoldLabel,
  TouchableOpacity,
  View,
  XsRegularLabel,
} from '@/components/ui';
import {
  type ColorSchemeType,
  LOGOUT_ITEM,
  PROFILE_SECTIONS,
  useLoggedInUser,
  useProfileImage,
} from '@/lib';

export default function Profile() {
  const { data: user } = useLoggedInUser();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  const { imageKey, getProfileImageUrl } = useProfileImage();

  return (
    <View className="flex-1">
      <View className="relative">
        <FocusAwareStatusBar defaultStatusBar translucent />
        <View style={styles.statusBarOverlay} />
        <BlurView tint="prominent" intensity={50} style={styles.blurView} />
        <ProfileGradientBg
          image={
            getProfileImageUrl(user?.profileImageUrl) ||
            require('~/assets/images/avatar.png')
          }
        />
        <View className="relative" style={{ paddingTop: insets.top }}>
          {/* Background image + blur view + dark/white overlay */}
          <View className="px-2 py-4">
            <Back />
          </View>

          <View className="mb-10 flex-row items-center gap-4 px-4 py-1.5">
            <Image
              key={imageKey || 'profile-avatar'}
              className="size-20 rounded-full"
              source={
                getProfileImageUrl(user?.profileImageUrl) ||
                require('~/assets/images/avatar.png')
              }
              priority="high"
            />
            <View className="gap-2">
              <H4 className="text-white">{user?.fullName}</H4>
              <XsRegularLabel className="text-white">
                @{user?.username}
              </XsRegularLabel>
              <TouchableOpacity
                className="w-16 items-center justify-center rounded-full border border-white px-4 py-1"
                onPress={() => router.navigate('/profile/edit')}
              >
                <SmBoldLabel className="text-white">Edit</SmBoldLabel>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
      <ScrollView
        className="bg-bg-canvas flex-1 dark:bg-grey-100"
        showsVerticalScrollIndicator={false}
      >
        <View className="relative overflow-hidden">
          <React.Fragment>
            {/* Profile Section */}
            <View
              className="bg-bg-canvas dark:bg-grey-100"
              style={{
                paddingBottom: insets.bottom,
              }}
            >
              {PROFILE_SECTIONS.map((section, sectionIndex) => (
                <View key={`section-${sectionIndex}`}>
                  {/* header */}
                  <SectionHeader title={section.title} />
                  {/* Items */}
                  {section.items.map((item, itemIndex) => (
                    <ProfileItem
                      key={`item-${sectionIndex}-${itemIndex}`}
                      item={item}
                    />
                  ))}
                </View>
              ))}
              <ProfileItem
                item={LOGOUT_ITEM(colorScheme as unknown as ColorSchemeType)}
              />
            </View>
          </React.Fragment>
        </View>
      </ScrollView>
    </View>
  );
}
