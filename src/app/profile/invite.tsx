import { useRouter } from 'expo-router';
import { useRef } from 'react';

import { AuthLayout } from '@/components/layouts/auth-layout';
import { ShareModal } from '@/components/modals/share';
import { Button, Text, View } from '@/components/ui';

export default function Invite() {
  const router = useRouter();
  const shareModalRef = useRef<any>(null);

  return (
    <AuthLayout
      title="Invite Friends"
      subTitle="Grow your audience by inviting friends to follow your account."
      footer={
        <Button
          testID="category-selection-button"
          label="Continue"
          className="my-4"
          onPress={() => router.push('/(auth)/completion')}
        />
      }
    >
      <View className="gap-4">
        <Button
          label="Share link"
          onPress={() => shareModalRef.current?.present()}
          className="w-1/3"
          textClassName="font-extrabold"
        />
      </View>

      <View className="my-6 px-4">
        <View className="rounded-xl border border-fg-muted-light bg-bg-subtle-light px-4 py-3 dark:border-fg-muted-dark dark:bg-bg-subtle-dark">
          <Text className="text-fg-default-light dark:text-fg-default-dark text-sm">
            You have been invited to Vibes with Dj Mekkzy.{'\n'}
            https://app.popla.com/event/ifae3ae{'\n'}
            Your exclusive code is : ZYNU7
          </Text>
        </View>
      </View>

      <ShareModal
        shareModalRef={shareModalRef}
        onDismiss={() => shareModalRef.current?.dismiss()}
        content="https://app.popla.com/event/ifae3ae"
      />
    </AuthLayout>
  );
}
