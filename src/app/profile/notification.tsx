import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Back } from '@/components/common/back';
import { NotificationItem } from '@/components/profile/notification/item';
import { FocusAwareStatusBar, H1, View } from '@/components/ui';
import { NOTIFICATION_TYPES } from '@/lib';

export default function NotificationSettings() {
  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back />
      </View>
      <H1 className="px-4 pb-4">Notifications</H1>
      <View className="mt-8 gap-2 px-4">
        {Array.from({ length: NOTIFICATION_TYPES.length }, (_, index) => ({
          id: index.toString(),
          text: `${NOTIFICATION_TYPES[index]} notification`,
          type: NOTIFICATION_TYPES[index],
        })).map((item, index) => (
          <NotificationItem
            key={index}
            text={item.text}
            type={item.type}
            nativeID={`${NOTIFICATION_TYPES[index].toLowerCase()}-notification`}
          />
        ))}
      </View>
    </SafeAreaView>
  );
}
