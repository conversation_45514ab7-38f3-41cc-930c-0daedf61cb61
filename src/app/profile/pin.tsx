import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { toast } from 'sonner-native';

import { useCreatePin, useValidatePin } from '@/api/user';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button, ChevronLeft, colors, H3, P, View } from '@/components/ui';
import { cn, useLoggedInUser } from '@/lib';

export default function Pin() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(1);
  const [error, setError] = useState('');

  const { data: user } = useLoggedInUser();

  const userHasPin = !!user?.pin;

  const { mutate: createPin, isPending: creatingPin } = useCreatePin();
  const { mutate: validatePin, isPending: validatingPin } = useValidatePin();

  const onCreatePin = () => {
    createPin(
      { pin: confirmPin },
      {
        onSuccess: () => {
          toast.success('Pin saved successfully');
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.back();
        },
        onError: (error) => {
          setError(error.message);
          toast.error(error.message);
        },
      }
    );
  };

  const onUpdatePin = (mode: 'verify' | 'update') => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid) => {
          if (isValid) {
            if (mode === 'verify') {
              setStep(2);
              setConfirmPin('');
            } else {
              onCreatePin();
            }
          } else {
            if (mode === 'verify') {
              setError('Invalid PIN');
              setPin('');
            } else {
              setError("Previous Pin doesn't match");
              toast.error("Previous Pin doesn't match");
            }
          }
        },
        onError: (error) => {
          setError(error.message);
          toast.error(error.message);
          setPin('');
        },
      }
    );
  };

  const handleKeyPress = (key: string) => {
    const currentPin = step === 1 ? pin : confirmPin;

    if (key === 'delete') {
      if (step === 1) {
        setPin((prev) => prev.slice(0, -1));
      } else {
        setConfirmPin((prev) => prev.slice(0, -1));
      }
      setError('');
    } else if (currentPin.length < 4) {
      if (step === 1) {
        const newPin = pin + key;
        setPin(newPin);

        // if (newPin.length === 4) {
        //   setTimeout(() => setStep(2), 300);
        // }
      } else {
        setConfirmPin((prev) => prev + key);
      }
      setError('');
    }
  };

  const handleContinue = () => {
    if (step === 1 && pin.length === 4) {
      if (userHasPin) {
        onUpdatePin('verify');
      } else {
        setStep(2);
        setConfirmPin('');
      }
    } else if (step === 2) {
      if (userHasPin) {
        onUpdatePin('update');
      } else {
        if (pin === confirmPin) {
          onCreatePin();
        } else {
          setError('PINs do not match. Please try again.');
          setConfirmPin('');
        }
      }
    }
  };

  const handleBackPress = () => {
    if (step === 1) {
      router.back();
    } else if (step === 2) {
      setStep(1);
      setError('');
    }
  };

  const renderKey = (value: string) => {
    const isDelete = value === 'delete';
    const isDisabled =
      isDelete &&
      ((step === 1 && pin.length === 0) ||
        (step === 2 && confirmPin.length === 0));

    return (
      <TouchableOpacity
        key={value}
        onPress={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'items-center justify-center',
          isDisabled && 'opacity-50'
        )}
        activeOpacity={0.7}
        disabled={isDisabled}
        hitSlop={20}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3 className="text-accent-moderate dark:text-accent-moderate">
            {value}
          </H3>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <AuthLayout
      title={
        step === 1
          ? `${userHasPin ? 'Update' : 'Create'} transaction PIN`
          : `${userHasPin ? 'New' : 'Confirm'} transaction PIN`
      }
      subTitle={
        step === 1
          ? userHasPin
            ? 'Enter your previous PIN to update your existing one'
            : 'You will need this to authorize transactions'
          : userHasPin
            ? 'Enter your new PIN to complete the update'
            : 'Re-enter your PIN to confirm'
      }
      onBackPress={handleBackPress}
    >
      <View className="flex-1 items-center justify-between py-8">
        <View className="items-center">
          <View className="flex-row gap-4">
            {[0, 1, 2, 3].map((index) => {
              const currentPin = step === 1 ? pin : confirmPin;
              const isFilled = index < currentPin.length;

              return (
                <View
                  key={index}
                  className={cn(
                    'size-14 items-center justify-center rounded-full border-2',
                    isFilled
                      ? 'border-accent-moderate dark:border-accent-moderate'
                      : 'border-grey-20 dark:border-grey-80'
                  )}
                >
                  {isFilled && (
                    <View className="size-1 rounded-full bg-black dark:bg-white" />
                  )}
                </View>
              );
            })}
          </View>

          {error && <P className="my-4 text-center text-red-500">{error}</P>}
        </View>

        <Button
          testID="submit-pin-button"
          label="Continue"
          className="w-full"
          disabled={
            (step === 1 && pin.length < 4) ||
            (step === 2 && confirmPin.length < 4) ||
            creatingPin ||
            validatingPin
          }
          loading={creatingPin || validatingPin}
          onPress={handleContinue}
        />

        <View className="w-full gap-16">
          <View className="flex-row justify-around">
            {['1', '2', '3'].map(renderKey)}
          </View>
          <View className="flex-row justify-around">
            {['4', '5', '6'].map(renderKey)}
          </View>
          <View className="flex-row justify-around">
            {['7', '8', '9'].map(renderKey)}
          </View>
          <View className="flex-row justify-around">
            <View className="size-7" />
            {renderKey('0')}
            {renderKey('delete')}
          </View>
        </View>
      </View>
    </AuthLayout>
  );
}
