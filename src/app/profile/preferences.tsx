import { zodResolver } from '@hookform/resolvers/zod';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import { z } from 'zod';

import { useUpdateProfile } from '@/api/user';
import GenreItem from '@/components/genre/genre-item';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button, View } from '@/components/ui';
import { GENRES, useLoggedInUser } from '@/lib';

const schema = z.object({
  genres: z.array(z.string()).min(1, 'Please select at least one genre'),
});

export default function Preferences() {
  const { data: user } = useLoggedInUser();
  const { watch, setValue, handleSubmit } = useForm<z.infer<typeof schema>>({
    defaultValues: { genres: user?.genres || [] },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });
  const selectedGenres = watch('genres') || [];

  const handleToggleGenre = (genreId: string) => {
    let newSelectedGenres;

    if (selectedGenres.includes(genreId)) {
      newSelectedGenres = selectedGenres.filter((id) => id !== genreId);
    } else {
      newSelectedGenres = [...selectedGenres, genreId];
    }

    setValue('genres', newSelectedGenres, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const { mutate: updateProfile } = useUpdateProfile();

  const onSubmit: SubmitHandler<z.infer<typeof schema>> = (data) => {
    if (!user) return;
    updateProfile(
      {
        userId: user.id,
        payload: { genres: data.genres },
      },
      {
        onSuccess: () => {
          toast.success('Preferences updated successfully');
        },
      }
    );
  };

  return (
    <AuthLayout
      title="Content preference"
      footer={
        <Button
          testID="preferences-button"
          label="Save"
          className="my-4"
          disabled={watch('genres').length === 0}
          onPress={handleSubmit(onSubmit)}
        />
      }
    >
      <View className="flex-row flex-wrap justify-between gap-x-4 gap-y-6 px-2">
        {GENRES.map((genre) => (
          <GenreItem
            key={genre.id}
            genre={genre.name}
            imageSource={genre.image}
            isSelected={selectedGenres.includes(genre.id)}
            onToggle={() => handleToggleGenre(genre.id)}
          />
        ))}
      </View>
    </AuthLayout>
  );
}
