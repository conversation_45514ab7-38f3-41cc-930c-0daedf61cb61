import { zodResolver } from '@hookform/resolvers/zod';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import { z } from 'zod';

import { useGetEvent } from '@/api/events';
import { useGetRequestHistory } from '@/api/session';
import { useReportProblem } from '@/api/user';
import { BareLayout } from '@/components/layouts';
import {
  ActivityIndicator,
  Button,
  ControlledInputWithoutContext,
  Select,
} from '@/components/ui';
import { useAuth } from '@/lib';

export enum ReportEnum {
  LIVE_SESSION = 'LIVE_SESSION',
  EVENT = 'EVENT',
  APP = 'APP',
  TRANSACTION = 'TRANSACTION',
}

const categoryOptions = [
  { label: 'Report an app issue', value: ReportEnum.APP },
  { label: 'Report a Live session issue', value: ReportEnum.LIVE_SESSION },
  { label: 'Report an event issue', value: ReportEnum.EVENT },
  { label: 'Report a transaction issue', value: ReportEnum.TRANSACTION },
];

const schema = z.discriminatedUnion('type', [
  z.object({
    title: z.string().trim().min(1, 'Title is required'),
    type: z.literal(ReportEnum.LIVE_SESSION),
    description: z.string().trim().min(1, 'Description is required'),
    songRequestId: z.string().min(1, 'Required'),
  }),
  z.object({
    title: z.string().trim().min(1, 'Title is required'),
    type: z.literal(ReportEnum.EVENT),
    description: z.string().trim().min(1, 'Description is required'),
    eventId: z.string().min(1, 'Required'),
  }),
  z.object({
    title: z.string().trim().min(1, 'Title is required'),
    type: z.literal(ReportEnum.APP),
    description: z.string().trim().min(1, 'Description is required'),
  }),
  z.object({
    title: z.string().trim().min(1, 'Title is required'),
    type: z.literal(ReportEnum.TRANSACTION),
    description: z.string().trim().min(1, 'Description is required'),
    transactionId: z.string().min(1, 'Required'),
  }),
]);

type ProblemFormType = z.infer<typeof schema>;

export default function ReportProblem() {
  const user = useAuth.use.user();
  const router = useRouter();
  const localSearchParams = useLocalSearchParams();
  const songRequestId = Array.isArray(localSearchParams.songRequestId)
    ? localSearchParams.songRequestId[0]
    : (localSearchParams.songRequestId ?? '');
  const sessionName = Array.isArray(localSearchParams.sessionName)
    ? localSearchParams.sessionName[0]
    : (localSearchParams.sessionName ?? '');
  const shoutOutTo = Array.isArray(localSearchParams.shoutOutTo)
    ? localSearchParams.shoutOutTo[0]
    : (localSearchParams.shoutOutTo ?? '');
  const hasShoutout = !!shoutOutTo;

  const trxId = Array.isArray(localSearchParams.trxId)
    ? localSearchParams.trxId[0]
    : (localSearchParams.trxId ?? '');

  const eventId = Array.isArray(localSearchParams.eventId)
    ? localSearchParams.eventId[0]
    : (localSearchParams.eventId ?? '');

  const event = useGetEvent({ variables: { id: eventId }, enabled: !!eventId });

  const readonlyType = !!songRequestId || !!trxId || !!eventId;

  // const transaction = useGetTransaction({
  //   variables: { id: trxId },
  //   enabled: !!trxId,
  // });

  const form = useForm<ProblemFormType>({
    defaultValues: {
      title: '',
      type: songRequestId
        ? ReportEnum.LIVE_SESSION
        : trxId
          ? ReportEnum.TRANSACTION
          : eventId
            ? ReportEnum.EVENT
            : ReportEnum.APP,
      description: '',
      ...(songRequestId && { songRequestId }),
      ...(trxId && { transactionId: trxId }),
      ...(eventId && { eventId }),
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const {
    control,
    watch,
    handleSubmit,
    setValue,
    formState: { isValid },
  } = form;
  const type = watch('type');

  const { isLoading, data } = useGetRequestHistory({
    variables: { take: 10 },
    enabled: type === ReportEnum.LIVE_SESSION,
  });

  const allSessionRequests = React.useMemo(() => {
    return data?.pages?.flatMap((sessionRequests) => sessionRequests) || [];
  }, [data?.pages]);

  const { mutate: reportProblem, isPending } = useReportProblem();

  const onSubmit: SubmitHandler<ProblemFormType> = (data) => {
    reportProblem(
      { ...data },
      {
        onError: () => toast.error('Error reporting problem'),
        onSuccess: () => {
          toast.success('Problem reported successfully');
          router.back();
        },
      }
    );
  };

  if (!user) return null;

  const subtitle = (() => {
    switch (type) {
      case ReportEnum.LIVE_SESSION:
        return sessionName
          ? `Tell us the problem you faced during the "${sessionName}" live session.`
          : 'Tell us the problem you faced during a live session.';
      case ReportEnum.EVENT:
        return event?.data?.title
          ? `Tell us the problem you faced during the "${event.data.title}" event.`
          : 'Tell us the problem you faced during an event.';
      case ReportEnum.TRANSACTION:
        return trxId
          ? `Tell us the problem you faced with transaction "${trxId}".`
          : 'Tell us the problem you faced with a transaction.';
      case ReportEnum.APP:
      default:
        return 'Tell us the problem you faced while using the app.';
    }
  })();

  return (
    <BareLayout
      title="Report a problem"
      subTitle={subtitle}
      footer={
        <Button
          testID="submit-problem-button"
          label="Submit"
          className="m-4"
          disabled={!isValid || isPending}
          loading={isPending}
          onPress={handleSubmit(onSubmit)}
        />
      }
    >
      <ControlledInputWithoutContext
        name="title"
        label="Subject"
        control={control}
        textAlignVertical="top"
      />

      <Select
        title="Select category"
        onSelect={(value) => {
          setValue('type', value as ReportEnum);
        }}
        placeholder="Select category"
        options={categoryOptions}
        value={type}
        disabled={readonlyType}
      />

      {type === ReportEnum.LIVE_SESSION && (
        <>
          {isLoading ? (
            <ActivityIndicator />
          ) : (
            <Select
              title="Select Song Request"
              onSelect={(value) => {
                setValue('songRequestId', value as string);
              }}
              placeholder="Song Request ID"
              options={allSessionRequests?.map((session) => ({
                label: `${session.djSession.title} - ${hasShoutout ? shoutOutTo : `${session.song.title} by ${session.song.artist}`}`,
                value: session.id,
              }))}
              value={watch('songRequestId')}
            />
          )}
        </>
      )}

      <ControlledInputWithoutContext
        name="description"
        label="Briefly explain what happened..."
        control={control}
        multiline
        numberOfLines={5}
        textAlignVertical="top"
        inputClassName="h-[100px]"
      />
    </BareLayout>
  );
}
