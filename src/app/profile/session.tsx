import dayjs from 'dayjs';
import { useRouter } from 'expo-router';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useGetRequestHistory } from '@/api/session';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import SessionHistoryMoreDropdown from '@/components/profile/session/more-dropdown';
import { ActionButtons } from '@/components/session/request/action-buttons';
import { RequestDetails } from '@/components/session/request/details';
import { RequestFooter } from '@/components/session/request/footer';
import { RequestIcon } from '@/components/session/request/icon';
import { EmptyState } from '@/components/ui';
import {
  ActivityIndicator,
  colors,
  FocusAwareStatusBar,
  H1,
  List,
  ScrollView,
  View,
} from '@/components/ui';
import { useLoggedInUser } from '@/lib';

export default function LiveSessionHistory() {
  const { isLoading, data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetRequestHistory({
      variables: { take: 10 },
    });
  const { data: user } = useLoggedInUser();
  const router = useRouter();

  const allSessionRequests = React.useMemo(() => {
    return data?.pages?.flatMap((sessionRequests) => sessionRequests) || [];
  }, [data?.pages]);

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  if (isLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back />
      </View>
      <H1 className="px-4 pb-4">Live session history</H1>
      <ScrollView
        className="bg-bg-canvas flex-1 dark:bg-grey-100"
        showsVerticalScrollIndicator={false}
      >
        <List
          data={allSessionRequests?.filter(
            (request) =>
              request.status === 'CANCELLED' ||
              request.status === 'REJECTED' ||
              request.status === 'PLAYED'
          )}
          estimatedItemSize={74}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={!isFetchingNextPage ? <EmptyState /> : null}
          renderItem={({ item }) => {
            const hasShoutout = !!item.shoutOutTo;
            return (
              <View className="mx-4 gap-4 rounded-md bg-grey-10 px-4 py-3 dark:bg-grey-90">
                <View className="flex-row items-center justify-between gap-3">
                  <View className="flex-1 flex-row gap-2">
                    <RequestIcon
                      isShoutout={!!hasShoutout}
                      iconSource={item.song.coverUrl}
                    />
                    <RequestDetails
                      title={hasShoutout ? 'Shout out' : item.song.title}
                      subtitle={
                        hasShoutout ? item.shoutOutTo || '' : item.song.artist
                      }
                    />
                  </View>

                  <View className="flex-row items-center gap-2">
                    <ActionButtons
                      id={item.songId || item.song.id}
                      status={item.status}
                      isShoutout={!!hasShoutout}
                      isBoosted={item.isBoosted}
                      isSessionCreator={item.djSessionId === user?.id}
                      isSessionHistory
                    />
                    <SessionHistoryMoreDropdown
                      contentClassName="bg-grey-10 dark:bg-grey-90"
                      itemClassName="native:px-4"
                      onValueChange={(value) => {
                        switch (value) {
                          case 'report':
                            router.push({
                              pathname: '/profile/report-problem',
                              params: {
                                songRequestId: item.id,
                                sessionName: item.djSession.title,
                                shoutOutTo: item.shoutOutTo,
                              },
                            });
                          default:
                            break;
                        }
                      }}
                      contentInsets={{
                        right: 16,
                      }}
                    />
                  </View>
                </View>

                <RequestFooter
                  timestamp={dayjs(item.updatedAt).format('h:mm A')}
                  isHistoryItem
                  sessionName={item.djSession.title}
                  djName={item.djSession.dj.username}
                />
              </View>
            );
          }}
          ItemSeparatorComponent={() => <View className="size-2" />}
          keyExtractor={(item) => String(item.id)}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0}
          ListFooterComponent={() =>
            isFetchingNextPage ? (
              <View className="items-center py-4">
                <ActivityIndicator color={colors.brand[60]} size="large" />
              </View>
            ) : null
          }
          contentContainerClassName="mt-8"
        />
      </ScrollView>
    </SafeAreaView>
  );
}
