import { useColorScheme } from 'nativewind';
import React from 'react';

import { IconComponent } from '@/components/common/icon-component';
import { BareLayout } from '@/components/layouts';
import {
  ChevronRight,
  MdRegularLabel,
  semanticColors,
  TouchableOpacity,
  View,
} from '@/components/ui';
import {
  IG_DEEP_LINK_URL,
  IG_SITE_URL,
  openLinkInBrowser,
  TIKTOK_DEEP_LINK_URL,
  TIKTOK_SITE_URL,
  X_DEEP_LINK_URL,
  X_SITE_URL,
  YOUTUBE_DEEP_LINK_URL,
  YOUTUBE_SITE_URL,
} from '@/lib';
import type { IconType } from '@/types';

const OPTIONS: {
  iconName: string;
  iconType: IconType;
  text: string;
  handleExternalLink: () => void;
  color?: string;
}[] = [
  {
    iconName: 'Instagram',
    iconType: 'custom-svg' as IconType,
    text: 'Follow us on Instagram',
    handleExternalLink: () => openLinkInBrowser(IG_DEEP_LINK_URL, IG_SITE_URL),
  },
  {
    iconName: 'X',
    iconType: 'custom-svg' as IconType,
    text: 'Follow us on X (Twitter)',
    handleExternalLink: () => openLinkInBrowser(X_DEEP_LINK_URL, X_SITE_URL),
  },
  {
    iconName: 'logo-tiktok',
    iconType: 'ionicons' as IconType,
    text: 'Follow us on Tiktok',
    handleExternalLink: () =>
      openLinkInBrowser(TIKTOK_DEEP_LINK_URL, TIKTOK_SITE_URL),
  },
  {
    iconName: 'youtube',
    iconType: 'entypo' as IconType,
    text: 'Subscribe to our YouTube Channel',
    handleExternalLink: () =>
      openLinkInBrowser(YOUTUBE_DEEP_LINK_URL, YOUTUBE_SITE_URL),
    color: '#FF0000',
  },
];

export default function SocialsScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  return (
    <BareLayout title="Follow on socials">
      <View>
        {OPTIONS.map((option, index) => (
          <TouchableOpacity
            key={index}
            className="h-16 flex-row items-center justify-between gap-4"
            onPress={option.handleExternalLink}
          >
            <View className="flex-row items-center gap-4">
              <IconComponent
                iconType={option.iconType}
                iconName={option.iconName}
                color={option.color || (isDark ? 'white' : '#070707')}
              />
              <MdRegularLabel>{option.text}</MdRegularLabel>
            </View>
            <ChevronRight
              color={
                isDark
                  ? semanticColors.fg.base.dark
                  : semanticColors.fg.base.light
              }
            />
          </TouchableOpacity>
        ))}
      </View>
    </BareLayout>
  );
}
