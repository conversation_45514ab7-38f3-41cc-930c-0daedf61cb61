import { MoneyTextInput } from '@alexzunik/react-native-money-input';
import React from 'react';
import { Keyboard } from 'react-native';
import { TouchableWithoutFeedback } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { Back } from '@/components/common/back';
import { Button, FocusAwareStatusBar, H1, Tiny, View } from '@/components/ui';
import { toAmountInMinor } from '@/lib';
import { useTopup } from '@/lib/hooks/use-topup';

export default function TopupWallet() {
  const [value, setValue] = React.useState<string>();
  const { topup, initializeTransaction, initializing } = useTopup({
    amount: Number(value || 0),
  });

  const handleTopup = () => {
    if (!value) return;
    if (Number(value) > 10000000) {
      toast.error('Maximum topup amount is ₦10,000,000');
      return;
    }
    initializeTransaction(
      {
        amount: toAmountInMinor(Number(value || 0)),
        country: 'NIGERIA',
      },
      {
        onSuccess: (data) => {
          topup({ reference: data.id });
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior="padding"
        keyboardVerticalOffset={10}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View className="bg-bg-canvas flex-1 dark:bg-grey-100">
            <View className="px-2 py-4">
              <Back />
            </View>
            <H1 className="px-4 pb-4">Top-up wallet</H1>
            <View className="flex-1 justify-center px-4">
              <View className="gap-14">
                <View className="h-[70px] items-center justify-between">
                  <MoneyTextInput
                    value={value}
                    onChangeText={(_formatted, extracted) => {
                      // console.log(formatted); // $1,234,567.89
                      // console.log(extracted); // 1234567.89
                      setValue(extracted);
                    }}
                    className="h-auto font-aeonik-black text-2xl/150 font-bold text-grey-100 dark:text-white"
                    placeholder="₦0"
                    placeholderClassName="text-grey-50 dark:text-grey-60 text-xl/150 font-aeonik-black font-bold"
                    prefix="₦"
                    groupingSeparator=","
                    fractionSeparator="."
                    autoFocus={true}
                    maxValue={10000000}
                  />
                  <Tiny className="text-grey-60 dark:text-grey-50">
                    Minimum: ₦1000
                  </Tiny>
                </View>
                <Button
                  testID="topup-button"
                  label="Continue"
                  disabled={!value || Number(value) < 1000 || initializing}
                  loading={initializing}
                  onPress={handleTopup}
                />
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
