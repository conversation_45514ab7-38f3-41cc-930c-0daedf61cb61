import { Feather, Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as Sharing from 'expo-sharing';
import { useColorScheme } from 'nativewind';
import { useRef } from 'react';
import ViewShot from 'react-native-view-shot';

import { useGetTransaction } from '@/api/transactions';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import {
  ArrowUpRight,
  Button,
  CoinIcon,
  colors,
  DownloadIcon,
  FocusAwareStatusBar,
  FundIcon,
  H4,
  LgBoldLabel,
  MdBoldLabel,
  MdRegularLabel,
  SafeAreaView,
  semanticColors,
  SmRegularLabel,
  SongIcon,
  TicketIcon,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { StatusPill } from '@/components/ui/status-pill';
import { formatAmount, formatEnumLabel, toAmountInMajor } from '@/lib';

export default function Transaction() {
  const ref = useRef<ViewShot>(null);
  const router = useRouter();

  const handleDownloadImage = async () => {
    const uri = await ref.current?.capture?.();
    if (uri) {
      await Sharing.shareAsync(uri);
    }
  };

  const { id: txId } = useLocalSearchParams<{ id: string }>();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { data: transaction, isLoading } = useGetTransaction({
    variables: { id: txId },
    enabled: !!txId,
  });

  if (isLoading) return <LoadingScreen />;
  if (!transaction) return null;

  const {
    songRequest,
    djSession,
    ticket,
    amount,
    currency,
    category,
    createdAt,
    type,
    status,
    id,
    meta,
  } = transaction;

  const isOnlineTransaction =
    category === 'FUND_WALLET' ||
    (category === 'EVENT_TICKET_PURCHASE' && meta?.paymentMethod === 'DIRECT');

  const IconComponent = () => {
    const iconColor = isDark ? colors.brand[40] : colors.brand[70];
    switch (category) {
      case 'FUND_WALLET':
        return <Ionicons name="wallet-outline" size={32} color={iconColor} />;
      case 'SONG_REQUEST':
        return (
          <Ionicons name="musical-notes-outline" size={32} color={iconColor} />
        );
      case 'WITHDRAW_WALLET':
        return <Feather name="arrow-up-right" size={32} color={iconColor} />;
      case 'EVENT_TICKET_PURCHASE':
      case 'EVENT_TICKET_EARNINGS':
        return <TicketIcon height={32} width={32} color={iconColor} />;
      case 'DJ_SESSION_EARNINGS':
        return <SongIcon height={32} width={32} color={iconColor} />;
      default:
        return <FundIcon height={32} width={32} color={iconColor} />;
    }
  };

  const items = {
    Type: formatEnumLabel(type || ''),
    Date: createdAt ? format(new Date(createdAt), 'd LLL, yyyy') : '',
    Time: createdAt ? format(new Date(createdAt), 'p') : '',
    Status: status,
  };

  const hasBreakdown = [
    'SONG_REQUEST',
    'DJ_SESSION_EARNINGS',
    'EVENT_TICKET_PURCHASE',
    'EVENT_TICKET_EARNINGS',
  ].includes(category);

  const majorAmount = toAmountInMajor(amount);
  const breakdownItems: { label: string; value?: string }[] = [];

  if (hasBreakdown) {
    switch (category) {
      case 'SONG_REQUEST': {
        const isBoosted = songRequest?.isBoosted;
        const shoutOutFee = djSession?.shoutoutCost ?? 0;

        const requestFee = isBoosted ? majorAmount / 2 : majorAmount;
        const boostFee = isBoosted ? majorAmount - requestFee : 0;

        breakdownItems.push(
          { label: 'Session', value: djSession?.title },
          { label: 'Creator', value: songRequest?.djSession?.dj.username },
          { label: 'Song', value: songRequest?.song?.title },
          {
            label: 'Request fee',
            value: formatAmount(requestFee, undefined, currency),
          }
        );

        if (isBoosted) {
          breakdownItems.push({
            label: 'Boost fee',
            value: formatAmount(boostFee, undefined, currency),
          });
        }

        if (songRequest?.shoutOutTo?.trim()) {
          breakdownItems.push({
            label: 'Shoutout fee',
            value: formatAmount(
              toAmountInMajor(shoutOutFee),
              undefined,
              currency
            ),
          });
        }

        break;
      }

      case 'DJ_SESSION_EARNINGS': {
        breakdownItems.push({
          label: 'Session',
          value: djSession?.title,
        });

        const {
          boostedSongRequests,
          shoutOutRequests,
          shoutoutCost,
          cost,
          playedSongRequestsWithShoutout,
        } = djSession || {};
        const sessionCostInMajor = toAmountInMajor(cost || 0);
        const shoutoutCostInMajor = toAmountInMajor(shoutoutCost || 0);
        if (playedSongRequestsWithShoutout) {
          breakdownItems.push({
            label: `Song Requests x${playedSongRequestsWithShoutout}`,
            value: formatAmount(
              sessionCostInMajor * playedSongRequestsWithShoutout,
              undefined,
              currency
            ),
          });
        }

        if (boostedSongRequests) {
          breakdownItems.push({
            label: `Boosted Requests x${boostedSongRequests}`,
            value: formatAmount(
              sessionCostInMajor * boostedSongRequests,
              undefined,
              currency
            ),
          });
        }

        if (shoutOutRequests) {
          breakdownItems.push({
            label: `Shoutouts x${shoutOutRequests}`,
            value: formatAmount(
              shoutoutCostInMajor * shoutOutRequests,
              undefined,
              currency
            ),
          });
        }

        break;
      }
      case 'EVENT_TICKET_PURCHASE': {
        breakdownItems.push({
          label: 'Event',
          value: ticket?.event?.title ?? 'N/A',
        });

        const breakdown = ticket?.meta?.breakdown || [];
        const aggregatedBreakdown = Array.from(
          breakdown.reduce((map, { type, quantity, cost }) => {
            if (!map.has(type)) {
              map.set(type, { type, quantity, cost });
            } else {
              const existing = map.get(type)!;
              existing.quantity += quantity;
              existing.cost += cost;
            }
            return map;
          }, new Map<string, { type: string; quantity: number; cost: number }>())
        ).map(([, value]) => value);

        aggregatedBreakdown?.forEach(({ type, quantity, cost }) => {
          const costInMajor = toAmountInMajor(cost);

          breakdownItems.push({
            label: `${type} × ${quantity}`,
            value: formatAmount(costInMajor * quantity, undefined, currency),
          });
        });

        breakdownItems.push({
          label: 'Service fee',
          value: formatAmount(
            toAmountInMajor(ticket?.serviceFee ?? 0),
            undefined,
            currency
          ),
        });

        if (ticket?.discount) {
          breakdownItems.push({
            label: 'Discount',
            value: `- ${formatAmount(
              toAmountInMajor(ticket?.discount.amount ?? 0),
              undefined,
              currency
            )}`,
          });
        }

        break;
      }

      case 'EVENT_TICKET_EARNINGS': {
        breakdownItems.push({
          label: 'Event',
          value: meta.eventTitle ?? 'N/A',
        });

        break;
      }
    }
  }

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="h-16 flex-row items-center justify-between px-2">
        <Back />
        <LgBoldLabel>Wallet</LgBoldLabel>
        <TouchableOpacity
          className="size-8 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
          onPress={handleDownloadImage}
        >
          <DownloadIcon
            color={
              isDark
                ? semanticColors.accent.bold.dark
                : semanticColors.accent.bold.light
            }
          />
        </TouchableOpacity>
      </View>

      <View className="flex-1 gap-y-4 p-4">
        <ViewShot
          ref={ref}
          options={{ format: 'png', quality: 1 }}
          style={{
            backgroundColor: isDark
              ? colors.grey[100]
              : semanticColors.bg.canvas.light,
            rowGap: 16,
            flex: 1,
          }}
        >
          <View className="h-20 flex-row items-center gap-4 px-2">
            <View className="size-16 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark">
              <IconComponent />
            </View>

            <View className="flex-1 flex-row items-center justify-between">
              <View className="gap-2">
                <H4 className="text-fg-on-contrast-dark dark:text-fg-on-contrast-light">
                  {formatEnumLabel(category || '')}
                </H4>
                {amount !== undefined && (
                  <View className="flex-row items-center gap-0.5">
                    <CoinIcon color={isDark ? colors.white : colors.black} />
                    <MdRegularLabel className="text-fg-on-contrast-dark dark:text-fg-on-contrast-light">
                      {formatAmount(majorAmount, undefined, currency)}
                    </MdRegularLabel>
                  </View>
                )}
              </View>

              {category !== 'WITHDRAW_WALLET' && (
                <View className="h-8 flex-row items-center gap-2 rounded-full bg-accent-subtle-light pl-3 pr-4 dark:bg-accent-subtle-dark">
                  <ArrowUpRight />
                  <MdBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                    {isOnlineTransaction ? 'Online' : 'Wallet'}
                  </MdBoldLabel>
                </View>
              )}
            </View>
          </View>

          <View className="flex-row gap-2.5 px-2">
            <MdRegularLabel className="text-fg-base-light dark:text-fg-base-dark">
              Transaction ID:
            </MdRegularLabel>
            <SmRegularLabel
              className="text-fg-muted-light dark:text-fg-muted-dark"
              numberOfLines={1}
            >
              {id}
            </SmRegularLabel>
          </View>

          <View className="gap-4 p-4">
            {Object.entries(items).map(([key, value]) => (
              <View
                key={key}
                className="flex-row items-center justify-between gap-2"
              >
                <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                  {key}
                </SmRegularLabel>
                {key === 'Status' ? (
                  <StatusPill status={value as any} />
                ) : (
                  <MdRegularLabel
                    className="self-end text-fg-base-light dark:text-fg-base-dark"
                    numberOfLines={1}
                  >
                    {value}
                  </MdRegularLabel>
                )}
              </View>
            ))}
          </View>

          {hasBreakdown && (
            <View className="px-2">
              <MdRegularLabel>Breakdown</MdRegularLabel>
              <View className="gap-4 px-2 py-4">
                {breakdownItems.map(({ label, value }, index) => (
                  <View key={index} className="flex-row items-center gap-2">
                    <SmRegularLabel
                      className="flex-1 text-left text-fg-muted-light dark:text-fg-muted-dark"
                      numberOfLines={1}
                    >
                      {String(label)}
                    </SmRegularLabel>

                    <MdRegularLabel
                      className="flex-1 text-right"
                      numberOfLines={1}
                    >
                      {String(value ?? '')}
                    </MdRegularLabel>
                  </View>
                ))}
              </View>
            </View>
          )}
        </ViewShot>

        <View className="mt-auto items-center justify-center pt-4">
          <Button
            label="Contact support"
            variant="ghost"
            textClassName="!text-[#7257FF]"
            onPress={() => router.push(`/profile/report-problem?trxId=${txId}`)}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
