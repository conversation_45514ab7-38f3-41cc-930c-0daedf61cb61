import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import { useGetTransactionHistory } from '@/api/user';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import { TransactionHistoryItem } from '@/components/profile/wallet';
import { EmptyState } from '@/components/ui';
import {
  ActivityIndicator,
  ArrowUpRight,
  BankIcon,
  Button,
  colors,
  FocusAwareStatusBar,
  H1,
  H2,
  Image,
  LgBoldLabel,
  List,
  MdBoldLabel,
  MdRegularLabel,
  Modal,
  PasswordIcon,
  semanticColors,
  TouchableOpacity,
  useModal,
  View,
  XsRegularLabel,
} from '@/components/ui';
import {
  formatAmount,
  maskLast,
  toAmountInMajor,
  useLoggedInUser,
  useVisibility,
} from '@/lib';

export default function Wallet() {
  const router = useRouter();
  const { bottom } = useSafeAreaInsets();
  const { toggleVisibility, isOpen } = useVisibility();
  const bankModal = useModal();

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { isLoading: userLoading, data: currentUser } = useLoggedInUser();
  const isCreator = currentUser?.role !== 'QUESTER';
  const bankDetails = currentUser?.idVerificationData?.bank;

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetTransactionHistory({
      variables: {
        id: currentUser?.id || '',
        currency: 'NGN',
        take: 10,
      },
      enabled: !!currentUser?.id,
    });

  const allTransactions = React.useMemo(() => {
    return data?.pages?.flatMap((page) => page.transactions) || [];
  }, [data?.pages]);

  // Handle load more when reaching the end
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  if (isLoading || userLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="flex-1">
        {/* Header */}
        <View className="h-16 flex-row items-center justify-between px-2">
          <Back />
          <LgBoldLabel>Wallet</LgBoldLabel>
          {isCreator ? (
            <TouchableOpacity className="size-8" onPress={bankModal.present}>
              <BankIcon />
            </TouchableOpacity>
          ) : (
            <View className="size-8" />
          )}
        </View>
        {/* Content */}
        <View className="flex-1 gap-3 px-4">
          {/* WalletCard */}
          <View className="relative h-44">
            <Image
              className="size-full rounded-lg"
              source={
                isDark
                  ? require('~/assets/gradient-bg/wallet-gradient.png')
                  : require('~/assets/gradient-bg/wallet-gradient-light.png')
              }
              contentFit="cover"
            />
            <View className="absolute size-full justify-between p-4">
              <View className="gap-1">
                <XsRegularLabel className="text-white dark:text-white">
                  Balance
                </XsRegularLabel>
                <View className="flex-row items-center gap-4">
                  <H1 className="text-white dark:text-white">
                    {!isOpen
                      ? formatAmount(
                          toAmountInMajor(currentUser?.walletBalance || 0)
                        )
                      : '*****'}
                  </H1>
                  <TouchableOpacity onPress={toggleVisibility}>
                    <Ionicons
                      name={!isOpen ? 'eye-outline' : 'eye-off-outline'}
                      size={24}
                      color={colors.white}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              <View className="flex-row gap-3">
                <WalletButton type="topup" />
                {isCreator && <WalletButton type="withdraw" />}
              </View>
            </View>
          </View>

          {isCreator && (
            <TouchableOpacity
              className="h-[53px] flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 dark:bg-bg-subtle-dark"
              onPress={() => {
                router.push({ pathname: '/profile/pin' });
              }}
            >
              <PasswordIcon color={colors.brand[60]} />
              <MdRegularLabel className="flex-1">
                Transaction pin
              </MdRegularLabel>
            </TouchableOpacity>
          )}

          {/* Tx History */}
          <List
            data={allTransactions}
            estimatedItemSize={74}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={!isFetchingNextPage ? <EmptyState /> : null}
            renderItem={({ item }) => <TransactionHistoryItem item={item} />}
            ItemSeparatorComponent={() => (
              <View className="border border-grey-20 dark:border-grey-80" />
            )}
            keyExtractor={(item) => String(item.id)}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0}
            ListFooterComponent={() =>
              isFetchingNextPage ? (
                <View className="items-center py-4">
                  <ActivityIndicator color={colors.brand[60]} size="large" />
                </View>
              ) : null
            }
          />
        </View>
      </View>
      <Modal
        ref={bankModal.ref}
        index={0}
        enableDynamicSizing
        hasHandle={false}
      >
        <BottomSheetView
          style={{
            paddingBottom: bottom + 16,
          }}
          className="min-h-[15i0px] gap-4 px-4 pt-4"
        >
          <H2>Account Details</H2>
          {bankDetails && (
            <View className="flex-row items-center gap-3 rounded-md border border-border-subtle-light px-4 py-3 dark:border-border-subtle-dark">
              <View className="size-8 items-center justify-center bg-accent-subtle-light dark:bg-accent-subtle-dark">
                <BankIcon color={colors.white} />
              </View>
              <MdRegularLabel className="flex-1">
                {bankDetails.bankName}{' '}
                {maskLast(bankDetails.accountNumber, 4, '*')}
              </MdRegularLabel>
            </View>
          )}
          <Button
            label={bankDetails ? 'Update bank details' : 'Add new bank'}
            variant="secondary"
            onPress={() => {
              bankModal.dismiss();
              router.push({ pathname: '/profile/withdraw/bank' });
            }}
          />
        </BottomSheetView>
      </Modal>
    </SafeAreaView>
  );
}

export const WalletButton: React.FC<{ type: 'topup' | 'withdraw' }> = ({
  type,
}) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isTopup = type === 'topup';
  return (
    <TouchableOpacity
      className="h-8 flex-row items-center justify-center gap-2 rounded-full bg-brand-20 px-4 pl-3 dark:bg-brand-90"
      onPress={() =>
        isTopup
          ? router.navigate('/profile/top-up')
          : router.navigate('/profile/withdraw')
      }
    >
      {isTopup ? (
        <Ionicons
          name="add"
          size={16}
          color={isDark ? colors.brand[40] : colors.brand[70]}
        />
      ) : (
        <ArrowUpRight
          color={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
        />
      )}

      <MdBoldLabel className="text-brand-70 dark:text-brand-40">
        {isTopup ? 'Top up' : 'Withdraw'}
      </MdBoldLabel>
    </TouchableOpacity>
  );
};
