import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { useSavePayoutDetails } from '@/api/transactions';
import { useValidatePin } from '@/api/user';
import { BankForm } from '@/components/forms/bank-form';
import { AuthLayout } from '@/components/layouts/auth-layout';
import {
  Button,
  ChevronLeft,
  colors,
  H2,
  H3,
  Modal,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import { cn, useBankSetupForm, useLoggedInUser } from '@/lib';

export default function Bank() {
  const router = useRouter();
  const { bottom } = useSafeAreaInsets();
  const queryClient = useQueryClient();
  const { data: user } = useLoggedInUser();
  const hasPinCreated = !!user?.pin;
  const [pin, setPin] = React.useState('');
  const { mutate: validatePin } = useValidatePin();
  const onConfirmPin = (pin: string) => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid) => {
          if (isValid) {
            handleAddBankDetails();
          } else {
            toast.error('Invalid Pin');
          }
          setPin('');
        },
        onError: (error) => {
          toast.error(error.message);
          setPin('');
        },
      }
    );
  };

  const { formMethods, hasExistingBankDetails } = useBankSetupForm();
  const acctName = formMethods.watch('accountName');

  const { mutate: addBankDetails, isPending } = useSavePayoutDetails();

  const handleAddBankDetails = () => {
    const isAcctNoFieldDirty =
      formMethods.getFieldState('accountNumber').isDirty;

    if (isAcctNoFieldDirty) {
      const payload = formMethods.watch();
      addBankDetails(payload, {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          toast.success('Bank details added successfully');
          router.back();
        },
        onError: (error) => toast.error(error.message),
      });
    } else {
      router.back();
    }
  };

  const modal = useModal();

  const handleKeyPress = (key: string) => {
    if (key === 'delete') {
      setPin((prev) => prev.slice(0, -1));
    } else if (pin.length < 4) {
      const newPin = pin + key;
      setPin(newPin);
      if (newPin.length === 4) {
        modal.dismiss();
        onConfirmPin(newPin);
      }
    }
  };

  const renderKey = (value: string) => {
    const isDelete = value === 'delete';
    const isDisabled = isDelete && pin.length === 0;

    return (
      <TouchableOpacity
        key={value}
        onPress={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'items-center justify-center',
          isDisabled && 'opacity-50'
        )}
        activeOpacity={0.7}
        disabled={isDisabled}
        hitSlop={20}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3 className="text-accent-moderate dark:text-accent-moderate">
            {value}
          </H3>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <AuthLayout
      title={`${hasExistingBankDetails ? 'Update' : 'Add'} Bank Details`}
      subTitle={`${hasExistingBankDetails ? 'Modify' : 'Enter'} your bank account details`}
      footer={
        <Button
          testID="bankName-button"
          label="Continue"
          className="my-4"
          loading={isPending}
          disabled={!formMethods.formState.isValid || !acctName || isPending}
          onPress={() => {
            if (hasPinCreated) {
              modal.present();
            } else {
              toast.info('You need to set up a PIN to continue.');
              router.push('/profile/pin');
            }
          }}
        />
      }
    >
      <FormProvider {...formMethods}>
        <BankForm />
      </FormProvider>
      <Modal ref={modal.ref} index={0} enableDynamicSizing>
        <BottomSheetView
          style={{
            paddingBottom: bottom + 24,
          }}
          className="min-h-[478px] items-center gap-6 px-4 pt-6"
        >
          <H2>Enter your transaction PIN</H2>

          <View className="flex-row gap-2">
            {[0, 1, 2, 3].map((index) => {
              const isFilled = index < pin.length;
              return (
                <View
                  key={index}
                  className={cn(
                    'size-14 items-center justify-center rounded-full border-2',
                    isFilled
                      ? 'border-accent-moderate dark:border-accent-moderate'
                      : 'border-grey-20 dark:border-grey-80'
                  )}
                >
                  {isFilled && (
                    <View className="size-1 rounded-full bg-black dark:bg-white" />
                  )}
                </View>
              );
            })}
          </View>
          <View className="w-full gap-16">
            <View className="flex-row justify-around">
              {['1', '2', '3'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              {['4', '5', '6'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              {['7', '8', '9'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              <View className="size-7" />
              {renderKey('0')}
              {renderKey('delete')}
            </View>
          </View>
        </BottomSheetView>
      </Modal>
    </AuthLayout>
  );
}
