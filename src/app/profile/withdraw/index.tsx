import { MoneyTextInput } from '@alexzunik/react-native-money-input';
import { useRouter } from 'expo-router';
import React from 'react';
import { Keyboard, TouchableOpacity } from 'react-native';
import { TouchableWithoutFeedback } from 'react-native';

import { BareLayout } from '@/components/layouts';
import {
  Button,
  Image,
  MdRegularLabel,
  Tiny,
  View,
  XsBoldLabel,
  XsRegularLabel,
} from '@/components/ui';
import {
  formatAmount,
  formatNumber,
  maskLast,
  useLoggedInUser,
  useWithdrawalSummary,
} from '@/lib';

export default function Withdraw() {
  const router = useRouter();
  const { data: user } = useLoggedInUser();
  const bankDetails = user?.idVerificationData?.bank;
  const [value, setValue] = React.useState<string>();

  const { summaryItems, total } = useWithdrawalSummary({
    amount: Number(value || 0),
    feeRate: 0.01,
  });
  return (
    <BareLayout title="Withdraw" contentClassName="gap-0">
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="flex-1">
          <View className="flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 py-2 dark:bg-bg-subtle-dark">
            <View className="flex-row gap-1">
              <Image
                source={{
                  uri: `https://flagcdn.com/128x96/${
                    user?.wallet.countryCode || 'ng'
                  }.png`,
                }}
                className="size-6 rounded-xl"
              />
              <MdRegularLabel>NGN</MdRegularLabel>
            </View>
            <View className="gap-1">
              <XsRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                Wallet balance
              </XsRegularLabel>
              <MdRegularLabel>
                {formatNumber(user?.walletBalance)}
              </MdRegularLabel>
            </View>
          </View>
          <View className="gap-6">
            <View className="mt-10 h-[70px] items-center justify-between">
              <MoneyTextInput
                value={value}
                onChangeText={(_formatted, extracted) => {
                  // console.log(formatted); // $1,234,567.89
                  // console.log(extracted); // 1234567.89
                  setValue(extracted);
                }}
                className="h-auto flex-1 text-center font-aeonik-black text-2xl/120 font-bold text-grey-100 dark:text-white"
                placeholder="₦0"
                placeholderClassName="text-grey-50 dark:text-grey-60 text-xl/120 font-aeonik-black font-bold"
                prefix="₦"
                groupingSeparator=","
                fractionSeparator="."
                autoFocus={true}
                maxValue={10000000}
              />
              <Tiny className="text-grey-60 dark:text-grey-50">
                Minimum withdrawal: N10,000.00
              </Tiny>
            </View>
            <View className="gap-2">
              {summaryItems.map(({ label, value, key }) => (
                <View
                  className="flex-row items-center justify-between gap-2.5"
                  key={key}
                >
                  <XsRegularLabel className="flex-1 text-fg-muted-light dark:text-fg-muted-dark">
                    {label}
                  </XsRegularLabel>
                  <XsRegularLabel>{formatAmount(value)}</XsRegularLabel>
                </View>
              ))}
            </View>
            {bankDetails && (
              <View className="flex-row justify-between gap-4 rounded-md border border-border-muted-light px-4 py-2 dark:border-border-muted-dark">
                <View className="gap-1">
                  <XsRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                    Withdrawing to
                  </XsRegularLabel>
                  <MdRegularLabel>
                    {bankDetails.bankName}{' '}
                    {maskLast(bankDetails.accountNumber, 4, '*')}
                  </MdRegularLabel>
                </View>
                <TouchableOpacity
                  className="justify-center px-4 py-2"
                  onPress={() =>
                    router.push({ pathname: '/profile/withdraw/bank' })
                  }
                >
                  <XsBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                    Change
                  </XsBoldLabel>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <Button
            className="mt-[18px]"
            testID="withdraw-button"
            label="Withdraw"
            disabled={!value || Number(total) < 10000}
            // loading={initializing}
            // onPress={handleTopup}
            onPress={() =>
              router.push({
                pathname: '/profile/withdraw/review',
                params: { amount: value },
              })
            }
          />
        </View>
      </TouchableWithoutFeedback>
    </BareLayout>
  );
}
