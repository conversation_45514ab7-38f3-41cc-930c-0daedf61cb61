import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { useWithdrawalRequest } from '@/api/transactions';
import { useValidatePin } from '@/api/user';
import { BareLayout } from '@/components/layouts';
import {
  Button,
  ChevronLeft,
  colors,
  H2,
  H3,
  Modal,
  semanticColors,
  SmRegularLabel,
  TouchableOpacity,
  useModal,
  View,
  WarningTriangle,
} from '@/components/ui';
import {
  cn,
  formatAmount,
  toAmountInMinor,
  useLoggedInUser,
  useWithdrawalSummary,
} from '@/lib';

export default function ReviewWithdrawal() {
  const { amount } = useLocalSearchParams<{ amount: string }>();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { bottom } = useSafeAreaInsets();
  const modal = useModal();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { data: user } = useLoggedInUser();
  const bankDetails = user?.idVerificationData?.bank;
  const hasPinCreated = !!user?.pin;

  const { summaryItems, withdrawalAmount, total } = useWithdrawalSummary({
    amount: Number(amount || 0),
    feeRate: 0.01,
  });

  const [pin, setPin] = React.useState('');
  const { mutate: validatePin, isPending: validatingPin } = useValidatePin();

  const onConfirmPin = (pin: string) => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid) => {
          if (isValid) {
            handleWithdraw();
          } else {
            toast.error('Invalid Pin');
          }
          setPin('');
        },
        onError: (error) => {
          toast.error(error.message);
          setPin('');
        },
      }
    );
  };

  const { mutate: submitWithdrawaRequest, isPending: isSubmittingRequest } =
    useWithdrawalRequest();

  const handleWithdraw = () => {
    if (Number(total) > ********) {
      toast.error('Maximum withdrawal amount is ₦10,000,000');
      return;
    }
    submitWithdrawaRequest(
      { amount: toAmountInMinor(total) },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          queryClient.invalidateQueries({
            queryKey: ['getTransactionHistory'],
          });
          toast.success('Transaction Successful');
          router.dismissTo({ pathname: '/profile/wallet' });
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  const handleKeyPress = (key: string) => {
    if (key === 'delete') {
      setPin((prev) => prev.slice(0, -1));
    } else if (pin.length < 4) {
      const newPin = pin + key;
      setPin(newPin);
      if (newPin.length === 4) {
        modal.dismiss();
        onConfirmPin(newPin);
      }
    }
  };

  const renderKey = (value: string) => {
    const isDelete = value === 'delete';
    const isDisabled = isDelete && pin.length === 0;

    return (
      <TouchableOpacity
        key={value}
        onPress={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'items-center justify-center',
          isDisabled && 'opacity-50'
        )}
        activeOpacity={0.7}
        disabled={isDisabled}
        hitSlop={20}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3 className="text-accent-moderate dark:text-accent-moderate">
            {value}
          </H3>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <BareLayout
      title="Review transfer details"
      footer={
        <Button
          testID="bankName-button"
          label="Withdraw"
          className="m-4"
          loading={validatingPin || isSubmittingRequest}
          disabled={validatingPin || isSubmittingRequest}
          onPress={() => {
            if (hasPinCreated) {
              modal.present();
            } else {
              toast.info('You need to set up a PIN to continue.');
              router.push('/profile/pin');
            }
          }}
        />
      }
    >
      <Card>
        <View className="size-10 items-center justify-center rounded-full bg-bg-warning-light dark:bg-bg-warning-dark">
          <WarningTriangle
            color={
              isDark
                ? semanticColors.fg.warning.dark
                : semanticColors.fg.warning.light
            }
          />
        </View>
        <SmRegularLabel>
          You are about to withdraw {formatAmount(withdrawalAmount)}
        </SmRegularLabel>
        <SmRegularLabel className="text-fg-warning-light dark:text-fg-warning-dark">
          Withdrawals may take up to 48hrs to reflect in your bank account.
        </SmRegularLabel>
      </Card>
      {bankDetails && (
        <Card>
          <View className="flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Recipient
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.fullName}</SmRegularLabel>
          </View>
          <View className="flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Account number
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.accountNumber}</SmRegularLabel>
          </View>
          <View className="flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Bank
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.bankName}</SmRegularLabel>
          </View>
        </Card>
      )}
      <Card>
        {summaryItems.map(({ key, value }) => (
          <View key={key} className="flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              {key === 'amount'
                ? 'You get'
                : key === 'fee'
                  ? 'Transaction fees'
                  : 'Total'}
            </SmRegularLabel>
            <SmRegularLabel>{formatAmount(value)}</SmRegularLabel>
          </View>
        ))}
      </Card>

      <Modal ref={modal.ref} index={0} enableDynamicSizing>
        <BottomSheetView
          style={{
            paddingBottom: bottom + 24,
          }}
          className="min-h-[478px] items-center gap-6 px-4 pt-6"
        >
          <H2>Enter your transaction PIN</H2>

          <View className="flex-row gap-2">
            {[0, 1, 2, 3].map((index) => {
              const isFilled = index < pin.length;
              return (
                <View
                  key={index}
                  className={cn(
                    'size-14 items-center justify-center rounded-full border-2',
                    isFilled
                      ? 'border-accent-moderate dark:border-accent-moderate'
                      : 'border-grey-20 dark:border-grey-80'
                  )}
                >
                  {isFilled && (
                    <View className="size-1 rounded-full bg-black dark:bg-white" />
                  )}
                </View>
              );
            })}
          </View>
          <View className="w-full gap-16">
            <View className="flex-row justify-around">
              {['1', '2', '3'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              {['4', '5', '6'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              {['7', '8', '9'].map(renderKey)}
            </View>
            <View className="flex-row justify-around">
              <View className="size-7" />
              {renderKey('0')}
              {renderKey('delete')}
            </View>
          </View>
        </BottomSheetView>
      </Modal>
    </BareLayout>
  );
}

const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <View
    className={cn(
      'rounded-lg bg-bg-subtle-light dark:bg-bg-subtle-dark p-4 gap-2',
      className
    )}
  >
    {children}
  </View>
);
