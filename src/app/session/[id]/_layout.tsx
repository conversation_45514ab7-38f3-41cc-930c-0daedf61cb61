import { Stack } from 'expo-router';

export default function ViewLiveSessionLayout() {
  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="request" options={{ headerShown: false }} />
      <Stack.Screen name="shoutout" options={{ headerShown: false }} />
      <Stack.Screen
        name="recording"
        options={{
          headerShown: false,
          presentation: 'containedModal',
          gestureEnabled: false,
        }}
      />
      <Stack.Screen name="viewers" options={{ headerShown: false }} />
    </Stack>
  );
}
