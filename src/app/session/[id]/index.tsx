import {
  BottomSheetScrollView,
  useBottomSheetModal,
} from '@gorhom/bottom-sheet';
import { type FlashList } from '@shopify/flash-list';
import { useFocusEffect, useLocalSearchParams } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useCallback } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useGetAgoraToken } from '@/api/session';
import ConfirmationDialog from '@/components/dialogs/confirmation';
import { LiveSessionLayout } from '@/components/layouts';
import LoadingScreen from '@/components/loading';
import { GiftModal } from '@/components/modals/gift';
import { SessionTabsComponent } from '@/components/session';
import { SideActions } from '@/components/session/actions';
import { PinnedMessage } from '@/components/session/chat/pinned';
import { ChatSection } from '@/components/session/chat/section';
import { SessionFooter } from '@/components/session/footer';
import { SessionHeader } from '@/components/session/header';
import { VideoStreamView } from '@/components/session/stream/video-view';
import {
  Modal,
  semanticColors,
  useModal,
  View as UIView,
} from '@/components/ui';
import {
  cn,
  useAgoraVideo,
  useKeyboardVisible,
  useLiveSession,
  useLiveSessionActions,
  useLoggedInUser,
} from '@/lib';
import { useSessionChat } from '@/lib/hooks/use-session-chat';

export default function LiveSession() {
  const { id: sessionId } = useLocalSearchParams<{ id: string }>();
  const insets = useSafeAreaInsets();
  const isKeyboardVisible = useKeyboardVisible();
  const giftModal = useModal();
  const requestModal = useModal();
  const snapPoints = React.useMemo(() => ['50%', '70%', '85%'], []);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { isLoading: userLoading, data: authUser } = useLoggedInUser();
  const { isLoading: isLiveSessionLoading, sessionData } =
    useLiveSession(sessionId);

  const scrollViewRef = React.useRef<FlashList<any>>(null);

  const {
    isSessionCreator: isSessionOwner,
    messages,
    pinnedMessage,
    sendMessage,
    message,
    setMessage,
    showCreatorActionSheet,
  } = useSessionChat({
    scrollViewRef,
    sessionId,
  });

  const { data: agoraTokenData } = useGetAgoraToken({
    variables: {
      channelName: sessionId,
      uid: isSessionOwner ? 1234 : 0,
      role: isSessionOwner ? 0 : 1,
    },
    enabled: !!sessionId,
  });
  const {
    joinState,
    isMuted,
    isCameraHidden,
    toggleMute,
    switchCamera,
    toggleCamera,
    leaveChannel,
    retryJoin,
  } = useAgoraVideo({
    channelName: sessionId,
    token: agoraTokenData?.token || '',
    isSessionOwner,
    enableVideo: sessionData?.djSession.enableVideo,
    uid: isSessionOwner ? 1234 : 0,
  });

  const {
    isEnding,
    isLeaving,
    confirmEndVisible,
    setConfirmEndVisible,
    onEndLiveSession,
    onLeaveLiveSession,
  } = useLiveSessionActions(sessionId);

  const { dismissAll: dismissAllModals } = useBottomSheetModal();

  useFocusEffect(
    useCallback(() => {
      return () => {
        dismissAllModals();
      };
    }, [dismissAllModals])
  );
  const handleMessageFade = (_id: number | string) => {};

  if (
    isEnding ||
    isLeaving ||
    userLoading ||
    isLiveSessionLoading ||
    !sessionData
  )
    return <LoadingScreen />;

  const sessionInfo = sessionData.djSession;
  const questersOnSession = sessionData.questersOnSession;

  return (
    <LiveSessionLayout>
      <UIView className="relative flex-1 items-center justify-center">
        <VideoStreamView
          joinState={joinState}
          isOwner={isSessionOwner}
          enableVideo={!!sessionInfo.enableVideo}
          onRetry={retryJoin}
          isCameraHidden={
            isSessionOwner
              ? isCameraHidden || !!sessionInfo.cameraHidden
              : !!sessionInfo.cameraHidden
          }
        />

        <UIView
          className="absolute inset-0 justify-between"
          style={{
            marginTop: insets.top,
          }}
        >
          {/* Session Header */}
          <SessionHeader
            isSessionOwner={isSessionOwner}
            onEndSession={() => setConfirmEndVisible(true)}
            onExitSession={() => onLeaveLiveSession(leaveChannel)}
            dj={sessionInfo.dj}
            viewersCount={questersOnSession}
            walletBalance={authUser?.walletBalance || authUser?.wallet.balance}
            isMuted={isMuted}
            onMutePress={toggleMute}
            onSwitchCamera={switchCamera}
            showCamera={toggleCamera}
            hideCamera={toggleCamera}
            isCameraHidden={isCameraHidden}
            isSessionVideoEnabled={!!sessionInfo.enableVideo}
          />

          {/* Gift Button */}
          {/* {isSessionOwner && (
              <UIView className="mb-11 mr-8 mt-auto items-end">
                <ActionButton
                  imagePath={require('~/assets/images/session/gift.png')}
                  label="Gift"
                  onPress={giftModal.present}
                />
              </UIView>
            )} */}

          <UIView className="flex-1" />

          {/* Session chat + actions */}
          <UIView
            className={cn(
              'mb-1 ml-4 mr-6 flex-row items-end justify-between overflow-hidden h-[350px]',
              isKeyboardVisible && 'h-[250px]'
            )}
          >
            <ChatSection
              messages={[...messages].reverse()}
              onMessageFade={(id) => handleMessageFade(id)}
              handleChatLongPress={(message) => {
                if (isSessionOwner) {
                  showCreatorActionSheet(message);
                }
              }}
              scrollViewRef={scrollViewRef}
            />
            {!isSessionOwner && <SideActions />}
          </UIView>
        </UIView>
      </UIView>

      {/* Pinned message */}
      {pinnedMessage && (
        <PinnedMessage
          avatar={pinnedMessage.image}
          message={pinnedMessage.text}
          username={pinnedMessage.username}
          isSessionOwner={pinnedMessage.username === sessionInfo.dj.username}
          handleChatLongPress={() => {
            if (isSessionOwner) {
              showCreatorActionSheet(pinnedMessage, true);
            }
          }}
        />
      )}

      {/* Chat input */}
      <SessionFooter
        sessionId={sessionId}
        message={message}
        handleSend={sendMessage}
        isSessionOwner={isSessionOwner}
        setNewMessage={setMessage}
        handleRequestOpen={requestModal.present}
      />
      <Modal ref={giftModal.ref} index={0} enableDynamicSizing hasHandle>
        <GiftModal onGiftSelect={giftModal.dismiss} />
      </Modal>
      <Modal
        ref={requestModal.ref}
        index={1}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: isDark
            ? semanticColors.bg.surface.dark
            : semanticColors.bg.surface.light,
        }}
        handleClassName="bg-bg-interactive-secondary-light dark:bg-bg-interactive-secondary-dark"
        enablePanDownToClose={false}
        backdropComponent={() => <UIView />}
        hasCloseButton
      >
        <BottomSheetScrollView className="flex-1">
          <SessionTabsComponent className="pt-6" sessionId={sessionId} />
        </BottomSheetScrollView>
      </Modal>
      <ConfirmationDialog
        visible={confirmEndVisible}
        message="Are you sure you want to end this live session?"
        onCancel={() => setConfirmEndVisible(false)}
        onConfirm={() => onEndLiveSession(leaveChannel)}
      />
    </LiveSessionLayout>
  );
}
