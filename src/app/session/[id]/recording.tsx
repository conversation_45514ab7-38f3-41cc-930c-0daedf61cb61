import { useRouter } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';

import { BlurredBackground } from '@/components/background/blur';
import SongRecordingModal from '@/components/modals/song-recording';
import { colors, FocusAwareStatusBar, View } from '@/components/ui';

export default function SongRecording() {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const { songId, id: sessionId } = useLocalSearchParams<{
    songId: string;
    id: string;
  }>();
  const [modalVisible, setModalVisible] = React.useState(true);

  const handleCloseModal = () => {
    setModalVisible(false);
    router.replace({
      pathname: '/session/[id]/request',
      params: { id: sessionId },
    });
  };

  return (
    <View className="relative flex-1 bg-bg-overlay-light dark:bg-bg-overlay-dark">
      <FocusAwareStatusBar hidden />
      <BlurredBackground
        isDark={isDark}
        blurStyle={styles.blurView}
        overlayStyle={styles.overlay}
      />
      <SongRecordingModal
        visible={modalVisible}
        onClose={handleCloseModal}
        songId={songId}
        sessionId={sessionId}
      />
    </View>
  );
}

export const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
