import { useLocalSearchParams } from 'expo-router';
import React from 'react';

import { SessionTabsComponent } from '@/components/session';
import { FocusAwareStatusBar, SafeAreaView } from '@/components/ui';

export default function LiveRequest() {
  const { id: sessionId } = useLocalSearchParams<{ id: string }>();

  return (
    <SafeAreaView className="flex-1 bg-white dark:bg-grey-100">
      <FocusAwareStatusBar />
      <SessionTabsComponent hasHeader sessionId={sessionId} />
    </SafeAreaView>
  );
}
