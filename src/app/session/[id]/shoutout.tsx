import { Ionicons } from '@expo/vector-icons';
import * as Crypto from 'expo-crypto';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { Keyboard, TextInput, TouchableWithoutFeedback } from 'react-native';
import { toast } from 'sonner-native';

import { type SongRequestPayload, useSendSongRequest } from '@/api/session';
import { Back } from '@/components/common/back';
import { SearchSongModalContent } from '@/components/modals/search-song';
import { RequestDetails } from '@/components/session/request/details';
import { RequestIcon } from '@/components/session/request/icon';
import {
  Button,
  colors,
  FocusAwareStatusBar,
  H1,
  MdBoldLabel,
  Modal,
  P,
  SafeAreaView,
  SmBoldLabel,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import {
  cn,
  formatAmount,
  toAmountInMajor,
  useGetLiveSessionQuery,
  useInitializeSpotify,
  useLoggedInUser,
} from '@/lib';
import { useBottomSheetModal } from '@gorhom/bottom-sheet';

export default function RequestShoutout() {
  useInitializeSpotify();

  const { id: sessionId } = useLocalSearchParams<{ id: string }>();
  const { replace, push } = useRouter();
  const { dismissAll: dismissAllModals } = useBottomSheetModal();
  const modal = useModal();
  const { data: user } = useLoggedInUser();

  const { data: sessionData } = useGetLiveSessionQuery(sessionId);
  const sessionInfo = sessionData?.djSession;

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const snapPoints = React.useMemo(() => ['60%', '80%'], []);

  const [shoutoutMessage, setShoutoutMessage] = React.useState('');
  const [requestedSong, setRequestedSong] =
    React.useState<SongRequestPayload>();

  const requestCostInMajor = toAmountInMajor(sessionInfo?.cost || 0);
  const shoutoutCostInMajor = toAmountInMajor(sessionInfo?.shoutoutCost || 0);
  const extraCost = requestedSong ? requestCostInMajor : 0;
  const totalCost = shoutoutMessage ? shoutoutCostInMajor + extraCost : null;

  const { mutate, isPending } = useSendSongRequest();

  const onSendSongRequest = () => {
    if (
      !sessionInfo?.isFree &&
      user?.walletBalance &&
      user?.walletBalance < (sessionInfo?.cost || 0)
    ) {
      toast.info(
        'Insufficient wallet balance to make this request, please top up',
        {
          action: (
            <Button
              label="Top up"
              onPress={() => {
                dismissAllModals();
                push({ pathname: '/profile/top-up' });
                toast.dismiss();
              }}
              size="sm"
              className="w-40"
            />
          ),
        }
      );
    } else {
      mutate(
        {
          sessionId,
          payload: {
            ...(requestedSong || {}),
            songId: requestedSong?.songId || `shoutout_${Crypto.randomUUID()}`,
            shoutOutTo: shoutoutMessage,
            userCurrency: 'NGN',
          },
        },
        {
          onSuccess: () => {
            toast.success('shoutout requested successfully');
            replace({
              pathname: '/session/[id]/request',
              params: { id: sessionId },
            });
          },
          onError: (error) =>
            toast.error(
              Array.isArray(error.message)
                ? error.message.join('\n')
                : error.message
            ),
        }
      );
    }
  };

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View>
          <View className="h-16 px-2 py-3">
            <Back />
          </View>

          <View className="gap-1 p-4 pt-0">
            <H1>Request a shoutout</H1>
            <P className='dark:text-grey-50" text-grey-60'>
              Send a personalized message to the DJ/band.
            </P>
          </View>

          <View className="gap-4 p-4">
            <TextInput
              className="h-[104px] rounded-md border-2 border-accent-moderate p-4 font-aeonik-regular text-base/100 font-medium dark:text-white"
              multiline
              placeholder="Type shoutout here"
              placeholderClassName="text-fg-subtle-light dark:text-fg-subtle-dark text-base/150 font-aeonik-regular"
              onChangeText={setShoutoutMessage}
              autoFocus
            />
            {requestedSong ? (
              <View className="flex-row gap-4 rounded-md bg-grey-10 px-4 py-3 dark:bg-grey-90">
                <View className="flex-1 flex-row items-center justify-between gap-4">
                  <View className="flex-1 flex-row gap-4">
                    <RequestIcon
                      isShoutout={false}
                      iconSource={requestedSong.coverUrl}
                    />
                    <RequestDetails
                      title={requestedSong.title!}
                      subtitle={requestedSong.artist}
                    />
                  </View>

                  <SmBoldLabel className="text-fg-success-light dark:text-fg-success-dark">
                    {formatAmount(requestCostInMajor)}
                  </SmBoldLabel>
                  <TouchableOpacity onPress={modal.present}>
                    <SmBoldLabel className="text-accent-moderate dark:text-accent-moderate">
                      Change
                    </SmBoldLabel>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              sessionInfo?.songRequestEnabled && (
                <TouchableOpacity
                  className="mr-auto h-10 flex-row items-center gap-1 rounded-full bg-accent-subtle-light py-2 pl-6 pr-5 dark:bg-accent-subtle-dark"
                  onPress={modal.present}
                >
                  <MdBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                    Add song request
                  </MdBoldLabel>
                  <Ionicons
                    name="add"
                    size={24}
                    color={isDark ? colors.brand[40] : colors.brand[70]}
                  />
                </TouchableOpacity>
              )
            )}
          </View>

          <Button
            label={`Send request${totalCost !== null ? ` (${formatAmount(totalCost)})` : ''}`}
            className={cn(
              'mx-4 mt-14',
              !sessionInfo?.songRequestEnabled && 'mt-10'
            )}
            disabled={!shoutoutMessage || isPending}
            loading={isPending}
            onPress={onSendSongRequest}
          />
        </View>
      </TouchableWithoutFeedback>
      <Modal
        ref={modal.ref}
        index={0}
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
        snapPoints={snapPoints}
      >
        <SearchSongModalContent
          onDismiss={modal.dismiss}
          onSelectSong={(song) => setRequestedSong(song)}
          sessionId={sessionId}
        />
      </Modal>
    </SafeAreaView>
  );
}
