import { Feather } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  type GetSessionViewersResponse,
  useGetLiveSessionViewers,
} from '@/api/session';
import { UserAvatar } from '@/components/avatars';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import {
  UserActionModal,
  type UserActionModalRef,
} from '@/components/modals/quester-action-modal';
import { EmptyState } from '@/components/ui';
import {
  colors,
  FocusAwareStatusBar,
  LgBoldLabel,
  List,
  MdBoldLabel,
  ScrollView,
  Small,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { useRefetchOnFocus } from '@/lib';

export default function SessionViewers() {
  const { id: sessionId } = useLocalSearchParams<{ id: string }>();

  const {
    isPending,
    data: viewers,
    refetch,
  } = useGetLiveSessionViewers({
    variables: { sessionId },
    enabled: !!sessionId,
    refetchOnReconnect: 'always',
  });

  useRefetchOnFocus(refetch, 0);

  if (isPending) return <LoadingScreen />;

  return (
    <SafeAreaView className="flex-1 ">
      <FocusAwareStatusBar />
      <ScrollView
        className="flex-1 bg-white dark:bg-grey-100"
        showsVerticalScrollIndicator={false}
      >
        <View className="h-16 flex-row items-center gap-2 px-2">
          <Back />
          <LgBoldLabel>Viewers</LgBoldLabel>
        </View>

        <View className="m-4 gap-4 rounded-[20px] bg-bg-subtle-light p-4 dark:bg-bg-subtle-dark">
          <List
            data={viewers}
            key={viewers?.length}
            estimatedItemSize={74}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<EmptyState />}
            renderItem={({ item, index }) => <Viewer {...item} key={index} />}
            ItemSeparatorComponent={() => <View className="size-4" />}
            keyExtractor={(item) => String(item.id)}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const Viewer: React.FC<GetSessionViewersResponse> = ({ quester }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const userActionModalRef = React.useRef<UserActionModalRef>(null);

  const handleMorePress = React.useCallback(() => {
    userActionModalRef.current?.present();
  }, []);

  return (
    <View className="h-16 flex-row items-center justify-between">
      <View className="flex-1 flex-row items-center gap-2.5">
        <UserAvatar avatar={quester.profileImageUrl} hideStatusPill />
        <View className="gap-1">
          <MdBoldLabel>{quester.fullName}</MdBoldLabel>
          <Small className="text-fg-muted-light dark:text-fg-muted-dark">
            {quester.username}
          </Small>
        </View>
      </View>
      <TouchableOpacity onPress={handleMorePress}>
        <Feather
          name="more-horizontal"
          size={24}
          color={isDark ? colors.white : colors.grey[100]}
        />
      </TouchableOpacity>
      <UserActionModal ref={userActionModalRef} user={quester} />
    </View>
  );
};
