import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';

import { useCreateLiveSession } from '@/lib';

export default function CreateLiveSessionLayout() {
  const { formMethods } = useCreateLiveSession();
  return (
    <FormProvider {...formMethods}>
      <Stack>
        <Stack.Screen
          name="type"
          options={{
            headerShown: false,
            presentation: 'containedModal',
            animation: 'slide_from_bottom',
          }}
        />
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="location" options={{ headerShown: false }} />
      </Stack>
    </FormProvider>
  );
}
