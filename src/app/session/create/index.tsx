import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import { AuthLayout } from '@/components/layouts/auth-layout';
import { CostSettingModalContent } from '@/components/modals/cost-setting';
import { GenreSelectorModalContent } from '@/components/modals/genre-select';
import { CostSelector } from '@/components/session/create/cost-selector';
import { FeatureToggle } from '@/components/session/create/feature-toggle';
import {
  Button,
  colors,
  ControlledInput,
  Modal,
  ScrollView,
} from '@/components/ui';
import { formatAmount, useLiveSessionForm } from '@/lib';

export default function CreateLiveSession() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const snapPoints = React.useMemo(() => ['35%'], []);

  const {
    control,
    liveType,
    selectedGenres,
    requestCost,
    shoutoutCost,
    songRequestEnabled,
    shoutoutEnabled,
    videoEnabled,
    genreModal,
    songRequestModal,
    shoutoutModal,
    setValue,
    handleToggleGenre,
    handleEnableSongRequest,
    handleEnableShoutout,
    handleEnableVideoStream,
    trigger,
    formState: { errors },
  } = useLiveSessionForm();

  useEffect(() => {
    if (
      liveType === 'REQUEST' &&
      errors.shoutoutCost &&
      errors.shoutoutCost.message
    ) {
      toast.error(errors.shoutoutCost.message);
    } else if (liveType === 'SHOUTOUT' && errors.cost && errors.cost.message) {
      toast.error(errors.cost.message);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors.shoutoutCost, errors.cost]);

  return (
    <AuthLayout
      title={
        liveType === 'REQUEST' ? 'Song request details' : 'Shout out details'
      }
      subTitle="We will send your account information here."
      onBackPress={() => router.dismissTo('/')}
      footer={
        <Button
          testID="create-live-button"
          label="Continue"
          className="my-4"
          onPress={async () => {
            const isValid = await trigger(
              ['title', 'cost', 'shoutoutCost', 'genres', 'enableVideo'],
              { shouldFocus: true }
            );
            if (isValid) {
              router.push('/session/create/location');
            }
          }}
        />
      }
    >
      <ScrollView
        className="flex-1"
        contentContainerClassName="gap-4"
        showsVerticalScrollIndicator={false}
      >
        <ControlledInput
          testID="title-input"
          control={control}
          name="title"
          label="Title"
        />
        {liveType === 'REQUEST' && (
          <CostSelector
            control={control}
            setValue={setValue}
            name="cost"
            label="Cost per request"
            costOptions={[0, 10000, 20000, 50000]}
            testID="cost-input"
          />
        )}
        {liveType === 'SHOUTOUT' && (
          <CostSelector
            control={control}
            setValue={setValue}
            name="shoutoutCost"
            label="Cost per shoutout"
            costOptions={[0, 10000, 20000, 50000]}
            testID="shoutout-cost-input"
          />
        )}

        {/* <GenreSelector
          selectedGenres={selectedGenres}
          onToggleGenre={handleToggleGenre}
          onOpenModal={() => {
            Keyboard.dismiss();
            genreModal.present();
          }}
        /> */}

        {liveType === 'REQUEST' && (
          <FeatureToggle
            title="Enable shoutout"
            subtitle={
              shoutoutCost ? formatAmount(Number(shoutoutCost)) : undefined
            }
            checked={shoutoutEnabled}
            onChange={handleEnableShoutout}
            accessibilityLabel="Enable Shoutout"
          />
        )}

        {liveType === 'SHOUTOUT' && (
          <FeatureToggle
            title="Enable song requests"
            subtitle={
              requestCost ? formatAmount(Number(requestCost)) : undefined
            }
            checked={songRequestEnabled}
            onChange={handleEnableSongRequest}
            accessibilityLabel="Enable Song Request"
          />
        )}

        <FeatureToggle
          title="Add video streaming"
          subtitle="Available for paid sessions only"
          checked={videoEnabled}
          onChange={handleEnableVideoStream}
          accessibilityLabel="Enable Video streaming"
          disabled={!requestCost || Number(requestCost) === 0 ? true : false}
        />
      </ScrollView>
      <Modal
        ref={genreModal.ref}
        index={0}
        enableDynamicSizing
        hasHandle={false}
        backgroundStyle={{
          backgroundColor: isDark ? '#070707' : colors.white,
        }}
      >
        <GenreSelectorModalContent
          insets={insets}
          selectedGenres={selectedGenres}
          onToggleGenre={handleToggleGenre}
          onDismiss={genreModal.dismiss}
        />
      </Modal>
      <Modal
        ref={shoutoutModal.ref}
        index={1}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: isDark ? '#070707' : colors.white,
        }}
      >
        <CostSettingModalContent
          insets={insets}
          control={control}
          fieldName="shoutoutCost"
          title="Shout out cost"
          testID="shoutout-cost-input"
          onDismiss={shoutoutModal.dismiss}
          setValue={setValue}
        />
      </Modal>
      <Modal
        ref={songRequestModal.ref}
        index={2}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: isDark ? '#070707' : colors.white,
        }}
      >
        <CostSettingModalContent
          insets={insets}
          control={control}
          fieldName="cost"
          title="Song request cost"
          testID="cost-input"
          onDismiss={songRequestModal.dismiss}
          setValue={setValue}
        />
      </Modal>
    </AuthLayout>
  );
}
