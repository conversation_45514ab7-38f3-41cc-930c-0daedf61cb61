import { useRouter } from 'expo-router';
import React from 'react';
import { type Point } from 'react-native-google-places-autocomplete';
import { toast } from 'sonner-native';

import { useCreateLiveSession } from '@/api/session';
import LocationInput from '@/components/input/location-autocomplete';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { LocationOption } from '@/components/session/location/option';
import { Button, P, RadioGroup, ScrollView, View } from '@/components/ui';
import {
  cn,
  toAmountInMinor,
  useLiveSessionForm,
  useLiveSessionLocation,
} from '@/lib';
import { useSession } from '@/lib/session';
import { type LocationType } from '@/types';

export default function LiveSessionLocation() {
  const router = useRouter();
  const {
    locationType,
    address,
    isEditing,
    setLocationType,
    setAddress,
    setIsEditing,
  } = useLiveSessionLocation();

  const { setValue, watch } = useLiveSessionForm();

  const onSelectLocation = React.useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const { mutate: createLiveSession, isPending } = useCreateLiveSession();
  const setJoinedSessionId = useSession.use.setJoinedSessionId();
  const setLiveSession = useSession.use.setLiveSession();

  const handleSubmit = () => {
    const {
      cost,
      enableVideo,
      location,
      title,
      shoutoutEnabled,
      songRequestEnabled,
      shoutoutCost,
      type,
    } = watch();

    if (locationType === 'PHYSICAL' && (!location || !location.country)) {
      toast.error('Location is required for physical events');
      return;
    }

    const isFree = Number(cost) === 0;
    const form: FormData = new FormData();

    form.append('title', title.trim());
    form.append('type', type);
    form.append('locationType', locationType);
    // @ts-ignore
    form.append('isFree', isFree);
    // @ts-ignore
    form.append('shoutoutEnabled', shoutoutEnabled);
    // @ts-ignore
    form.append('songRequestEnabled', songRequestEnabled);
    // @ts-ignore
    form.append('cost', cost ? toAmountInMinor(cost as string) : 0);
    form.append(
      'shoutoutCost',
      // @ts-ignore
      shoutoutCost ? toAmountInMinor(shoutoutCost as string) : 0
    );
    // TODO: use user preferred currency
    form.append('currency', 'NGN');
    locationType === 'PHYSICAL' &&
      form.append('location', JSON.stringify(location));
    // @ts-ignore
    form.append('enableVideo', enableVideo);

    createLiveSession(form, {
      onSuccess: (liveSession) => {
        toast.success('Successfully created live session');
        setJoinedSessionId(liveSession.id);
        setLiveSession({ djSession: liveSession, questersOnSession: 0 });
        router.replace({
          pathname: '/session/[id]',
          params: { id: liveSession.id },
        });
      },
      onError: (error) => toast.error(error.message),
    });
  };

  return (
    <AuthLayout
      title="Location"
      subTitle="Where is this Live session taking place"
      footer={
        <Button
          testID="location-button"
          label="Go Live"
          className="my-4"
          loading={isPending}
          onPress={handleSubmit}
        />
      }
    >
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <RadioGroup
          value={locationType}
          onValueChange={setLocationType}
          className="gap-4 py-4"
        >
          <View className="gap-px">
            <LocationOption
              value="PHYSICAL"
              currentValue={locationType}
              title="Physical"
              description="The session will be displayed on the map, with increased visibility"
              labelClassName={cn(
                !isEditing &&
                  !!address &&
                  'text-fg-muted-light dark:text-fg-muted-dark text-xs'
              )}
              customContent={
                !isEditing && !!address ? <P>{address}</P> : undefined
              }
              onPress={() => setIsEditing(true)}
            />
            {locationType === 'PHYSICAL' && isEditing && (
              <LocationInput
                onSelectLocation={onSelectLocation}
                onBlur={() => setIsEditing(false)}
                onFocus={() => setIsEditing(true)}
                defaultValue={watch('location')}
              />
            )}
          </View>

          <LocationOption
            value="ONLINE"
            currentValue={locationType}
            title="Online"
            description="The session will not be displayed on the map, limiting visibility"
            onPress={() => {
              setIsEditing(false);
              setAddress('');
            }}
          />
        </RadioGroup>
      </ScrollView>
    </AuthLayout>
  );
}
