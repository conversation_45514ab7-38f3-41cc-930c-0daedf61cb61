import { useFocusEffect, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { BlurredBackground } from '@/components/background/blur';
import { LiveTypeModalContent } from '@/components/modals/live-type';
import {
  FocusAwareStatusBar,
  PersistentModal,
  useModal,
  View,
} from '@/components/ui';
import { type CreateLiveFormType } from '@/lib';

import { styling } from '../[id]/recording';

export default function LiveSessionType() {
  const router = useRouter();
  const typeModal = useModal();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const { setValue } = useFormContext<CreateLiveFormType>();

  useFocusEffect(
    React.useCallback(() => {
      typeModal.present();
      return () => typeModal.dismiss();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
  );

  const handleTypeSelect = (type: 'REQUEST' | 'SHOUTOUT') => {
    setValue('type', type);
    setValue('songRequestEnabled', type === 'REQUEST');
    setValue('shoutoutEnabled', type === 'SHOUTOUT');
    router.dismissAll();
    router.replace('/session/create');
  };

  return (
    <View className="relative flex-1 bg-bg-overlay-light dark:bg-bg-overlay-dark">
      <FocusAwareStatusBar hidden />
      <BlurredBackground
        isDark={isDark}
        blurStyle={styles.blurView}
        overlayStyle={styles.overlay}
      />

      <PersistentModal
        ref={typeModal.ref}
        onBackdropPress={() => console.log('close')}
      >
        <LiveTypeModalContent
          onSelectType={handleTypeSelect}
          setValue={setValue}
          onDismiss={typeModal.dismiss}
        />
      </PersistentModal>
    </View>
  );
}
