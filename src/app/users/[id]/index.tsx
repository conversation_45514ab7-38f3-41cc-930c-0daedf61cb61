import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Linking, Pressable, Text, TouchableOpacity, View } from 'react-native';

import { useGetEvents } from '@/api/events';
import { useGetUser } from '@/api/user';
import { RegularAvatar } from '@/components/avatars/regular-avatar';
import { EventFavoriteCard } from '@/components/cards/event-favorite';
import { Back } from '@/components/common/back';
import { IconComponent } from '@/components/common/icon-component';
import { MyEventsLoading } from '@/components/loading/my-events';
import { EmptyState } from '@/components/ui';
import {
  Button,
  FocusAwareStatusBar,
  H3,
  Image,
  List,
  P,
  SafeAreaView,
} from '@/components/ui';
import { cn, useAccountFavourite, useAuth, useProfileImage } from '@/lib';

export default function UserProfile() {
  const { push } = useRouter();
  const currentUser = useAuth.use.user();
  const { id } = useLocalSearchParams<{
    id: string;
  }>();
  const [selectedTab, setSelectedTab] = useState<'events' | 'posts'>('events');

  const { getProfileImageUrl } = useProfileImage();

  const { data: user } = useGetUser({ variables: { id }, enabled: !!id });

  const { data: eventsData, isPending } = useGetEvents({
    variables: {
      userId: currentUser?.id,
      organizerId: user?.id,
    },
    enabled: !!user?.id && selectedTab === 'events',
  });

  const { favourited, toggleFavourite } = useAccountFavourite(id);

  const chatRoomId = React.useMemo(
    () => [currentUser?.id, user?.id].sort().join('_'),
    [currentUser?.id, user?.id]
  );

  if (!user) {
    return;
  }

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />

      <View className="flex flex-row items-center justify-between px-2 py-3">
        <Back />
        {/* <View className="relative">
            <TouchableOpacity
              className="size-8 items-center justify-center"
              onPress={() => setShowMenu(!showMenu)}
            >
              <Entypo
                name="dots-three-vertical"
                size={24}
                color={colors.brand['60']}
                className="text-fg-link"
              />
            </TouchableOpacity>
            {showMenu && (
              <View className="absolute right-0 mt-2 w-40 rounded-md bg-white p-2 shadow-lg dark:bg-bg-surface-dark">
                <TouchableOpacity
                  className="py-2"
                  onPress={() => {
                    setShowMenu(false);
                  }}
                >
                  <Text className="text-fg-default-light dark:text-fg-default-dark text-sm">
                    Block user
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View> */}
      </View>
      <View className="flex-1 gap-4 p-4">
        <View className="items-start gap-4">
          <RegularAvatar
            avatar={
              getProfileImageUrl(user?.profileImageUrl) ||
              require('~/assets/images/avatar.png')
            }
            size={80}
          />
          <H3>{user.username}</H3>
          <View className="gap-y-4">
            <View className="flex-row gap-x-2">
              <Button
                label={favourited ? 'Favourited' : 'Favourite'}
                className="h-[30px] w-[74-px]"
                variant={favourited ? 'secondary' : 'default'}
                onPress={toggleFavourite}
              />
              <Button
                variant="secondary"
                label="Chat"
                className="h-[30px] w-[74-px]"
                onPress={() => {
                  push({
                    pathname: '/chats/[chatRoomId]',
                    params: {
                      chatRoomId,
                      username: user.username,
                      userId: user.id,
                      userAvatar: user.profileImageUrl,
                    },
                  });
                }}
              />
            </View>
          </View>
        </View>
        <View className="gap-[5px]">
          {user.role !== 'QUESTER' && (
            <Text className="text-body-sm text-fg-muted-light dark:text-fg-muted-dark">
              {user.category}
            </Text>
          )}
          {user.bio && <P className="pb-1 text-xs">{user.bio}</P>}
          {user.url && (
            <Text className="text-xs font-bold text-fg-link">{user.url}</Text>
          )}
        </View>
        {(user.socials?.spotify ||
          user.socials?.instagram ||
          user.socials?.snapchat ||
          user.socials?.youtube ||
          user.socials?.x ||
          user.socials?.tiktok) && (
          <View className="flex-row items-center gap-x-4 py-2">
            {user.socials?.spotify && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.spotify;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="spotify"
                  size={32}
                  iconType="font-awesome"
                  color="#2EBD59"
                />
              </TouchableOpacity>
            )}
            {user.socials?.instagram && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.instagram;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="Instagram"
                  iconType="custom-svg"
                  className="size-8"
                />
              </TouchableOpacity>
            )}
            {user.socials?.snapchat && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.snapchat;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="Snapchat"
                  iconType="custom-svg"
                  className="size-8"
                />
              </TouchableOpacity>
            )}
            {user.socials?.tiktok && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.tiktok;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="Tiktok"
                  iconType="custom-svg"
                  className="size-8"
                />
              </TouchableOpacity>
            )}
            {user.socials?.x && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.x;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="X"
                  className="size-8"
                  iconType="custom-svg"
                />
              </TouchableOpacity>
            )}
            {user.socials?.youtube && (
              <TouchableOpacity
                onPress={() => {
                  const url = user.socials?.youtube;
                  if (url) {
                    Linking.openURL(url);
                  }
                }}
              >
                <IconComponent
                  iconName="youtube"
                  size={32}
                  iconType="entypo"
                  color="#FF0000"
                />
              </TouchableOpacity>
            )}
          </View>
        )}
        {user.role !== 'QUESTER' ? (
          <View className="flex-row rounded-full bg-bg-subtle-light p-0.5 dark:bg-bg-subtle-dark">
            <Pressable
              onPress={() => setSelectedTab('events')}
              className={cn(
                'items-center flex-1 py-2 rounded-full bg-transparent',
                selectedTab === 'events' &&
                  'rounded-full bg-bg-surface-light dark:bg-bg-surface-dark'
              )}
            >
              <Text
                className={cn(
                  'text-sm font-medium text-fg-muted-light dark:text-fg-muted-dark',
                  selectedTab === 'events' && 'text-fg-link dark:text-fg-link'
                )}
              >
                Events
              </Text>
            </Pressable>
            <Pressable
              onPress={() => setSelectedTab('posts')}
              className={cn(
                'items-center flex-1 py-2 rounded-full bg-transparent',
                selectedTab === 'posts' &&
                  'rounded-full bg-bg-surface-light dark:bg-bg-surface-dark'
              )}
            >
              <Text
                className={cn(
                  'text-sm font-medium text-fg-muted-light dark:text-fg-muted-dark',
                  selectedTab === 'posts' && 'text-fg-link dark:text-fg-link'
                )}
              >
                Posts
              </Text>
            </Pressable>
          </View>
        ) : (
          <View className="flex-row items-center justify-center rounded-full bg-bg-surface-light py-2 text-center dark:bg-bg-surface-dark">
            <Text className="text-sm font-medium text-fg-link">Posts</Text>
          </View>
        )}
        {user.role !== 'QUESTER' && selectedTab === 'events' && (
          <>
            {isPending ? (
              <MyEventsLoading />
            ) : (
              <List
                data={
                  Array.isArray(eventsData?.events) ? eventsData.events : []
                }
                key={selectedTab}
                estimatedItemSize={74}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={<EmptyState />}
                renderItem={({ item }) => (
                  <EventFavoriteCard {...item} attendees={item.ticketsSold} />
                )}
                ItemSeparatorComponent={() => <View className="size-4" />}
                keyExtractor={(item) =>
                  item?.id?.toString?.() ?? Math.random().toString()
                }
              />
            )}
          </>
        )}
        {(user.role === 'QUESTER' || selectedTab === 'posts') && (
          <View className="flex-row flex-wrap justify-between">
            {true ? (
              <EmptyState />
            ) : (
              [].map((_, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => console.log(`Pressed image ${index}`)}
                >
                  <View className="mb-1 h-[231px] w-[117px]">
                    <Image
                      source={require('~/assets/images/feed_image.png')}
                      className="size-full rounded-sm"
                    />
                    <View className="absolute bottom-1.5 left-1.5 flex-row items-center">
                      <Ionicons
                        name="eye-outline"
                        size={12}
                        color="#fff"
                        className="mr-1"
                      />
                      <Text className="text-xs font-bold text-white">
                        12.4K
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))
            )}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}
