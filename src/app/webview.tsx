import { useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

import { Back } from '@/components/common/back';
import { FocusAwareStatusBar, View } from '@/components/ui';

export default function WebviewScreen() {
  const { uri } = useLocalSearchParams<{ uri: string }>();

  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar />
      <View className="px-2 py-4">
        <Back />
      </View>
      <WebView source={{ uri }} />
    </SafeAreaView>
  );
}
