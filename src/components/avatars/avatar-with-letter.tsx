import { useRouter } from 'expo-router';

import { H5, Image, TouchableOpacity } from '@/components/ui';
import { cn, useLoggedInUser, useProfileImage } from '@/lib';

interface AvatarWithLetterProps {
  onPress?: () => void;
  className?: string;
}
export const AvatarWithLetter: React.FC<AvatarWithLetterProps> = ({
  onPress,
  className,
}) => {
  const router = useRouter();
  const { data: user } = useLoggedInUser();
  const { imageKey, getProfileImageUrl } = useProfileImage();
  const profileImageUrl = getProfileImageUrl(user?.profileImageUrl);

  return (
    <TouchableOpacity
      className={cn(
        'size-8 items-center justify-center self-center rounded-full bg-brand-60',
        className
      )}
      onPress={() => (onPress ? onPress() : router.navigate('/profile'))}
    >
      {profileImageUrl ? (
        <Image
          key={imageKey || 'profile-avatar'}
          className="size-8 rounded-full"
          source={profileImageUrl}
          priority="high"
        />
      ) : (
        <H5 className="text-brand-20">{user?.username.charAt(0) || 'P'}</H5>
      )}
    </TouchableOpacity>
  );
};
