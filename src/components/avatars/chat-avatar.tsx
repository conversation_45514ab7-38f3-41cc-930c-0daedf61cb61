import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import {
  colors,
  H4,
  Image,
  OnlineIndicator,
  Pressable,
  View,
} from '@/components/ui';

interface ChatAvatarProps {
  username: string;
  userId: string;
  avatar: string;
  online?: boolean;
}

export const ChatAvatar: React.FC<ChatAvatarProps> = ({
  username,
  userId,
  avatar,
  online,
}) => {
  const { push } = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="items-center gap-2">
      <Pressable
        className="relative"
        onPress={() =>
          push({ pathname: '/users/[id]', params: { id: userId } })
        }
      >
        <Image
          className="size-16 rounded-full"
          source={avatar}
          priority="high"
        />
        {online && (
          <View className="absolute bottom-0 right-0 size-6 items-center justify-center rounded-full bg-white dark:bg-grey-100">
            <OnlineIndicator
              color={isDark ? colors.green[50] : colors.green[60]}
              strokeColor={isDark ? colors.grey[100] : colors.white}
            />
          </View>
        )}
      </Pressable>
      <H4>{username}</H4>
    </View>
  );
};
