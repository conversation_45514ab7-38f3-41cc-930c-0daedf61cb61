/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';

import { Image, Small, Tiny, TouchableOpacity, View } from '@/components/ui';
import { cn } from '@/lib';

interface LiveUserAvatarProps {
  username: string;
  avatar: string;
  className?: string;
  textClassName?: string;
  hideStatusPill?: boolean;
  onPress?: () => void;
  hasRing?: boolean;
}

export const LiveUserAvatar: React.FC<LiveUserAvatarProps> = ({
  username,
  avatar,
  className,
  textClassName,
  hideStatusPill,
  onPress,
  hasRing = true,
}) => {
  return (
    <TouchableOpacity
      className={cn('gap-1 items-center', className)}
      onPress={onPress}
    >
      <View className="relative size-16 items-center justify-center rounded-full">
        {hasRing ? (
          <>
            <LinearGradient
              colors={['#664FB0', '#A992F5']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{
                position: 'absolute',
                inset: 0,
                borderRadius: 9999,
              }}
            />
            {/* White/transparent spacer to create inner cutout */}
            <View className="absolute size-[60px] rounded-full bg-brand-10 dark:bg-brand-90" />
            <Image
              source={avatar || require('~/assets/images/avatar.png')}
              className="size-14 rounded-full"
              contentFit="cover"
              priority="high"
            />
          </>
        ) : (
          <Image
            source={avatar || require('~/assets/images/avatar.png')}
            className="size-14 rounded-full"
            contentFit="cover"
            priority="high"
          />
        )}
        {!hideStatusPill && (
          <View className="absolute -bottom-[5px] w-10 flex-row items-center justify-center gap-0.5 rounded-md bg-red-50 px-1 py-0.5">
            <View className="size-1 rounded-full bg-white" />
            <Tiny className="text-[10px] text-xs/100 font-bold text-white">
              LIVE
            </Tiny>
          </View>
        )}
      </View>
      <Small className={textClassName}>{username}</Small>
    </TouchableOpacity>
  );
};
