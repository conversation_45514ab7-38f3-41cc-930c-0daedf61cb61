import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet } from 'react-native';

import { Button, Image, Small, TouchableOpacity, View } from '@/components/ui';
import { cn, useAccountFavourite } from '@/lib';

interface PopularAccountCardProps {
  id: string;
  username: string;
  avatar: string;
  className?: string;
  textClassName?: string;
  onPress?: () => void;
}

export const PopularAccountCard: React.FC<PopularAccountCardProps> = ({
  id,
  username,
  avatar,
  className,
  textClassName,
  onPress,
}) => {
  const { favourited, toggleFavourite } = useAccountFavourite(id);
  return (
    <TouchableOpacity
      className={cn('w-[108px] rounded-md p-2 relative', className)}
      onPress={onPress}
    >
      <LinearGradient
        colors={['#FFFFFF', '#FFFFFF']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={{
          borderRadius: 8,
          opacity: 0.05,
          ...StyleSheet.absoluteFillObject,
        }}
      />
      <View className="items-center gap-2">
        <Image
          source={avatar}
          className="size-16 rounded-full"
          contentFit="cover"
          priority="high"
        />
        <Small className={textClassName} numberOfLines={1}>
          {username}
        </Small>
        <Button
          label={favourited ? 'Favourited' : 'Favourite'}
          className="h-8 px-2"
          textClassName="text-xs/150"
          onPress={toggleFavourite}
        />
      </View>
    </TouchableOpacity>
  );
};
