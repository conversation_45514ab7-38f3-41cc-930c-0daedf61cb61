/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';

import { Image, View } from '@/components/ui';
import { cn } from '@/lib';

interface RegularAvatarProps {
  avatar: string;
  className?: string;
  size?: number;
  hasRing?: boolean;
}

export const RegularAvatar: React.FC<RegularAvatarProps> = ({
  avatar,
  className,
  size,
  hasRing = true,
}) => {
  return (
    <View
      className={cn('relative items-center justify-center', className)}
      style={{ width: size ?? 64, height: size ?? 64 }}
    >
      {hasRing ? (
        <>
          <LinearGradient
            colors={['#664FB0', '#A992F5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{
              position: 'absolute',
              inset: 0,
              borderRadius: 9999,
            }}
          />
          {/* White/transparent spacer to create inner cutout */}
          <View
            className="absolute rounded-full bg-brand-10 dark:bg-brand-90"
            style={{ width: (size ?? 64) - 4, height: (size ?? 64) - 4 }}
          />
          <Image
            source={avatar}
            style={{
              width: (size ?? 64) - 8,
              height: (size ?? 64) - 8,
              borderRadius: 9999,
            }}
            contentFit="cover"
            priority="high"
          />
        </>
      ) : (
        <Image
          source={avatar}
          style={{
            width: size ?? 64,
            height: size ?? 64,
            borderRadius: 9999,
          }}
          contentFit="cover"
          priority="high"
        />
      )}
    </View>
  );
};
