import { BlurView } from 'expo-blur';
import React from 'react';
import { type StyleProp, type ViewStyle } from 'react-native';

import { View } from '@/components/ui';

interface BlurredBackgroundProps {
  isDark: boolean;
  blurStyle: StyleProp<ViewStyle>;
  overlayStyle: StyleProp<ViewStyle>;
  intensity?: number;
}

export const BlurredBackground: React.FC<BlurredBackgroundProps> = ({
  isDark,
  blurStyle,
  overlayStyle,
  intensity = isDark ? 20 : 70,
}) => {
  return (
    <BlurView
      tint={isDark ? 'systemMaterialLight' : 'extraLight'}
      intensity={intensity}
      style={blurStyle}
    >
      <View style={overlayStyle} />
    </BlurView>
  );
};
