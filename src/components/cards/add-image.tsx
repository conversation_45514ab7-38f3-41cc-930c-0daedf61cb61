import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  type FieldValues,
  type Path,
  type UseFormSetValue,
} from 'react-hook-form';

import { Image, Pressable, View } from '@/components/ui';

type ExtractArrayElement<T> = T extends (infer U)[]
  ? U & { uri: string }
  : never;

interface AddImageCardProps<T extends FieldValues, K extends Path<T>> {
  photo: ExtractArrayElement<NonNullable<T[K]>>;
  photos: NonNullable<T[K]>;
  setValue: UseFormSetValue<T>;
  name: K;
  onImageChange?: () => Promise<void>;
}

export const AddImageCard = <T extends FieldValues, K extends Path<T>>({
  photo,
  photos,
  setValue,
  name,
  onImageChange,
}: AddImageCardProps<T, K>) => {
  return (
    <View className="relative size-20 rounded-md">
      <Pressable
        onPress={() => {
          const updated = photos.filter((p: T[K]) => p !== photo) as T[K];
          setValue(name, updated);
        }}
        className="absolute -right-1 -top-1 z-10"
      >
        <View className="size-6 items-center justify-center rounded-full bg-white dark:bg-white">
          <Ionicons name="close" size={16} color="#000" />
        </View>
      </Pressable>

      <Pressable onPress={onImageChange} className="size-full">
        <Image source={{ uri: photo.uri }} className="size-full rounded-md" />
      </Pressable>
    </View>
  );
};
