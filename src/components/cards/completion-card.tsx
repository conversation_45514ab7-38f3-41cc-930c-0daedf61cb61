import {
  Image,
  MdBoldLabel,
  Small,
  TouchableOpacity,
  View,
} from '@/components/ui';

interface CompleteCreatorOnboardingCardProps {
  title: string;
  description: string;
  imageSource: string;
  onPress?: () => void;
}

export function CompletionCard({
  title,
  description,
  onPress,
  imageSource,
}: CompleteCreatorOnboardingCardProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="relative w-full rounded-lg bg-white px-4 py-3 dark:bg-grey-100"
    >
      <CardContent
        imageSource={imageSource}
        title={title}
        description={description}
      />
    </TouchableOpacity>
  );
}

interface CardContentProps {
  title: string;
  description: string;
  imageSource: string;
}

const CardContent = ({ imageSource, title, description }: CardContentProps) => (
  <View className="flex w-full flex-row gap-4">
    <View className="my-auto size-10 items-center justify-center border-grey-60">
      <Image
        className="my-auto size-10"
        source={imageSource}
        contentFit="cover"
      />
    </View>
    <View className="w-full max-w-[273px] gap-2">
      <MdBoldLabel>{title}</MdBoldLabel>
      <Small className="text-grey-60 dark:text-grey-50">{description}</Small>
    </View>
  </View>
);
