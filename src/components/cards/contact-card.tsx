import {
  Checkbox,
  Image,
  MdBoldLabel,
  TouchableOpacity,
  View,
} from '@/components/ui';

interface ContactCardProps {
  name: string;
  image: string;
  isSelected?: boolean;
  onPress?: () => void;
}

export function ContactCard({
  name,
  onPress,
  image,
  isSelected = false,
}: ContactCardProps) {
  return (
    <View className="flex w-full flex-row items-center justify-between">
      <View className="my-auto flex-row items-center gap-4">
        <View className="size-10 overflow-hidden rounded-full">
          <Image
            source={image}
            className="size-10 rounded-full"
            contentFit="cover"
          />
        </View>
        <MdBoldLabel>{name}</MdBoldLabel>
      </View>
      <TouchableOpacity
        onPress={onPress}
        className="relative bg-white dark:bg-grey-90"
      >
        <Checkbox.Icon checked={isSelected} />
      </TouchableOpacity>
    </View>
  );
}
