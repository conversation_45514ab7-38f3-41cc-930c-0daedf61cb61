import { Feather, Ionicons, Octicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import moment from 'moment';
import React from 'react';

import { EventFormat, type IEvent } from '@/api/events';
import { useFavoriteToggle } from '@/lib';

import ConfirmationDialog from '../dialogs/confirmation';
import {
  colors,
  Image,
  MdBoldLabel,
  Pressable,
  Tiny,
  TouchableOpacity,
  View,
} from '../ui';

interface EventFavoriteCardProps extends IEvent {
  attendees: number;
  isFavoriteTab?: boolean;
}

export const EventFavoriteCard: React.FC<EventFavoriteCardProps> = ({
  title,
  bannerUrl,
  startTime,
  location,
  id,
  slug,
  eventFormat,
  onlineEventUrl,
  isfavourite,
  isFavoriteTab,
  status,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const [confirmVisible, setConfirmVisible] = React.useState(false);

  const hasPhysicalLocation = eventFormat !== EventFormat.ONLINE;
  const eventAddress = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';
  return (
    <Link
      href={{
        pathname: '/events/[id]',
        params: {
          id,
          slug,
        },
      }}
      asChild
    >
      <Pressable className="h-[152px] flex-1 flex-row gap-4 rounded-lg bg-grey-10 p-4 dark:bg-grey-90">
        <Image
          source={bannerUrl}
          className="size-[120px] rounded-lg"
          priority="high"
        />
        <View className="flex-1 gap-2">
          <View className="flex-row items-center justify-between gap-2">
            <MdBoldLabel className="max-w-52 flex-1" numberOfLines={1}>
              {title}
            </MdBoldLabel>
            <TouchableOpacity
              className="shrink-0"
              onPress={() =>
                isFavoriteTab ? setConfirmVisible(true) : handleFavToggle()
              }
            >
              <Octicons
                name={favourited ? 'heart-fill' : 'heart'}
                size={24}
                color={favourited ? colors.red[50] : colors.brand[40]}
              />
            </TouchableOpacity>
          </View>
          <View className="gap-2">
            <View className="h-[26px] w-[73px] flex-row items-center gap-1 rounded-full border border-brand-30 bg-white px-2 py-1 dark:border-brand-80 dark:bg-grey-100">
              <Feather name="calendar" size={16} color={colors.brand[40]} />
              <Tiny className="text-brand-70 dark:text-brand-40">
                {moment.utc(startTime).format('D MMM')}
              </Tiny>
            </View>
            <View className="h-[26px] flex-row items-center gap-1 self-start rounded-full border border-brand-30 bg-white px-2 py-1 dark:border-brand-80 dark:bg-grey-100">
              <Ionicons
                name="location-outline"
                size={16}
                color={colors.brand[40]}
              />
              <Tiny
                className="shrink text-brand-70 dark:text-brand-40"
                numberOfLines={1}
              >
                {eventAddress}
              </Tiny>
            </View>
            {status === 'DRAFT' && (
              <View className="justify-center self-start rounded-full bg-bg-warning-light px-2 py-1 dark:bg-bg-warning-dark">
                <Tiny className="text-dark dark:text-white">Pending</Tiny>
              </View>
            )}
          </View>
          {/* <View className="flex-row items-center gap-0.5">
          <View className="flex-row items-center justify-between">
            {colorList.map((color, idx) => (
              <View
                key={idx}
                className={cn(
                  `size-[19px] rounded-full border border-grey-20 dark:border-grey-80 ${color}`,
                  idx !== 0 && '-ml-2'
                )}
                style={{ zIndex: colorList.length + idx }}
              />
            ))}
          </View>
          <XsRegularLabel className="text-grey-60 dark:text-grey-50">
            {attendees} others attending
          </XsRegularLabel>
        </View> */}
        </View>

        <ConfirmationDialog
          visible={confirmVisible}
          message="Are you sure you want to remove this event from your favourites?"
          onCancel={() => setConfirmVisible(false)}
          onConfirm={() => {
            setConfirmVisible(false);
            handleFavToggle();
          }}
        />
      </Pressable>
    </Link>
  );
};
