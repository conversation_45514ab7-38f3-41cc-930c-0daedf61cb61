import { Feather } from '@expo/vector-icons';
import { format, isPast, parseISO } from 'date-fns';
import { Link } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { type IEvent } from '@/api/events/types';

import { Button, H3, Image, Pressable, View, XsRegularLabel } from '../ui';

export interface EventCardWithActionProps {
  image: string;
  title: string;
  month: string;
  date: string;
  attendees: string;
  address: string;
}

export const EventCardWithAction: React.FC<
  Partial<IEvent> & { attendees: number; id: string }
> = ({ bannerUrl, title, startTime, endTime, id, slug, location }) => {
  const date = parseISO(startTime || '');
  const month = format(date, 'MMM'); // "May"
  const day = format(date, 'd'); // "23"
  const isEventInThePast = endTime ? isPast(endTime) : true;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Link
      href={{
        pathname: '/events/[id]',
        params: {
          id,
          slug,
        },
      }}
      asChild
    >
      <Pressable className="flex-row items-center gap-x-2 rounded-md bg-grey-30 pr-3 dark:bg-grey-80">
        <Image
          source={bannerUrl}
          className="size-[136px] rounded-l-md"
          priority="high"
        />
        <View className="flex-1 gap-y-2">
          <H3>{title}</H3>
          <View className="flex-row items-center gap-x-0.5 py-1">
            <Feather
              name="map-pin"
              size={10}
              color={isDark ? 'white' : 'black'}
            />
            <XsRegularLabel>{location?.landmark}</XsRegularLabel>
          </View>
          {!isEventInThePast && (
            <Link
              href={{
                pathname: '/events/[id]',
                params: {
                  id,
                  slug,
                },
              }}
              asChild
            >
              <Button
                className="h-7 w-24"
                textClassName="text-xs/100 font-aeonik-black font-bold text-white dark:text-white"
                label="Get Tickets"
              />
            </Link>
          )}
        </View>
        <View className="size-14 items-center gap-1 rounded-sm bg-brand-60 px-2 py-1">
          <H3 className="text-grey-100 dark:text-white">
            {month.toUpperCase()}
          </H3>
          <H3 className="text-grey-100 dark:text-white">{day}</H3>
        </View>
      </Pressable>
    </Link>
  );
};
