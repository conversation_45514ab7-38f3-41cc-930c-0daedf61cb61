import {
  Feather,
  FontAwesome,
  FontAwesome6,
  Ionicons,
} from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import React from 'react';
import { StyleSheet } from 'react-native';

import { type IEventLocation } from '@/api/events';
import {
  colors,
  H2,
  Image,
  TouchableOpacity,
  View,
  XsRegularLabel,
} from '@/components/ui';
import { blurhash, useFavoriteToggle } from '@/lib';

interface EventCardProps {
  image: string;
  title: string;
  date: string;
  location?: IEventLocation;
  id: string;
  slug: string;
  onlineEventUrl?: string;
  hasPhysicalLocation: boolean;
  isfavourite?: boolean;
}
export const EventCard: React.FC<EventCardProps> = ({
  image,
  title,
  date,
  location,
  id,
  slug,
  hasPhysicalLocation,
  onlineEventUrl,
  isfavourite,
}) => {
  const router = useRouter();
  const locationText = hasPhysicalLocation
    ? location?.landmark ||
      location?.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });
  return (
    <TouchableOpacity
      onPress={() => {
        router.push({
          pathname: '/events/[id]',
          params: { id, slug },
        });
      }}
    >
      <View className="relative h-[278px] flex-1 rounded-lg">
        <Image
          className="size-full rounded-lg"
          source={image}
          placeholder={{ blurhash }}
          contentFit="cover"
          priority="high"
        />
        <View className="absolute size-full items-end justify-between gap-2 px-4 py-3">
          <TouchableOpacity
            className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90"
            onPress={handleFavToggle}
          >
            {favourited ? (
              <FontAwesome name="heart" size={16} color={colors.red[60]} />
            ) : (
              <FontAwesome6 name="heart" size={16} color={colors.brand[40]} />
            )}
          </TouchableOpacity>
          <View className="h-[93px] w-full justify-between rounded-lg bg-[#FFFFFF05] p-4">
            <BlurView
              tint="systemUltraThinMaterialDark"
              intensity={70}
              style={styles.blurView}
            >
              <H2 className="text-white" numberOfLines={1}>
                {title}
              </H2>
              <View className="flex-row gap-4">
                <View className="h-6 flex-row items-center gap-0.5">
                  <Feather name="calendar" size={16} color="white" />
                  <XsRegularLabel className="text-white">{date}</XsRegularLabel>
                </View>
                <View className="h-6 flex-row items-center gap-0.5">
                  <Ionicons name="location-outline" size={16} color="white" />
                  <XsRegularLabel className="text-white">
                    {locationText}
                  </XsRegularLabel>
                </View>
              </View>
            </BlurView>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  blurView: {
    ...StyleSheet.absoluteFillObject,
    overflow: 'hidden',
    backgroundColor: 'transparent',
    flexDirection: 'column',
    justifyContent: 'space-between',
    borderRadius: 16,
    padding: 16,
  },
});
