import { useRouter } from 'expo-router';
import React from 'react';

import { cn, useAccountFavourite } from '@/lib';

import ConfirmationDialog from '../dialogs/confirmation';
import {
  colors,
  HeartFilledIcon,
  Image,
  Pressable,
  Small,
  SmBoldLabel,
  TouchableOpacity,
  View,
} from '../ui';

interface FavouriteAccountCardProps {
  image: string;
  fullname: string;
  username: string;
  id: string;
}

export const FavouriteAccountCard: React.FC<FavouriteAccountCardProps> = ({
  image,
  fullname,
  username,
  id,
}) => {
  const router = useRouter();
  const { toggleFavourite } = useAccountFavourite(id);
  const [confirmVisible, setConfirmVisible] = React.useState(false);

  return (
    <Pressable
      onPress={() => router.push({ pathname: '/users/[id]', params: { id } })}
      className={cn('h-16 flex-1 flex-row gap-3.5 items-center')}
    >
      <Image
        className="size-10 rounded-full"
        source={image || require('~/assets/images/avatar.png')}
        contentFit="cover"
        priority="high"
      />
      <View className="flex-1 gap-1">
        <SmBoldLabel numberOfLines={1}>{fullname}</SmBoldLabel>
        <Small numberOfLines={1}>{username}</Small>
      </View>
      <TouchableOpacity
        className="size-10 items-center justify-center"
        onPress={() => setConfirmVisible(true)}
      >
        <HeartFilledIcon color={colors.red[50]} />
      </TouchableOpacity>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to remove this account from your favourites?"
        onCancel={() => setConfirmVisible(false)}
        onConfirm={() => {
          setConfirmVisible(false);
          toggleFavourite();
        }}
      />
    </Pressable>
  );
};
