import { Button, MdBold<PERSON><PERSON><PERSON>, <PERSON>, View } from '@/components/ui';
import { useAccountFavourite } from '@/lib';

import { RegularAvatar } from '../avatars/regular-avatar';

interface FavouriteCardProps {
  id: string;
  name: string;
  username: string;
  image: string;
}

export function FavouriteCard({
  id,
  name,
  username,
  image,
}: FavouriteCardProps) {
  const { favourited, toggleFavourite } = useAccountFavourite(id);
  return (
    <View className={`flex w-full flex-row items-center justify-between gap-4`}>
      <View className="my-auto flex-row items-center">
        <RegularAvatar
          className="mr-2"
          avatar={image || require('~/assets/images/avatar.png')}
        />
        <View className="gap-1">
          <MdBoldLabel>{name}</MdBoldLabel>
          <Small className="text-grey-60 dark:text-grey-50">@{username}</Small>
        </View>
      </View>
      <Button
        label={favourited ? 'Remove' : 'Favourite'}
        variant={favourited ? 'outline' : 'default'}
        size="sm"
        onPress={toggleFavourite}
        className="px-5"
      />
    </View>
  );
}
