import React from 'react';

import {
  AnimatedBorder,
  Image,
  MdBold<PERSON>abel,
  Small,
  TouchableOpacity,
  View as UIView,
  WIDTH,
} from '@/components/ui';
import { cn } from '@/lib';

interface GradientBorderCardProps {
  title: string;
  description?: string;
  icon: string;
  isSelected: boolean;
  onPress?: () => void;
}

export function GradientBorderCard({
  title,
  description,
  onPress,
  icon,
  isSelected,
}: GradientBorderCardProps) {
  return (
    <UIView className="relative">
      <TouchableOpacity
        onPress={onPress}
        className={cn(
          'relative w-full px-4 py-3 rounded-lg h-[92px] justify-center',
          isSelected
            ? 'bg-white dark:bg-grey-100 border border-border-subtle-light dark:border-border-subtle-dark'
            : 'bg-transparent'
        )}
      >
        {/* Animated Skia Border - only renders when selected */}
        {isSelected && (
          <AnimatedBorder
            width={WIDTH - 32}
            height={92}
            isSelected={isSelected}
            borderRadius={16}
            borderWidth={1.5}
            blurRadius={0}
          />
        )}

        {/* Content */}
        <CardContent icon={icon} title={title} description={description} />
      </TouchableOpacity>
    </UIView>
  );
}

interface CardContentProps {
  title: string;
  description?: string;
  icon: string;
}

const CardContent = ({ icon, title, description }: CardContentProps) => (
  <UIView className="relative z-10 flex w-full flex-row gap-4">
    <Image className="my-auto size-10" source={icon} contentFit="cover" />
    {description && (
      <UIView className="w-full max-w-[273px] gap-2">
        <MdBoldLabel>{title}</MdBoldLabel>
        <Small className="text-grey-60 dark:text-grey-50">{description}</Small>
      </UIView>
    )}
    {!description && <MdBoldLabel className="my-auto">{title}</MdBoldLabel>}
  </UIView>
);
