import { H6, Image, TouchableOpacity, View } from '@/components/ui';
import { cn } from '@/lib';

interface CompleteCreatorOnboardingCardProps {
  title: string;
  imageSource: string;
  onPress?: () => void;
  className?: string;
}

export function HomeCompletionCard({
  title,
  onPress,
  imageSource,
  className,
}: CompleteCreatorOnboardingCardProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className={cn(
        'relative h-[100px] w-[136px] items-center justify-center gap-y-2 rounded-lg bg-bg-subtle-light p-2 dark:bg-bg-subtle-dark',
        className
      )}
    >
      <Image
        className="size-12 self-center"
        source={imageSource}
        contentFit="cover"
      />

      {/* Full width so title aligns left */}
      <View className="w-full items-start">
        <H6>{title}</H6>
      </View>
    </TouchableOpacity>
  );
}
