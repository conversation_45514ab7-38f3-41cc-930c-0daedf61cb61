import React from 'react';

import { Image, Tiny, View } from '@/components/ui';
import { cn } from '@/lib';

import { LiveUserAvatar } from '../avatars/live-avatar';

interface LiveUserCardProps {
  username: string;
  avatar: string;
  status: string;
  bgImage: string;
  className?: string;
  onPress?: () => void;
}

export const LiveUserCard: React.FC<LiveUserCardProps> = ({
  username,
  avatar,
  status,
  bgImage,
  className,
  onPress,
}) => {
  return (
    <View
      className={cn(
        'relative h-[200px] w-[150px] items-center rounded-lg',
        className
      )}
    >
      <Image className="size-full" source={bgImage} contentFit="cover" />
      <View className="absolute top-2 w-full gap-8">
        <View className="ml-auto mr-2 w-10 flex-row items-center justify-center gap-0.5 rounded-md bg-red-50 px-1 py-0.5">
          <View className="size-1 rounded-full bg-white" />
          <Tiny className="text-[10px] text-xs/100 font-bold text-white">
            {status}
          </Tiny>
        </View>

        <LiveUserAvatar
          username={username}
          avatar={avatar || require('~/assets/images/avatar.png')}
          hideStatusPill
          textClassName="text-white"
          onPress={onPress}
        />
      </View>
    </View>
  );
};
