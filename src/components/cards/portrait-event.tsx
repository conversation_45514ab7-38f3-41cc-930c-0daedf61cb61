import { Feather, FontAwesome, Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { Link } from 'expo-router';
import moment from 'moment';
import React from 'react';
import { StyleSheet } from 'react-native';

import { type IEvent } from '@/api/events';
import {
  colors,
  H4,
  Image,
  TouchableOpacity,
  View,
  XsRegularLabel,
} from '@/components/ui';
import { cn, useFavoriteToggle } from '@/lib';

interface PortraitEventCardProps extends IEvent {
  isEven: boolean;
  hasPhysicalLocation: boolean;
}
export const PortraitEventCard: React.FC<PortraitEventCardProps> = ({
  bannerUrl,
  title,
  startTime,
  location,
  isEven,
  id,
  slug,
  isfavourite,
  hasPhysicalLocation,
  onlineEventUrl,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const locationText = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  return (
    <Link
      href={{
        pathname: '/events/[id]',
        params: {
          id,
          slug,
        },
      }}
      asChild
    >
      <TouchableOpacity
        className={cn(
          'relative h-[300px] flex-1 rounded-lg',
          isEven ? 'mr-2' : 'ml-2'
        )}
      >
        <Image
          className="size-full rounded-lg"
          source={bannerUrl}
          contentFit="cover"
          priority="high"
        />
        <View className="absolute size-full items-end justify-between px-1 pb-1.5 pt-2.5">
          <TouchableOpacity
            className="mr-2.5 size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90"
            onPress={handleFavToggle}
          >
            <FontAwesome
              name={favourited ? 'heart' : 'heart-o'}
              size={16}
              color={favourited ? colors.red[50] : colors.brand[40]}
            />
          </TouchableOpacity>

          <View className="h-[123px] w-full gap-4 rounded-lg bg-[#FFFFFF05] px-2 py-4">
            <BlurView
              tint="systemThickMaterialDark"
              intensity={50}
              style={styles.blurView}
            >
              <H4 className="text-white dark:text-white">{title}</H4>
              <View className="gap-2">
                <View className="h-6 flex-row items-center gap-0.5">
                  <Feather name="calendar" size={16} color="white" />
                  <XsRegularLabel className="text-white">
                    {moment.utc(startTime).format('D MMM YYYY [at] HH:mm')}
                  </XsRegularLabel>
                </View>

                <View className="h-6 flex-row items-center gap-0.5">
                  <Ionicons name="location-outline" size={16} color="white" />
                  <XsRegularLabel className="text-white">
                    {locationText}
                  </XsRegularLabel>
                </View>
              </View>
            </BlurView>
          </View>
        </View>
      </TouchableOpacity>
    </Link>
  );
};

const styles = StyleSheet.create({
  blurView: {
    ...StyleSheet.absoluteFillObject,
    overflow: 'hidden',
    backgroundColor: 'transparent',
    flexDirection: 'column',
    justifyContent: 'space-between',
    borderRadius: 16,
    padding: 16,
  },
});
