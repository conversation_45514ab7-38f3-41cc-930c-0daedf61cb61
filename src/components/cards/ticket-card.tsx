import { Feather } from '@expo/vector-icons';
import React from 'react';
import { Pressable } from 'react-native';

import {
  colors,
  Text,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { Image } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { formatCurrency } from '@/lib/utils/formatCurrency';

type TicketCardProps = {
  ticket: CreateEventFormType['tickets'][number];
  fieldId: string | number;
  isMenuOpen: boolean;
  setMenuVisible: (id: string | number | null) => void;
  onEdit: () => void;
  onDelete?: () => void;
  isDark: boolean;
};

function PresaleBadge() {
  return (
    <View className="absolute right-2 top-1 z-10 h-5 w-[84px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
      <XsBoldLabel className="text-xs text-green-60 dark:text-green-40">
        Presale incl.
      </XsBoldLabel>
    </View>
  );
}

export function TicketCard({
  ticket,
  fieldId,
  isMenuOpen,
  setMenuVisible,
  onEdit,
  onDelete,
  isDark,
}: TicketCardProps) {
  return (
    <View className="relative h-[98px] flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
      <View className="flex-1 flex-row items-center gap-2">
        <Image
          source={require('~/assets/images/session/disc.png')}
          className="size-10"
          alt="Ticket"
        />
        <View className="gap-2">
          <Text className="font-bold text-fg-base-light dark:text-fg-base-dark">
            {ticket.name}
          </Text>
          <Text className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {formatCurrency(ticket.price)}
          </Text>
          {ticket.description && (
            <Text className="text-fg-subtle-light dark:text-fg-subtle-dark">
              {ticket.description.length > 30
                ? ticket.description.slice(0, 30) + '...'
                : ticket.description}
            </Text>
          )}
        </View>
      </View>
      {ticket.hasPresale && <PresaleBadge />}
      <View className="relative flex-row items-center gap-2">
        <TouchableOpacity
          onPress={() => setMenuVisible(fieldId)}
          accessibilityLabel="Open ticket options"
        >
          <Feather
            name="more-horizontal"
            size={24}
            color={isDark ? colors.white : colors.grey[100]}
          />
        </TouchableOpacity>

        {isMenuOpen && (
          <>
            <Pressable
              className="absolute inset-0 z-[9998]"
              onPress={() => setMenuVisible(null)}
            />

            <View className="absolute right-0 top-8 z-[9999] w-[120px] rounded-md bg-bg-interactive-secondary-light p-2 shadow-md dark:bg-bg-interactive-secondary-dark">
              <TouchableOpacity
                onPress={onEdit}
                className="py-2"
                accessibilityLabel="Edit ticket"
              >
                <Text className="text-fg-base-light dark:text-fg-base-light">
                  Edit ticket
                </Text>
              </TouchableOpacity>
              {onDelete && (
                <TouchableOpacity
                  onPress={onDelete}
                  className="py-2"
                  accessibilityLabel="Delete ticket"
                >
                  <Text className="text-fg-danger-light dark:text-fg-danger-light">
                    Delete ticket
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </>
        )}
      </View>
    </View>
  );
}
