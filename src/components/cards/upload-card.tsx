import React from 'react';
import { StyleSheet } from 'react-native';

import {
  Image,
  semanticColors,
  Text,
  TouchableOpacity,
  View,
} from '@/components/ui';

interface UploadCardProps {
  onPress: () => void;
  icon: any;
  title: string;
}

export const UploadCard: React.FC<UploadCardProps> = ({
  onPress,
  icon,
  title,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="h-[360px] w-full items-center justify-center p-6"
      style={styles.container}
    >
      <View className="items-center justify-center gap-4">
        <Image source={icon} className="size-16" contentFit="contain" />

        <Text className="text-center text-accent-on-accent dark:text-accent-on-accent">
          {title}
        </Text>

        <View className="flex-row flex-wrap justify-center">
          <Text className="mx-1 text-xs text-fg-muted-light dark:text-fg-muted-dark">
            {'Supported Format: JPG, PNG (Max 10mb)'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 2,
    borderColor: semanticColors.border.subtle.dark,
    borderStyle: 'dashed',
    borderRadius: 12,
    backgroundColor: semanticColors.bg.subtle.dark,
  },
});
