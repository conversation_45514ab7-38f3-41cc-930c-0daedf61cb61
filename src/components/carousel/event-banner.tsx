import { AntDesign } from '@expo/vector-icons';
import React, { useRef, useState } from 'react';
import {
  type NativeScrollEvent,
  type NativeSyntheticEvent,
} from 'react-native';

import { type IEventCategories, type ISingleEvent } from '@/api/events';

import {
  colors,
  Image,
  MdBoldLabel,
  ScrollView,
  TouchableOpacity,
  View,
  WIDTH,
} from '../ui';

interface Props {
  event: ISingleEvent;
  eventCategory: IEventCategories;
  isCreator: boolean;
  favourited: boolean;
  handleFavToggle?: () => void;
}
const EventBannerCarousel: React.FC<Props> = ({
  event,
  eventCategory,
  isCreator,
  favourited,
  handleFavToggle,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const mediaItems = [
    {
      id: 'banner',
      url:
        event?.bannerUrl ||
        require('~/assets/gradient-bg/landscape-event-bg.png'),
      type: 'banner',
    },
    ...(event?.media || []).map((item, index) => ({
      id: `media-${index}`,
      url: item || require('~/assets/gradient-bg/landscape-event-bg.png'),
      type: 'media',
    })),
  ];

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / WIDTH);
    setCurrentIndex(index);
  };

  const scrollToIndex = (index: number) => {
    (scrollViewRef.current as any)?.scrollTo({
      x: index * WIDTH,
      animated: true,
    });
    setCurrentIndex(index);
  };

  if (mediaItems.length <= 1) {
    return (
      <View className="relative min-h-[450px] w-full flex-1">
        <Image
          source={mediaItems[0].url}
          className="size-full"
          contentFit="contain"
          priority="high"
        />
        {!isCreator && (
          <View className="absolute w-full flex-row justify-between py-4 pl-4 pr-2">
            {eventCategory && (
              <View className="h-8 items-center justify-center rounded-xl bg-blue-10 px-4 dark:bg-blue-80">
                <MdBoldLabel className="text-blue-60 dark:text-blue-40">
                  {eventCategory.category}
                </MdBoldLabel>
              </View>
            )}
            <TouchableOpacity
              className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90"
              onPress={handleFavToggle}
            >
              <AntDesign
                name={favourited ? 'heart' : 'hearto'}
                size={16}
                color={favourited ? colors.red[50] : colors.brand[40]}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  }

  return (
    <View className="relative min-h-[450px] w-full flex-1">
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        className="flex-1"
      >
        {mediaItems.map((item) => (
          <View key={item.id} style={{ width: WIDTH }}>
            <Image
              source={item.url}
              className="size-full"
              contentFit="contain"
              priority="high"
            />
          </View>
        ))}
      </ScrollView>

      {/* Top overlay with category and favorite button */}
      {!isCreator && (
        <View className="absolute w-full flex-row justify-between py-4 pl-4 pr-2">
          {eventCategory && (
            <View className="h-8 items-center justify-center rounded-xl bg-blue-10 px-4 dark:bg-blue-80">
              <MdBoldLabel className="text-blue-60 dark:text-blue-40">
                {eventCategory.category}
              </MdBoldLabel>
            </View>
          )}
          <TouchableOpacity
            className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90"
            onPress={handleFavToggle}
          >
            <AntDesign
              name={favourited ? 'heart' : 'hearto'}
              size={16}
              color={favourited ? colors.red[50] : colors.brand[40]}
            />
          </TouchableOpacity>
        </View>
      )}

      {/* Pagination dots */}
      <View className="absolute inset-x-0 bottom-4 flex-row justify-center gap-x-2">
        {mediaItems.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => scrollToIndex(index)}
            className={`size-2 rounded-full ${
              index === currentIndex ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </View>
    </View>
  );
};

export default EventBannerCarousel;
