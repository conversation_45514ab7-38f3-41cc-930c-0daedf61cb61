import React, { useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  runOnJS,
  useAnimatedRef,
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';

import { type IEvent } from '@/api/events/types';
import { CAROUSEL_INTERVAL } from '@/lib';

import { EventCardWithAction } from '../cards/event-with-action';
import DotStepIndicator from '../pagination/dot-step-indicator';
import { View, WIDTH } from '../ui';

interface EventCarouselProps {
  events: IEvent[];
}
const EventCarousel: React.FC<EventCarouselProps> = ({ events }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useAnimatedRef<Animated.FlatList<Event>>();
  const scrollX = useSharedValue(0);
  const intervalRef = useRef<number | null>(null);

  // Animated scroll handler to track scroll position
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollX.value = event.contentOffset.x;
      const index = Math.round(event.contentOffset.x / WIDTH);
      if (index !== activeIndex) {
        runOnJS(setActiveIndex)(index);
      }
    },
  });

  // Function to move to the next slide
  const scrollToNextSlide = useCallback(() => {
    const nextIndex = (activeIndex + 1) % events.length;
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: nextIndex * WIDTH,
        animated: true,
      });
    }
  }, [activeIndex, events.length, flatListRef]);

  // Set up auto-play
  useEffect(() => {
    // Start auto-play
    intervalRef.current = setInterval(() => {
      scrollToNextSlide();
    }, CAROUSEL_INTERVAL);

    // Clean up interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [activeIndex, events.length, scrollToNextSlide]);

  // Render each event item
  const renderItem = ({ item }: { item: IEvent }) => (
    <View style={styles.itemContainer}>
      <EventCardWithAction
        {...item}
        attendees={item.ticketsSold}
        startTime={item.startTime}
        bannerUrl={item.bannerUrl}
        title={item.title}
        id={item.id || ''}
        location={item.location}
      />
    </View>
  );

  return (
    <View className="items-center gap-2 py-2">
      <Animated.FlatList
        ref={flatListRef as any}
        data={events}
        renderItem={renderItem}
        keyExtractor={(_, index) => `event-${index}`}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        snapToInterval={WIDTH}
        decelerationRate="fast"
        style={styles.carousel}
      />
      <DotStepIndicator
        activeStep={activeIndex + 1}
        totalSteps={events.length}
        activeStepClassName="w-8"
        hasOneActiveStep
      />
    </View>
  );
};

export default EventCarousel;

const styles = StyleSheet.create({
  carousel: {
    width: WIDTH,
  },
  itemContainer: {
    width: WIDTH - 32, // Account for parent padding
    marginHorizontal: 16,
    overflow: 'hidden',
  },
});
