import { type FlashList } from '@shopify/flash-list';
import * as React from 'react';
import {
  type NativeScrollEvent,
  type NativeSyntheticEvent,
} from 'react-native';
import type ViewShot from 'react-native-view-shot';

import { type ITicketExtension } from '@/api/events';
import { List, View, WIDTH } from '@/components/ui';

import { PaginationDots } from '../common/pagination-dots';
import { TicketCard } from '../tickets/card';

type TicketCarouselProps = {
  tickets: ITicketExtension[];
  onTicketChange: (index: number) => void;
  currentIndex: number;
  viewShotRefs: React.RefObject<ViewShot>[];
};

export function TicketCarousel({
  tickets,
  onTicketChange,
  currentIndex,
  viewShotRefs,
}: TicketCarouselProps) {
  const flatListRef = React.useRef<FlashList<ITicketExtension>>(null);

  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const slideSize = WIDTH;
    const index = Math.round(event.nativeEvent.contentOffset.x / slideSize);
    if (index !== currentIndex && index >= 0 && index < tickets.length) {
      onTicketChange(index);
    }
  };

  const renderTicketItem = ({
    item,
    index,
  }: {
    item: ITicketExtension;
    index: number;
  }) => (
    <View className="mx-8 rounded-lg">
      <TicketCard ref={viewShotRefs[index]} ticket={item} />
    </View>
  );

  if (tickets.length === 1) {
    return (
      <View className="mx-8 rounded-lg">
        <TicketCard ref={viewShotRefs[0]} ticket={tickets[0]} />
      </View>
    );
  }

  return (
    <View>
      <List
        ref={flatListRef}
        data={tickets}
        renderItem={renderTicketItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        estimatedItemSize={341}
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
        snapToInterval={WIDTH}
        snapToAlignment="center"
        decelerationRate="fast"
      />
      <PaginationDots total={tickets.length} current={currentIndex} />
    </View>
  );
}
