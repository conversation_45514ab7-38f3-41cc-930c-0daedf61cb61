import React from 'react';
import { StyleSheet } from 'react-native';
import { GestureDetector } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';

import ConfirmationDialog from '@/components/dialogs/confirmation';
import { View } from '@/components/ui';
import { useChatActions } from '@/lib';
import { useSwipeGesture } from '@/lib/hooks/use-swipe-gesture';
import type { ChatListType } from '@/types';

import { ChatContent } from './item';
import { SwipeActions } from './item-actions';

interface ChatListItemProps {
  chat: ChatListType;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
export const ChatListItem: React.FC<ChatListItemProps> = ({
  chat,
  setLoading,
}) => {
  const isChatUnread = Number(chat.unreadCount.toString()) > 0;

  const {
    favourited,
    toggleFavourite,
    hasBlockedOtherUser,
    confirmVisible,
    setConfirmVisible,
    deleteVisible,
    setDeleteVisible,
    chatRoomId,
    onReport,
    handleBlockUser,
    handleUnblockUser,
    deleteChat,
  } = useChatActions(chat, setLoading);

  const {
    translateX,
    ACTION_WIDTH,
    composedGesture,
    handleBlockPress,
    handleDeletePress,
    handleFavoritePress,
    handleReportPress,
  } = useSwipeGesture({
    onReport,
    onFavorite: toggleFavourite,
    onBlock: () => setConfirmVisible(true),
    onDelete: () => setDeleteVisible(true),
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  return (
    <View className="relative">
      <SwipeActions
        translateX={translateX}
        ACTION_WIDTH={ACTION_WIDTH}
        favourited={favourited}
        hasBlockedOtherUser={hasBlockedOtherUser}
        onFavoritePress={handleFavoritePress}
        onBlockPress={handleBlockPress}
        onReportPress={handleReportPress}
        onDeletePress={handleDeletePress}
      />

      <GestureDetector gesture={composedGesture}>
        <Animated.View style={[styles.chatItem, animatedStyle]}>
          <ChatContent
            chat={chat}
            chatRoomId={chatRoomId}
            isChatUnread={isChatUnread}
          />
        </Animated.View>
      </GestureDetector>
      <ConfirmationDialog
        visible={deleteVisible}
        message="Are you sure you want to delete this chat"
        onCancel={() => setDeleteVisible(false)}
        onConfirm={async () => {
          await deleteChat();
          setDeleteVisible(false);
        }}
      />

      <ConfirmationDialog
        visible={confirmVisible}
        message={`Are you sure you want to ${hasBlockedOtherUser ? 'unblock' : 'block'} this account?`}
        onCancel={() => setConfirmVisible(false)}
        onConfirm={() => {
          if (hasBlockedOtherUser) {
            handleUnblockUser();
          } else {
            handleBlockUser();
          }
          setConfirmVisible(false);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  chatItem: {
    zIndex: 2,
  },
});
