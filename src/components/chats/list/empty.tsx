import { Ionicons } from '@expo/vector-icons';
import React from 'react';

import { H3, P, View } from '@/components/ui';

interface EmptyStateProps {
  type: 'chats' | 'users' | 'recent-users';
}

export const EmptyState: React.FC<EmptyStateProps> = ({ type }) => {
  const content: any = {
    chats: {
      icon: 'chatbubbles-outline',
      title: 'No Conversations Yet',
      message: 'Start a new chat by tapping the button below',
    },
    users: {
      icon: 'search-outline',
      title: 'No Users Found',
      message: 'Try searching with a different name',
    },
    'recent-users': {
      icon: 'search-outline',
      title: 'No Recent Searches',
    },
  };

  return (
    <View className="justify-center, mt-12 flex-1 items-center p-6">
      <Ionicons
        name={content[type].icon as any}
        size={64}
        color="#8E8E93"
        className="mb-4"
      />
      <View className="gap-2">
        <H3 className="text-center">{content[type].title}</H3>
        {!!content[type].message && (
          <P className="text-center">{content[type].message}</P>
        )}
      </View>
    </View>
  );
};
