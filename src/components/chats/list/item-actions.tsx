import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  interpolate,
  type SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';

import {
  colors,
  HeartFilledIcon,
  HeartIcon,
  P,
  TouchableOpacity,
} from '@/components/ui';
import { ACTION_WIDTH } from '@/lib';

interface SwipeActionsProps {
  translateX: SharedValue<number>;
  ACTION_WIDTH: number;
  favourited: boolean;
  hasBlockedOtherUser: boolean;
  onFavoritePress: () => void;
  onBlockPress: () => void;
  onReportPress: () => void;
  onDeletePress: () => void;
}

export const SwipeActions: React.FC<SwipeActionsProps> = ({
  translateX,
  ACTION_WIDTH,
  favourited,
  hasBlockedOtherUser,
  onFavoritePress,
  onBlockPress,
  onReportPress,
  onDeletePress,
}) => {
  const leftActionsStyle = useAnimatedStyle(() => {
    const progress = interpolate(
      translateX.value,
      [0, ACTION_WIDTH * 2],
      [0, 1],
      'clamp'
    );
    return {
      opacity: progress,
      transform: [
        {
          translateX: interpolate(
            translateX.value,
            [0, ACTION_WIDTH * 2],
            [-ACTION_WIDTH * 2, 0],
            'clamp'
          ),
        },
        {
          scale: interpolate(
            translateX.value,
            [0, ACTION_WIDTH * 2],
            [0.7, 1],
            'clamp'
          ),
        },
      ],
    };
  });

  const rightActionsStyle = useAnimatedStyle(() => {
    const progress = interpolate(
      translateX.value,
      [-ACTION_WIDTH * 2, 0],
      [1, 0],
      'clamp'
    );
    return {
      opacity: progress,
      transform: [
        {
          translateX: interpolate(
            translateX.value,
            [-ACTION_WIDTH * 2, 0],
            [0, ACTION_WIDTH * 2],
            'clamp'
          ),
        },
        {
          scale: interpolate(
            translateX.value,
            [-ACTION_WIDTH * 2, 0],
            [1, 0.7],
            'clamp'
          ),
        },
      ],
    };
  });

  return (
    <>
      {/* Left Actions */}
      <Animated.View style={[styles.leftActions, leftActionsStyle]}>
        <TouchableOpacity
          style={styles.actionButton}
          className="bg-brand-60"
          onPress={onFavoritePress}
        >
          {favourited ? (
            <HeartFilledIcon color={colors.red[50]} />
          ) : (
            <HeartIcon color={colors.white} />
          )}
          <P style={styles.actionText}>
            {favourited ? 'Unfavourite' : 'Favourite'}
          </P>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          className="bg-bg-warning-contrast"
          onPress={onBlockPress}
        >
          <Ionicons name="shield-outline" size={20} color="white" />
          <P style={styles.actionText}>
            {hasBlockedOtherUser ? 'Unblock' : 'Block'}
          </P>
        </TouchableOpacity>
      </Animated.View>

      {/* Right Actions */}
      <Animated.View style={[styles.rightActions, rightActionsStyle]}>
        <TouchableOpacity
          style={styles.actionButton}
          className="bg-bg-danger-primary"
          onPress={onReportPress}
        >
          <Ionicons name="flag-outline" size={20} color="white" />
          <P style={styles.actionText}>Report</P>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          className="bg-bg-danger-secondary"
          onPress={onDeletePress}
        >
          <Ionicons name="trash-outline" size={20} color={colors.red[20]} />
          <P style={styles.actionText}>Delete</P>
        </TouchableOpacity>
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  leftActions: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  rightActions: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  actionButton: {
    width: ACTION_WIDTH,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  actionText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});
