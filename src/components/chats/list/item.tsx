import { Link } from 'expo-router';
import React from 'react';

import {
  Image,
  Small,
  SmBoldLabel,
  Tiny,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { cn, formatLastSeen, getTimeValue } from '@/lib';
import type { ChatListType } from '@/types';
import { isValid } from 'date-fns';

interface ChatContentProps {
  chat: ChatListType;
  chatRoomId: string;
  isChatUnread: boolean;
}

export const ChatContent: React.FC<ChatContentProps> = ({
  chat,
  chatRoomId,
  isChatUnread,
}) => (
  <Link
    href={{
      pathname: '/chats/[chatRoomId]',
      params: {
        chatRoomId,
        username: chat.userName,
        userId: chat.userId,
        userAvatar: chat.userAvatar,
      },
    }}
    asChild
  >
    <TouchableOpacity className="h-[88px] flex-row items-center gap-x-3.5 border-b border-bg-subtle-light px-4 dark:border-bg-subtle-dark">
      <View className="relative">
        <Image
          className="size-16 rounded-full"
          source={chat.userAvatar || require('~/assets/images/avatar.png')}
          priority="high"
        />
        {!chat?.hideOnlineStatus && chat.status === 'online' && (
          <View className="absolute bottom-0 right-0 size-3.5 rounded-full border-2 border-white bg-green-60 dark:border-grey-100 dark:bg-green-50" />
        )}
      </View>

      <View className="flex-1 gap-1">
        <SmBoldLabel>{chat.userName}</SmBoldLabel>
        <Small
          numberOfLines={1}
          className={cn(
            'max-w-48 ',
            chat.isTyping && 'italic',
            !isChatUnread && 'text-grey-50 dark:text-grey-60'
          )}
        >
          {chat.isTyping ? 'Typing...' : chat.lastMessage || 'No messages yet'}
        </Small>
      </View>

      <View className="items-end gap-2">
        <XsBoldLabel
          className={cn(
            isChatUnread
              ? 'text-brand-60 dark:text-brand-60'
              : 'text-grey-50 dark:text-grey-60'
          )}
        >
          {chat.lastMessageTime &&
          typeof (chat.lastMessageTime as any).toDate === 'function'
            ? formatLastSeen((chat.lastMessageTime as any).toDate())
            : ''}
        </XsBoldLabel>
        {isChatUnread && (
          <View className="size-5 items-center justify-center rounded-full bg-profile-ring-start">
            <Tiny className="text-gray-50">
              {Number(chat.unreadCount.toString()) > 99
                ? '99+'
                : chat.unreadCount.toString()}
            </Tiny>
          </View>
        )}
      </View>
    </TouchableOpacity>
  </Link>
);
