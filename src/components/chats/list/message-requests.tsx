/* eslint-disable react-hooks/exhaustive-deps */
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useEffect, useState } from 'react';
import { Animated, FlatList, StyleSheet } from 'react-native';

import {
  colors,
  Image,
  P,
  Small,
  Text,
  Tiny,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { useLoggedInUser } from '@/lib';
import { type MessageRequest } from '@/types';

interface MessageRequestsSectionProps {
  requests: MessageRequest[];
  onAccept: (requestId: string) => void;
  onDecline: (requestId: string) => void;
}

export const MessageRequestsSection: React.FC<MessageRequestsSectionProps> = ({
  requests,
  onAccept,
  onDecline,
}) => {
  const router = useRouter();
  const { data: user } = useLoggedInUser();
  const [expanded, setExpanded] = useState(false);
  const [animation] = useState(new Animated.Value(0));

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  useEffect(() => {
    // Auto-expand if there are new requests
    if (requests.length > 0 && !expanded) {
      setExpanded(true);
    }
  }, [requests.length]);

  useEffect(() => {
    Animated.timing(animation, {
      toValue: expanded ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [expanded]);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const handleViewRequest = (request: MessageRequest) => {
    router.push({
      pathname: '/chats/[chatRoomId]',
      params: {
        chatRoomId: [user?.id, request.senderId].sort().join('_'),
        userId: request.senderId,
        username: request.senderName,
        userAvatar: request.senderAvatar,
      },
    });
  };

  const renderRequestItem = ({ item }: { item: MessageRequest }) => (
    <Animated.View style={styles.requestItem}>
      <TouchableOpacity
        className="flex-1 flex-row items-center"
        onPress={() => handleViewRequest(item)}
      >
        <Image
          source={item.senderAvatar || require('~/assets/images/avatar.png')}
          className="mr-3 size-16 rounded-full"
          priority="high"
        />
        <View className="flex-1">
          <P className="mb-1">{item.senderName}</P>
          <Small
            className="text-fg-subtle-light dark:text-fg-subtle-dark"
            numberOfLines={2}
          >
            {item.message}
          </Small>
          <Tiny className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {item.createdAt?.toDate
              ? new Date(item.createdAt.toDate()).toLocaleDateString()
              : ''}
          </Tiny>
        </View>
      </TouchableOpacity>
      <View className="ml-2 flex-row">
        <TouchableOpacity
          className="ml-2 size-8 items-center justify-center rounded-full bg-[#FFEBEE]"
          onPress={() => onDecline(item.id)}
        >
          <Ionicons name="close" size={20} color="#FF3B30" />
        </TouchableOpacity>
        <TouchableOpacity
          className="ml-2 size-8 items-center justify-center rounded-full bg-[#E8F5E9]"
          onPress={() => onAccept(item.id)}
        >
          <Ionicons name="checkmark" size={20} color="#4CAF50" />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  const maxHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, requests.length * 100], // Approximate height per item
  });

  if (requests.length === 0) {
    return null;
  }

  return (
    <View className="mx-3 my-2 overflow-hidden rounded-[10px] border border-grey-20 bg-white dark:border-grey-80 dark:bg-grey-100">
      <TouchableOpacity
        className="flex-row items-center justify-between bg-white p-3 dark:bg-grey-100"
        style={styles.header}
        onPress={toggleExpanded}
      >
        <View className="flex-row items-center">
          <Text className="text-base font-semibold text-grey-100 dark:text-white">
            Message Requests
          </Text>
          <View className="ml-2 h-5 min-w-5 items-center justify-center rounded-[10px] bg-brand-60 px-1.5">
            <Tiny>{requests.length}</Tiny>
          </View>
        </View>
        <Ionicons
          name={expanded ? 'chevron-up' : 'chevron-down'}
          size={20}
          color="#8E8E93"
        />
      </TouchableOpacity>

      <Animated.View style={{ maxHeight, overflow: 'hidden' }}>
        <FlatList
          data={requests}
          renderItem={renderRequestItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={true}
          style={styles.requestsList}
          contentContainerStyle={styles.requestsListContent}
        />
      </Animated.View>
    </View>
  );
};

const styling = (isDark: boolean) =>
  StyleSheet.create({
    header: {
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: isDark ? colors.grey[70] : colors.grey[30],
    },
    requestsList: {
      backgroundColor: isDark ? colors.grey[100] : colors.white,
    },
    requestItem: {
      flexDirection: 'row',
      padding: 12,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    requestsListContent: {
      paddingBottom: 8,
    },
  });
