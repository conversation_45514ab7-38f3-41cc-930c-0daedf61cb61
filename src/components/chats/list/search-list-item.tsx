import { useRouter } from 'expo-router';
import React from 'react';

import { createOrUpdateFirebaseUser } from '@/api';
import {
  Image,
  Small,
  SmBoldLabel,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { formatLastSeen, useLoggedInUser } from '@/lib';
import type { UserType } from '@/types';

interface SearchListItemProps {
  user: UserType;
  selectUser?: (user: UserType) => void;
}
export const SearchListItem: React.FC<SearchListItemProps> = ({
  user,
  selectUser,
}) => {
  const router = useRouter();
  const { data: currentUser } = useLoggedInUser();

  const chatRoomId = React.useMemo(
    () => [currentUser?.id, user.id].sort().join('_'),
    [currentUser?.id, user.id]
  );

  const goToChat = async () => {
    if (!user.isInFirestore) {
      await createOrUpdateFirebaseUser(
        {
          id: user.id,
          username: user.displayName,
          profileImageUrl: user.photoURL || '',
        },
        'offline'
      );
    }
    selectUser?.(user);
    router.push({
      pathname: '/chats/[chatRoomId]',
      params: {
        chatRoomId,
        username: user.displayName,
        userId: user.id,
        userAvatar: user.photoURL,
      },
    });
  };

  return (
    <TouchableOpacity
      className="h-[62px] flex-row items-center gap-x-3.5"
      onPress={goToChat}
    >
      <View className="relative">
        <Image
          className="size-10 rounded-full"
          source={user.photoURL || require('~/assets/images/avatar.png')}
          priority="high"
        />
        {!user.hideOnlineStatus && user.status === 'online' && (
          <View className="absolute bottom-0 right-0 size-3.5 rounded-full border-2 border-white bg-green-60 dark:border-grey-100 dark:bg-green-50" />
        )}
      </View>
      <View className="flex-1 gap-1">
        <SmBoldLabel>{user.displayName}</SmBoldLabel>
        {(!user.hideLastSeen ||
          (!user.hideOnlineStatus && user.status === 'online')) && (
          <Small className="text-grey-50 dark:text-grey-60">
            {!user.hideOnlineStatus && user.status === 'online'
              ? 'Online'
              : user.lastSeen &&
                  typeof (user.lastSeen as any).toDate === 'function'
                ? `Last seen ${formatLastSeen(user.lastSeen?.toDate())}`
                : 'Offline'}
          </Small>
        )}
      </View>
    </TouchableOpacity>
  );
};
