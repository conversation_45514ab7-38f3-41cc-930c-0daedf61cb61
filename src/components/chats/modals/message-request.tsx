import React from 'react';
import { Modal, View } from 'react-native';

import { Button, H3, Image, Input, P, Small } from '@/components/ui';

interface MessageRequestProps {
  userName: string;
  userAvatar: string;
  onSend: () => void;
  onAccept: () => void;
  onDecline: () => void;
  isReceived: boolean;
  message: string;
  setMessage: (text: string) => void;
  loading?: boolean;
}

export default function MessageRequestModal({
  userName,
  userAvatar,
  onSend,
  onAccept,
  onDecline,
  isReceived,
  message,
  setMessage,
  loading,
}: MessageRequestProps) {
  return (
    <Modal visible={true} transparent={true} animationType="slide">
      <View className="flex-1 items-center justify-center bg-black/50">
        <View className="w-[85%] items-center rounded-[20px] bg-white p-6 shadow-md shadow-black dark:bg-grey-80">
          <Image
            source={userAvatar || require('~/assets/images/avatar.png')}
            className="mb-4 size-20 rounded-full"
            priority="high"
          />

          {isReceived ? (
            <>
              <H3 className="mb-3 text-center">
                {userName} wants to chat with you
              </H3>
              <P className="mb-3">{message || 'hi there'}</P>

              <View className="w-full flex-row items-center justify-center gap-5">
                <Button
                  className="flex-1"
                  onPress={onDecline}
                  label="Decline"
                  variant="destructive"
                />
                <Button className="flex-1" onPress={onAccept} label="Accept" />
              </View>
            </>
          ) : (
            <>
              <H3 className="mb-3 text-center">
                Send message request to {userName}
              </H3>
              <Small className="mb-3 text-center">
                This user will need to accept your request before you can start
                chatting
              </Small>

              <View className="w-full">
                <Input
                  placeholder="Type your introduction message..."
                  label="Message"
                  value={message}
                  onChangeText={setMessage}
                  multiline
                  maxLength={200}
                />
              </View>

              <View className="mt-4 w-full">
                <Button
                  disabled={!message.trim()}
                  onPress={onSend}
                  loading={loading}
                  label="Send Request"
                />
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}
