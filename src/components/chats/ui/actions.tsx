import { Ionicons } from '@expo/vector-icons';
import { StyleSheet } from 'react-native';
import { Actions, type ActionsProps } from 'react-native-gifted-chat';

import { View } from '@/components/ui';

export default function CustomActions(props: ActionsProps) {
  return (
    <Actions
      {...props}
      containerStyle={styles.container}
      icon={() => (
        <View className="size-8 items-center justify-center rounded-full bg-brand-60">
          <Ionicons name="add" size={16} color="white" />
        </View>
      )}
      options={{
        'Choose From Library': () => {
          console.log('Choose From Library');
        },
        'Take Photo': () => {
          console.log('Take Photo');
        },
        Cancel: () => {
          console.log('Cancel');
        },
      }}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    marginBottom: 0,
  },
});
