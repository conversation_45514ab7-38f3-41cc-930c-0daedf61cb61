import * as Clipboard from 'expo-clipboard';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { Keyboard, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { type Swipeable } from 'react-native-gesture-handler';
import { Day, GiftedChat } from 'react-native-gifted-chat';
import type { AnimatedList } from 'react-native-gifted-chat/lib/MessageContainer';

import { semanticColors, Tiny, View } from '@/components/ui';
import { useLoggedInUser } from '@/lib';
import type { MessageType } from '@/types';

import CustomInputToolbar from './input-toolbar';
import ChatMessageBox from './message-box';
import CustomMessageBubble from './message-bubble';
import CustomComposer from './message-composer';
import ReplyMessageBar from './reply-message-bar';
import CustomSend from './send';
import CustomSystemMessage from './system-message';
import TypingIndicator from './typing-indicator';

interface ChatMessagesProps {
  messages: MessageType[];
  onSend: (messages: any[]) => void;
  isBlocked: boolean;
  hasPendingRequest: boolean;
  userId: string;
  userName: string;
  userAvatar: string;
  handlePickImage: () => void;
  replyMessage: MessageType | null;
  setReplyMessage: (message: MessageType | null) => void;
  onInputTextChanged?: (text: string) => void;
  isTyping?: boolean;
  onContentSizeChange?: (width: number, height: number) => void;
  onLayout?: (event: any) => void;
}

const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  onSend,
  isBlocked,
  hasPendingRequest,
  userId,
  userAvatar,
  onInputTextChanged,
  isTyping,
  replyMessage,
  setReplyMessage,
  onContentSizeChange,
  onLayout,
}) => {
  const { data: currentUser } = useLoggedInUser();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const chatRef = React.useRef<AnimatedList<MessageType> | null>(null);
  const swipeableRowRef = React.useRef<Swipeable | null>(null);

  const clearReplyMessage = () => setReplyMessage(null);

  const updateRowRef = React.useCallback(
    (currentRef: any) => {
      const newRefId = currentRef?.props?.children?.props?.currentMessage?._id;

      if (newRefId === replyMessage?._id) {
        swipeableRowRef.current?.close?.();
        swipeableRowRef.current = currentRef;
      }
    },
    [replyMessage]
  );

  React.useEffect(() => {
    if (replyMessage && swipeableRowRef.current) {
      swipeableRowRef.current.close();
      swipeableRowRef.current = null;
    }
  }, [replyMessage]);

  const onLongPress = (context: any, message: MessageType) => {
    const options = ['Reply', 'Copy Text', 'Cancel'];
    const cancelButtonIndex = options.length - 1;

    context.actionSheet().showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
      },
      (buttonIndex: number) => {
        switch (buttonIndex) {
          case 0:
            setReplyMessage(message);
            break;
          case 1:
            Clipboard.setStringAsync(message.text || '');
            break;
        }
      }
    );
  };

  const renderReplyMessageView = (props: any) => {
    if (!props.currentMessage || !props.currentMessage.replyMessage) {
      return null;
    }
    return (
      <View
        className="m-1 rounded-[6px] border-l-2 border-l-white p-2"
        style={{ backgroundColor: 'rgba(52, 52, 52, 0.5)' }}
      >
        <Tiny numberOfLines={1}> {props.currentMessage.replyMessage.text}</Tiny>
      </View>
    );
  };
  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <GiftedChat
        //@ts-ignore
        messageContainerRef={chatRef}
        messages={messages}
        onSend={(messages) => onSend(messages)}
        onLongPress={onLongPress}
        onInputTextChanged={onInputTextChanged}
        user={{
          _id: currentUser?.id || '',
          name: currentUser?.username || '',
          avatar:
            currentUser?.profileImageUrl ||
            require('~/assets/images/avatar.png'),
        }}
        renderMessage={(props) => (
          <ChatMessageBox
            {...props}
            updateRowRef={updateRowRef}
            setReplyOnSwipeOpen={(message) => {
              setReplyMessage(message);
            }}
          />
        )}
        renderBubble={(props) => <CustomMessageBubble {...props} />}
        renderChatFooter={() =>
          replyMessage ? (
            <ReplyMessageBar
              message={replyMessage}
              clearReply={clearReplyMessage}
            />
          ) : null
        }
        renderInputToolbar={(props) => (
          <CustomInputToolbar {...props} isBlocked={isBlocked} />
        )}
        renderSend={(props) => (
          <CustomSend {...props} hasPendingRequest={hasPendingRequest} />
        )}
        renderComposer={(props) => (
          <CustomComposer {...props} hasPendingRequest={hasPendingRequest} />
        )}
        // renderActions={(props) => <CustomActions {...props} />}
        renderSystemMessage={(props) => <CustomSystemMessage {...props} />}
        renderCustomView={renderReplyMessageView}
        renderFooter={(props) => (
          <TypingIndicator
            {...props}
            user={{
              _id: userId,
              userAvatar: userAvatar || require('~/assets/images/avatar.png'),
            }}
            isTyping={isTyping}
          />
        )}
        renderDay={(props) => (
          <Day
            {...props}
            wrapperStyle={{
              backgroundColor: isDark
                ? semanticColors.bg.subtle.dark
                : semanticColors.bg.subtle.light,
            }}
            textStyle={{
              color: isDark
                ? semanticColors.fg.subtle.dark
                : semanticColors.fg.subtle.light,
            }}
          />
        )}
        minInputToolbarHeight={60}
        keyboardShouldPersistTaps="never"
        bottomOffset={0}
        timeFormat="HH:mm"
        dateFormat="LL"
        timeTextStyle={{
          left: styles.timeTextLeft,
          right: styles.timeTextRight,
        }}
        // wrapInSafeArea={false}
        messagesContainerStyle={styles.messagesContainer}
        //@ts-ignore
        scrollToBottom
        infiniteScroll
        showAvatarForEveryMessage={false}
        listViewProps={{
          //@ts-ignore
          showsVerticalScrollIndicator: false,
          onContentSizeChange,
          onLayout,
        }}
        alwaysShowSend
        renderAvatar={null}
      />
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  messagesContainer: {
    paddingBottom: 8,
  },
  timeTextLeft: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 10,
  },
  timeTextRight: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 10,
  },
});

export default ChatMessages;
