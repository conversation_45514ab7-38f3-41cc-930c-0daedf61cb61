import { useColorScheme } from 'nativewind';
import { StyleSheet } from 'react-native';
import {
  type IMessage,
  InputToolbar,
  type InputToolbarProps,
} from 'react-native-gifted-chat';

export default function CustomInputToolbar(
  props: InputToolbarProps<IMessage> & { isBlocked: boolean }
) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  if (props.isBlocked) {
    return null;
  }

  return (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.primaryInputToolbar}
    />
  );
}

const styling = (_isDark: boolean) =>
  StyleSheet.create({
    inputToolbar: {
      backgroundColor: 'transparent',
      borderTopWidth: 1,
      borderTopColor: 'transparent',
      paddingHorizontal: 16,
    },
    primaryInputToolbar: {
      alignItems: 'center',
      gap: 16,
    },
  });
