import moment from 'moment';
import { useColorScheme } from 'nativewind';
import { StyleSheet } from 'react-native';
import { Bubble, type BubbleProps } from 'react-native-gifted-chat';

import {
  colors,
  semanticColors,
  Text,
  View,
  XsRegularLabel,
} from '@/components/ui';
import { cn, useLoggedInUser } from '@/lib';
import { type MessageType } from '@/types';

const MessageReplyBubble = (props: BubbleProps<MessageType>) => {
  const { currentMessage } = props;
  const replyToMessage = currentMessage?.replyToMessage;

  const { data: currentUser } = useLoggedInUser();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  if (!replyToMessage) return null;
  return (
    <View
      style={[
        styles.replyContainer,
        {
          backgroundColor:
            props.position === 'left'
              ? 'rgba(0,0,0,0.1)'
              : 'rgba(255,255,255,0.1)',
        },
      ]}
      // onPress={() => scrollToMessage(replyToMessage._id)}
    >
      <View style={styles.replyContent}>
        <Text style={styles.replyUserName}>
          {replyToMessage.user._id === currentUser?.id
            ? 'You'
            : replyToMessage.user.name}
        </Text>
        <Text style={styles.replyMessageText} numberOfLines={1}>
          {replyToMessage.text || (replyToMessage.image ? '[Image]' : '')}
        </Text>
      </View>
    </View>
  );
};

export default function CustomMessageBubble(props: BubbleProps<MessageType>) {
  const { data: currentUser } = useLoggedInUser();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const messageBelongsToCurrentUser =
    currentUser?.id == props.currentMessage.user._id;

  return (
    <View className="mx-2">
      <MessageReplyBubble {...props} />
      <Bubble
        {...props}
        wrapperStyle={{
          left: styles.leftBubble,
          right: styles.rightBubble,
        }}
        textStyle={{
          left: styles.text,
          right: [styles.text, { paddingRight: 8 }],
        }}
        usernameStyle={styles.username}
        renderTime={() => null}
        tickStyle={{ color: props.currentMessage.read ? '#007AFF' : '#999' }}
      />
      <XsRegularLabel
        className={cn(
          'text-fg-subtle-light my-2 dark:text-fg-subtle-dark',
          messageBelongsToCurrentUser ? 'self-end' : ''
        )}
      >
        {moment(props.currentMessage.createdAt).format('LT')}
      </XsRegularLabel>
    </View>
  );
}

const styling = (isDark: boolean) =>
  StyleSheet.create({
    leftBubble: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: isDark
        ? semanticColors.border.subtle.dark
        : semanticColors.border.subtle.light,
      borderRadius: 16,
      paddingTop: 6,
      paddingBottom: 4,
      paddingHorizontal: 12,
    },
    rightBubble: {
      backgroundColor: isDark ? colors.brand[90] : colors.brand[20],
      borderRadius: 16,
      paddingVertical: 6,
      paddingLeft: 12,
    },
    text: {
      color: isDark ? colors.white : colors.grey[100],
      fontSize: 16,
      fontFamily: 'Aeonik-Regular',
      fontWeight: '400',
    },
    username: {
      color: '#A0A0A0',
      fontWeight: '600',
      fontSize: 12,
    },

    replyContainer: {
      borderLeftWidth: 4,
      borderLeftColor: isDark ? colors.brand[90] : colors.brand[20],
      borderRadius: 4,
      // marginHorizontal: 10,
      marginTop: 10,
      paddingHorizontal: 10,
      paddingVertical: 6,
    },
    replyContent: {
      flexDirection: 'column',
    },
    replyUserName: {
      fontWeight: 'bold',
      fontSize: 12,
      color: isDark ? colors.white : colors.grey[100],
      marginBottom: 2,
    },
    replyMessageText: {
      fontSize: 12,
      color: isDark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)',
    },
  });
