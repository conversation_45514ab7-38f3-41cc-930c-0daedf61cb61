import { useColorScheme } from 'nativewind';
import { StyleSheet } from 'react-native';
import { Composer, type ComposerProps } from 'react-native-gifted-chat';

import { colors } from '@/components/ui';

export default function CustomComposer(
  props: ComposerProps & { hasPendingRequest: boolean }
) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  return (
    <Composer
      {...props}
      textInputStyle={styles.textInput}
      placeholderTextColor={isDark ? colors.grey[60] : colors.grey[50]}
      placeholder="Type your message..."
      //@ts-ignore
      composerHeight="auto"
      multiline
      disableComposer={props.hasPendingRequest}
    />
  );
}

const styling = (isDark: boolean) =>
  StyleSheet.create({
    textInput: {
      color: isDark ? colors.white : colors.grey[100],
      backgroundColor: 'transparent',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: isDark ? colors.grey[80] : colors.grey[20],
      padding: 16,
      fontSize: 16,
      lineHeight: 16,
      fontFamily: 'Aeonik-Regular',
      fontWeight: '400',
      marginLeft: 0,
      marginVertical: 0,
      flex: 1,
      minHeight: 48,
      maxHeight: 100,
    },
  });
