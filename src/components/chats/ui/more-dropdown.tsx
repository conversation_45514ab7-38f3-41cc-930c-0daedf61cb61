import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { MoreHorizontal, semanticColors } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/picker';
import { cn } from '@/lib';

type DropdownOptions = 'block' | 'report' | 'toggle-fav' | 'delete';

export interface ChatScreenMoreDropdownProps {
  onValueChange: (value: DropdownOptions) => void;
  value?: DropdownOptions;
  disabled?: boolean;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  itemTextClassName?: string;
  contentInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
  isBlocked?: boolean;
  isUserFavourited?: boolean;
}

const ChatScreenMoreDropdown: React.FC<ChatScreenMoreDropdownProps> = ({
  onValueChange,
  disabled = false,
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-grey-100 dark:text-white',
  contentInsets,
  isBlocked,
  isUserFavourited,
}) => {
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const dropdownOptions: { label: string; value: DropdownOptions }[] = [
    {
      label: `${isUserFavourited ? 'Unfavourite' : 'Favourite'} user`,
      value: 'toggle-fav',
    },
    { label: 'Report user', value: 'report' },
    { label: `${isBlocked ? 'Unblock' : 'Block'} user`, value: 'block' },
    { label: 'Delete chat', value: 'delete' },
  ];

  const defaultContentInsets = {
    top: insets.top,
    bottom: insets.bottom,
    right: 8,
    ...contentInsets,
  };

  return (
    <Select
      onValueChange={(option) => {
        if (option && !disabled) {
          onValueChange(option.value as DropdownOptions);
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          'native:size-8 rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark native:items-center native:justify-center p-0',
          disabled && 'opacity-50',
          triggerClassName
        )}
        hideChevron
        disabled={disabled}
      >
        <MoreHorizontal
          color={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
          fill={
            isDark ? semanticColors.fg.base.dark : semanticColors.fg.base.light
          }
        />
      </SelectTrigger>

      <SelectContent
        insets={defaultContentInsets}
        className={cn(
          'w-[220px] bg-black/45 border-0 dark:bg-black/70 rounded-lg',
          contentClassName
        )}
      >
        <SelectGroup>
          {dropdownOptions.map(({ label, value }) => (
            <SelectItem
              key={value}
              label={label}
              value={value}
              textClassName={cn(
                'native:text-fg-base-light native:dark:text-fg-base-dark',
                value === 'delete' &&
                  'native:text-fg-danger-light native:dark:text-fg-danger-dark',
                itemTextClassName
              )}
              className={itemClassName}
              hasCheck={false}
            >
              {label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default ChatScreenMoreDropdown;
