import { Entypo } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';

import { colors, P, TouchableOpacity, View } from '@/components/ui';

const replyMessageBarHeight = 50;

const ReplyMessageBar = ({ clearReply, message }: any) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  return (
    <View style={styles.container}>
      <View style={styles.replyImageContainer}>
        <Entypo name="reply" size={20} color={isDark ? 'white' : 'dark'} />
      </View>

      <View style={styles.messageContainer}>
        <P className="text-grey-60 dark:text-grey-50">{message?.text}</P>
      </View>

      <TouchableOpacity style={styles.crossButton} onPress={clearReply}>
        <Entypo name="cross" size={20} color={isDark ? 'white' : 'dark'} />
      </TouchableOpacity>
    </View>
  );
};

export default ReplyMessageBar;

const styling = (isDark: boolean) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 6,
      paddingHorizontal: 16,
      height: replyMessageBarHeight,
      borderTopWidth: 1,
      borderTopColor: isDark ? colors.grey[80] : colors.grey[20],
      backgroundColor: isDark ? colors.grey[100] : colors.white,
      marginTop: 10,
    },
    replyImageContainer: {
      paddingRight: 6,
      borderRightWidth: 2,
      borderRightColor: colors.brand[60],
      marginRight: 6,
      height: '100%',
      justifyContent: 'center',
    },
    crossButtonIcon: {
      width: 24,
      height: 24,
    },
    crossButton: {},
    messageContainer: {
      flex: 1,
    },
  });
