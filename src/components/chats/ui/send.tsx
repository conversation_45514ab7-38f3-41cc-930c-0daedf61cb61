import { StyleSheet } from 'react-native';
import { type IMessage, Send, type SendProps } from 'react-native-gifted-chat';

import { SendIcon, View } from '@/components/ui';

export default function CustomSend(
  props: SendProps<IMessage> & { hasPendingRequest: boolean }
) {
  if (props.hasPendingRequest) {
    return null;
  }

  return (
    <Send {...props} containerStyle={styles.sendContainer}>
      <View className="size-8 items-center justify-center rounded-full bg-brand-60 dark:bg-brand-60">
        <SendIcon />
      </View>
    </Send>
  );
}

const styles = StyleSheet.create({
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
