import { Ionicons } from '@expo/vector-icons';
import { type SystemMessageProps } from 'react-native-gifted-chat';

import { colors, Small, View } from '@/components/ui';
import { useLoggedInUser } from '@/lib';
import { type MessageType } from '@/types';

export default function CustomSystemMessage(
  props: SystemMessageProps<MessageType>
) {
  const { currentMessage } = props;
  const messageText = currentMessage.text;
  const requestType = currentMessage.requestType;

  const { data: currentUser } = useLoggedInUser();

  // Only show new_request messages to the recipient
  if (
    requestType === 'new_request' &&
    currentMessage.user._id === currentUser?.id
  ) {
    return null;
  }

  // Only show request_accepted messages to the sender
  if (
    requestType === 'request_accepted' &&
    currentMessage.user._id !== currentUser?.id
  ) {
    return null;
  }

  // Determine which icon to show based on the message content
  let iconName: any = 'information-circle-outline';
  let iconColor = colors.brand[60];

  if (messageText.includes('accepted')) {
    iconName = 'checkmark-circle-outline';
  } else if (messageText.includes('declined')) {
    iconName = 'close-circle-outline';
  } else if (messageText.includes('wants to chat')) {
    iconName = 'mail-outline';
  }

  return (
    <View className="my-2.5 items-center px-2">
      <View className="max-w-[80%] flex-row items-center rounded-[15px] bg-transparent px-3 py-2">
        <Ionicons
          name={iconName}
          size={16}
          color={iconColor}
          className="mr-1.5"
        />
        <Small className="text-center text-grey-50 dark:text-grey-60">
          {messageText}
        </Small>
      </View>
    </View>
  );
}
