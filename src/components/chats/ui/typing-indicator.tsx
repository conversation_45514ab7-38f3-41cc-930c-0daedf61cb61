import { useColorScheme } from 'nativewind';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { type MessageContainerProps } from 'react-native-gifted-chat';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

import { colors, Image } from '@/components/ui';
import { type MessageType } from '@/types';

const DotsAnimation = () => {
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);

  const topY = useMemo(() => -5, []);
  const bottomY = useMemo(() => 5, []);
  const duration = useMemo(() => 500, []);

  const dot1Style = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY: dot1.value,
        },
      ],
    }),
    [dot1]
  );

  const dot2Style = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY: dot2.value,
        },
      ],
    }),
    [dot2]
  );

  const dot3Style = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY: dot3.value,
        },
      ],
    }),
    [dot3]
  );

  useEffect(() => {
    dot1.value = withRepeat(
      withSequence(
        withTiming(topY, { duration }),
        withTiming(bottomY, { duration })
      ),
      0,
      true
    );
  }, [dot1, topY, bottomY, duration]);

  useEffect(() => {
    dot2.value = withDelay(
      100,
      withRepeat(
        withSequence(
          withTiming(topY, { duration }),
          withTiming(bottomY, { duration })
        ),
        0,
        true
      )
    );
  }, [dot2, topY, bottomY, duration]);

  useEffect(() => {
    dot3.value = withDelay(
      200,
      withRepeat(
        withSequence(
          withTiming(topY, { duration }),
          withTiming(bottomY, { duration })
        ),
        0,
        true
      )
    );
  }, [dot3, topY, bottomY, duration]);

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  return (
    <View style={[styles.fill, styles.centerItems, styles.dots]}>
      <Animated.View style={[styles.dot, dot1Style]} />
      <Animated.View style={[styles.dot, dot2Style]} />
      <Animated.View style={[styles.dot, dot3Style]} />
    </View>
  );
};

interface TypingIndicatorProps extends MessageContainerProps<MessageType> {
  user: {
    userAvatar: string;
    _id: string;
  };
  isTyping?: boolean;
}

const TypingIndicator = ({ isTyping, user }: TypingIndicatorProps) => {
  const yCoords = useSharedValue(200);
  const heightScale = useSharedValue(0);
  const marginScale = useSharedValue(0);

  const [isVisible, setIsVisible] = useState(isTyping);

  const containerStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY: yCoords.value,
        },
      ],
      height: heightScale.value,
      marginBottom: marginScale.value,
    }),
    [yCoords, heightScale, marginScale]
  );

  const slideIn = useCallback(() => {
    const duration = 250;

    yCoords.value = withTiming(0, { duration });
    heightScale.value = withTiming(35, { duration });
    marginScale.value = withTiming(8, { duration });
  }, [yCoords, heightScale, marginScale]);

  const slideOut = useCallback(() => {
    const duration = 250;

    yCoords.value = withTiming(200, { duration }, (isFinished) => {
      if (isFinished) runOnJS(setIsVisible)(false);
    });
    heightScale.value = withTiming(0, { duration });
    marginScale.value = withTiming(0, { duration });
  }, [yCoords, heightScale, marginScale]);

  useEffect(() => {
    if (isVisible)
      if (isTyping) slideIn();
      else slideOut();
  }, [isVisible, isTyping, slideIn, slideOut]);

  useEffect(() => {
    if (isTyping) setIsVisible(true);
  }, [isTyping]);

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  if (!isVisible) return null;

  if (user.userAvatar) {
    return (
      <Animated.View style={styles.container}>
        <Image
          source={user.userAvatar}
          className="size-9 rounded-full"
          priority="high"
        />
        <View style={[styles.animatedContainer, containerStyle]}>
          <DotsAnimation />
        </View>
      </Animated.View>
    );
  } else {
    return (
      <Animated.View style={[styles.animatedContainer, containerStyle]}>
        <DotsAnimation />
      </Animated.View>
    );
  }
};

export default TypingIndicator;

const styling = (isDark: boolean) =>
  StyleSheet.create({
    container: {
      position: 'relative',
      flexDirection: 'row',
      marginLeft: 8,
      alignItems: 'flex-end',
      marginTop: 8,
    },
    animatedContainer: {
      position: 'relative',
      marginLeft: 8,
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 20,
      borderBottomLeftRadius: 5,
      backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
    },
    dots: {
      flexDirection: 'row',
    },
    dot: {
      marginLeft: 2,
      marginRight: 2,
      borderRadius: 4,
      width: 8,
      height: 8,
      backgroundColor: !isDark ? colors.grey[100] : colors.white,
    },

    fill: {
      flex: 1,
    },
    centerItems: {
      justifyContent: 'center',
      alignItems: 'center',
    },

    imgContainer: {
      marginLeft: 8,
    },
    onTop: {
      alignSelf: 'flex-start',
    },
  });
