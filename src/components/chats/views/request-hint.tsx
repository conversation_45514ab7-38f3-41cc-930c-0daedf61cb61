import React from 'react';

import { P, View } from '@/components/ui';

interface RequestHintViewProps {
  userName: string;
}

export const RequestHintView: React.FC<RequestHintViewProps> = ({
  userName,
}) => (
  <View className="m-2 rounded-[12px] bg-[#F2F2F7] p-3">
    <P className="text-center text-[#8E8E93] dark:text-[#8E8E93]">
      Your first message will be sent as a request. {userName} will need to
      accept it before you can chat.
    </P>
  </View>
);
