import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';

import { colors, TouchableOpacity } from '../ui';

interface BackProps {
  onBackPress?: () => void;
  color?: string;
}

export const Back: React.FC<BackProps> = ({ onBackPress, color }) => {
  const router = useRouter();

  return (
    <TouchableOpacity
      className="size-8 items-center justify-center"
      onPress={() => {
        if (onBackPress) {
          onBackPress();
        } else {
          router.back();
        }
      }}
    >
      <Ionicons
        name="chevron-back"
        size={32}
        color={color || colors.brand['60']}
        className="text-fg-link"
      />
    </TouchableOpacity>
  );
};
