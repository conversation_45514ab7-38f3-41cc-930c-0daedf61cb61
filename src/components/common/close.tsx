import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';

import { colors, TouchableOpacity } from '../ui';

interface CloseProps {}

export const Close: React.FC<CloseProps> = () => {
  const router = useRouter();

  return (
    <TouchableOpacity
      className="size-8"
      onPress={() => {
        router.dismissTo('/');
      }}
    >
      <Ionicons
        name="close-outline"
        size={32}
        color={colors.brand['60']}
        className="text-fg-link"
      />
    </TouchableOpacity>
  );
};
