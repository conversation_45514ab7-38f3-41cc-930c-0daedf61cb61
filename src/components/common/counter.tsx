import { AntDesign, Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';

import { cn } from '@/lib';

import { colors, MdRegularLabel } from '../ui';

export interface CounterProps {
  value?: number; // Changed from initialValue to value for controlled component
  initialValue?: number; // Keep for backward compatibility
  minimum?: number;
  maximum?: number;
  onValueChange?: (value: number) => void;
  className?: string;
}

const Counter: React.FC<CounterProps> = ({
  value, // Controlled value
  initialValue = 0,
  minimum = 0,
  maximum = 100,
  onValueChange,
  className,
}) => {
  const [count, setCount] = useState(value ?? initialValue);

  // Sync internal state with external value prop
  useEffect(() => {
    if (value !== undefined && value !== count) {
      setCount(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const handleDecrement = () => {
    if (count > minimum) {
      const newValue = count - 1;
      setCount(newValue);
      onValueChange?.(newValue);
    }
  };

  const handleIncrement = () => {
    if (count < maximum) {
      const newValue = count + 1;
      setCount(newValue);
      onValueChange?.(newValue);
    }
  };

  const isDecrementDisabled = count <= minimum;
  const isIncrementDisabled = count >= maximum;

  return (
    <View
      className={cn(
        'w-[100px] flex-row items-center justify-between rounded-full bg-white p-2 dark:bg-grey-100',
        className
      )}
    >
      <TouchableOpacity
        onPress={handleDecrement}
        disabled={isDecrementDisabled}
        style={{ opacity: isDecrementDisabled ? 0.2 : 1 }}
      >
        <AntDesign name="minus" size={16} color={colors.brand[60]} />
      </TouchableOpacity>

      <MdRegularLabel>{count}</MdRegularLabel>

      <TouchableOpacity
        onPress={handleIncrement}
        disabled={isIncrementDisabled}
        style={{ opacity: isIncrementDisabled ? 0.2 : 1 }}
      >
        <Ionicons name="add" size={16} color={colors.brand[60]} />
      </TouchableOpacity>
    </View>
  );
};

export default Counter;
