import { useRouter } from 'expo-router';
import React from 'react';

import { Text, TouchableOpacity } from '../ui';

interface DoneProps {}

export const Done: React.FC<DoneProps> = () => {
  const router = useRouter();

  return (
    <TouchableOpacity
      className="items-center justify-end"
      onPress={() => {
        router.dismissAll();
      }}
    >
      <Text className="text-lg font-bold text-fg-link dark:text-fg-link">
        Done
      </Text>
    </TouchableOpacity>
  );
};
