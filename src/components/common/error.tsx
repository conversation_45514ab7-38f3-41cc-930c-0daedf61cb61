import { View, Text, Pressable, semanticColors } from '@/components/ui';
import { useColorScheme } from 'nativewind';
import { Feather } from '@expo/vector-icons';

type ErrorBannerProps = {
  message?: string;
  onRetry?: () => void;
};

export function ErrorBanner({
  message = 'You’re offline. Showing last saved data.',
  onRetry,
}: ErrorBannerProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="w-full flex-row items-center justify-between px-4 py-3 bg-bg-error-light dark:bg-bg-error-dark border-b border-border-error-light dark:border-border-error-dark">
      <View className="flex-row items-center flex-1">
        <Feather
          name="wifi-off"
          size={18}
          color={
            isDark
              ? semanticColors.fg.error.dark
              : semanticColors.fg.error.light
          }
        />
        <Text
          className="text-fg-error-light dark:text-fg-error-dark text-sm flex-shrink"
          numberOfLines={2}
        >
          {message}
        </Text>
      </View>

      {onRetry && (
        <Pressable
          onPress={onRetry}
          className="ml-3 px-3 py-1 rounded-full bg-bg-error-contrast-light dark:bg-bg-error-contrast-dark"
        >
          <Text className="text-white text-xs font-medium">Retry</Text>
        </Pressable>
      )}
    </View>
  );
}
