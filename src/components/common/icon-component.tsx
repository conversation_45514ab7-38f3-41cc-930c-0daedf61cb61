import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ather,
  FontAwesome,
  FontAwesome5,
  FontAwesome6,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';

import { cn } from '@/lib';
import { type IconType } from '@/types';

import { colors, Image } from '../ui';
import {
  ChatIcon,
  HelpIcon,
  InfoIcon,
  MessageDotCircle,
  XIcon,
} from '../ui/icons';

interface IconComponentProps {
  iconType: IconType;
  iconName: string;
  size?: number;
  color?: string;
  className?: string;
}

export const IconComponent: React.FC<IconComponentProps> = ({
  iconType,
  iconName,
  size = 24,
  color = 'white',
  className,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  switch (iconType) {
    case 'ionicons':
      return <Ionicons name={iconName as any} size={size} color={color} />;
    case 'font-awesome':
      return <FontAwesome name={iconName as any} size={size} color={color} />;
    case 'font-awesome-5':
      return <FontAwesome5 name={iconName as any} size={size} color={color} />;
    case 'font-awesome-6':
      return <FontAwesome6 name={iconName as any} size={size} color={color} />;
    case 'feather':
      return <Feather name={iconName as any} size={size} color={color} />;
    case 'material':
      return <MaterialIcons name={iconName as any} size={size} color={color} />;
    case 'material-community':
      return (
        <MaterialCommunityIcons
          name={iconName as any}
          size={size}
          color={color}
        />
      );
    case 'entypo':
      return <Entypo name={iconName as any} size={size} color={color} />;
    case 'custom-svg':
      // Return the appropriate custom SVG based on iconName
      switch (iconName) {
        case 'Help':
          return <HelpIcon color={isDark ? 'white' : '#070707'} />;
        case 'Instagram':
          return (
            <Image
              source={require('~/assets/icons/instagram.png')}
              className={cn('size-6', className)}
            />
          );
        case 'Zoom':
          return (
            <Image
              source={require('~/assets/icons/zoom.png')}
              className={cn('size-6', className)}
            />
          );
        case 'Snapchat':
          return (
            <Image
              source={require('~/assets/icons/snapchat.png')}
              className={cn('size-6', className)}
            />
          );
        case 'Tiktok':
          return (
            <Image
              source={require('~/assets/icons/tiktok.png')}
              className={cn('size-6', className)}
            />
          );
        case 'X':
          return <XIcon color={isDark ? 'white' : '#070707'} />;
        case 'ChatIcon':
          return (
            <ChatIcon strokeColor={isDark ? colors.white : colors.grey[100]} />
          );
        case 'InfoIcon':
          return <InfoIcon color={isDark ? colors.white : colors.grey[100]} />;
        case 'MessageDotCircle':
          return (
            <MessageDotCircle
              color={color ? color : isDark ? colors.white : colors.grey[100]}
            />
          );
        default:
          return null;
      }
    default:
      return null;
  }
};
