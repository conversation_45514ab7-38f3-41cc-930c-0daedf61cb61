import * as React from 'react';
import { type GestureResponderEvent, Pressable } from 'react-native';

import { View } from '@/components/ui';
import { cn } from '@/lib';

type PaginationDotsProps = {
  total: number;
  current: number;
  className?: string;
  dotClassName?: string;
  dotActiveClassName?: string;
  onDotPress?: (event: GestureResponderEvent) => void;
};

export const PaginationDots: React.FC<PaginationDotsProps> = ({
  total,
  current,
  className,
  dotClassName,
  dotActiveClassName,
  onDotPress,
}) => {
  if (total <= 1) return null;

  return (
    <View
      className={cn(
        'flex-row justify-center items-center mt-4 gap-2',
        className
      )}
    >
      {Array.from({ length: total }).map((_, index) => (
        <Pressable
          key={index}
          onPress={onDotPress}
          className={cn(
            'size-2 rounded-full',
            index === current
              ? cn('bg-brand-60', dotActiveClassName)
              : cn('bg-grey-40 dark:bg-grey-60', dotClassName)
          )}
        />
      ))}
    </View>
  );
};
