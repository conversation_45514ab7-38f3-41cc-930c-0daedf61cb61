import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { cn } from '@/lib';

import { MdRegularLabel } from '../ui';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '../ui/picker';

export interface QuantitySelectorProps {
  /**
   * Current selected quantity value
   */
  value: number;

  /**
   * Callback function called when quantity changes
   */
  onValueChange: (quantity: number) => void;

  /**
   * Minimum selectable quantity (default: 0)
   */
  minimum?: number;

  /**
   * Maximum selectable quantity (default: 10)
   */
  maximum?: number;

  /**
   * Step increment for quantity options (default: 1)
   */
  step?: number;

  /**
   * Custom quantity options array (overrides min/max/step)
   */
  options?: number[];

  /**
   * Whether the selector is disabled
   */
  disabled?: boolean;

  /**
   * Custom placeholder text when no value is selected
   */
  placeholder?: string;

  /**
   * Custom CSS classes for the trigger
   */
  triggerClassName?: string;

  /**
   * Custom CSS classes for the content container
   */
  contentClassName?: string;

  /**
   * Custom CSS classes for individual items
   */
  itemClassName?: string;

  /**
   * Custom CSS classes for item text
   */
  itemTextClassName?: string;

  /**
   * Custom insets for the dropdown content
   */
  contentInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };

  /**
   * Custom formatter for display values
   */
  formatter?: (value: number) => string;

  /**
   * Show zero as an option (useful when minimum is 0)
   */
  includeZero?: boolean;
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  value,
  onValueChange,
  minimum = 0,
  maximum = 10,
  step = 1,
  options,
  disabled = false,
  placeholder = 'Select quantity',
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-grey-100 dark:text-white',
  contentInsets,
  formatter,
  includeZero = true,
}) => {
  const insets = useSafeAreaInsets();

  const generateOptions = (): number[] => {
    if (options) {
      return options;
    }

    const opts: number[] = [];
    const start = includeZero ? Math.min(minimum, 0) : Math.max(minimum, 1);

    for (let i = start; i <= maximum; i += step) {
      opts.push(i);
    }

    return opts;
  };

  const quantityOptions = generateOptions();

  const defaultContentInsets = {
    top: insets.top,
    bottom: insets.bottom + 20,
    right: 16,
    ...contentInsets,
  };

  const formatLabel = (val: number): string => {
    if (formatter) {
      return formatter(val);
    }
    return val.toString();
  };

  return (
    <Select
      defaultValue={
        value !== undefined
          ? {
              value: value.toString(),
              label: formatLabel(value),
            }
          : undefined
      }
      onValueChange={(option) => {
        if (option && !disabled) {
          onValueChange(Number(option.value));
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          'border border-border-subtle-light dark:border-border-subtle-dark  rounded-md py-2.5 px-3 flex-row items-center gap-3.5',
          disabled && 'opacity-50',
          triggerClassName
        )}
      >
        <MdRegularLabel>
          {value !== undefined ? formatLabel(value) : placeholder}
        </MdRegularLabel>
      </SelectTrigger>

      <SelectContent
        insets={defaultContentInsets}
        className={cn(
          'min-w-[70px] bg-bg-canvas-light/90 border-0 dark:bg-bg-canvas-dark rounded-lg',
          contentClassName
        )}
      >
        <SelectGroup>
          {quantityOptions.map((qty) => (
            <SelectItem
              key={qty}
              label={formatLabel(qty)}
              value={qty.toString()}
              textClassName={itemTextClassName}
              className={itemClassName}
            >
              {formatLabel(qty)}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default QuantitySelector;
