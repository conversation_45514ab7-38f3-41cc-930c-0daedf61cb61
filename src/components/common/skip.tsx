import { useRouter } from 'expo-router';
import React from 'react';

import { Tiny, TouchableOpacity } from '../ui';

interface SkipProps {
  link: string;
}

export const Skip: React.FC<SkipProps> = ({ link }) => {
  const router = useRouter();

  return (
    <TouchableOpacity
      className="size-8"
      onPress={() => {
        router.push(`/${link}`);
      }}
    >
      <Tiny className="text-fg-link">Skip</Tiny>
    </TouchableOpacity>
  );
};
