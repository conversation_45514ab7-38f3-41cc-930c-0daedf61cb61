import { Feather, Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { BlurredBackground } from '../background/blur';
import {
  colors,
  EventIcon,
  LiveIcon,
  MdRegularLabel,
  Pressable,
  View,
  XsRegularLabel,
} from '../ui';

export interface FloatingActionPopoverProps {}

const actions = [
  {
    label: 'Live',
    description: 'Entertain your audience, accept requests',
    Icon: LiveIcon,
    pathname: '/session/create/type',
  },
  {
    label: 'Event',
    description: 'Host a paid or free events',
    Icon: EventIcon,
    pathname: '/events/create',
  },
  // {
  //   label: 'Highlight',
  //   description: 'Add a picture or video post to your profile',
  //   Icon: PlayIcon,
  //   pathname: '/',
  // },
];

export const FloatingActionPopover = () => {
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const [open, setOpen] = useState(false);

  const contentInsets = {
    top: insets.top,
    bottom: insets.bottom,
    left: 12,
    right: 12,
  };

  const iconRotation = useSharedValue(0);

  useEffect(() => {
    iconRotation.value = withTiming(open ? 1 : 0, { duration: 200 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const animatedIconStyle = useAnimatedStyle(() => {
    const rotation = interpolate(iconRotation.value, [0, 1], [0, 90]); // 90deg rotation
    return {
      transform: [{ rotate: `${rotation}deg` }],
    };
  });

  return (
    <Popover onOpenChange={setOpen} className="relative">
      <PopoverTrigger asChild>
        <Pressable className="absolute bottom-[115px] right-4 size-14 items-center justify-center rounded-full border-2 border-white bg-[#7257FF]">
          <Animated.View style={animatedIconStyle}>
            {open ? (
              <Feather name="x" size={24} color="white" />
            ) : (
              <Ionicons name="add" size={24} color="white" />
            )}
          </Animated.View>
        </Pressable>
      </PopoverTrigger>

      <PopoverContent
        side="top"
        insets={contentInsets}
        className="w-auto rounded-lg border-0 bg-bg-subtle-light p-0 dark:bg-bg-subtle-dark"
      >
        {isDark && (
          <BlurredBackground
            isDark={isDark}
            blurStyle={styles.blurView}
            overlayStyle={styles.overlay}
            intensity={5}
          />
        )}
        <View className="flex-1">
          {actions.map(({ label, Icon, pathname, description }, index) => (
            <Link asChild href={pathname as any} key={index}>
              <PopoverClose
                key={label}
                className="w-full flex-row items-center justify-start gap-2.5 px-4 py-3.5"
              >
                <Icon color={isDark ? colors.grey[60] : colors.grey[50]} />
                <View className="gap-2">
                  <MdRegularLabel className="text-grey-100 dark:text-white">
                    {label}
                  </MdRegularLabel>
                  <XsRegularLabel className="text-fg-subtle-light dark:text-fg-subtle-dark">
                    {description}
                  </XsRegularLabel>
                </View>
              </PopoverClose>
            </Link>
          ))}
        </View>
      </PopoverContent>
    </Popover>
  );
};

export const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      borderRadius: 16,
      ...StyleSheet.absoluteFillObject,
    },
  });
