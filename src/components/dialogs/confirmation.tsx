import React from 'react';
import { Modal, Text, View } from 'react-native';

import { Button, H2 } from '@/components/ui';

interface ConfirmationDialogProps {
  visible: boolean;
  title?: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  visible,
  title = 'Are you sure?',
  message,
  onConfirm,
  onCancel,
}) => {
  return (
    <Modal visible={visible} transparent animationType="slide">
      <View className="flex-1 items-center justify-center bg-black/50">
        <View className="w-4/5 gap-6 rounded-lg bg-bg-surface-light p-6 shadow-lg dark:bg-bg-surface-dark">
          <View className="items-center justify-center gap-2">
            <H2>{title}</H2>
            <Text className="mb-2 text-center text-base font-bold text-fg-muted-light dark:text-fg-muted-dark">
              {message}
            </Text>
          </View>

          <View className="gap-3">
            <Button label="Yes" onPress={onConfirm} variant="default" />
            <Button label="Dismiss" onPress={onCancel} variant="outline" />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationDialog;
