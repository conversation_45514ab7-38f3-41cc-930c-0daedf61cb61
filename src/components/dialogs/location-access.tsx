import * as React from 'react';
import { Modal } from 'react-native';

import { Button, H2, Image, P, View } from '@/components/ui';

type LocationAccessDialogProps = {
  visible: boolean;
  title?: string;
  message: string;
  confirmLabel?: string;
  dismissmLabel?: string;
  onConfirm: () => void;
  onCancel: () => void;
};

export const LocationAccessDialog = ({
  visible,
  title = 'Find your vibe nearby!',
  message,
  onConfirm,
  onCancel,
  confirmLabel = 'Allow access',
  dismissmLabel = 'Later',
}: LocationAccessDialogProps) => {
  return (
    <Modal visible={visible} transparent animationType="slide">
      <View className="flex-1 items-center justify-center bg-black/50">
        <View className="w-4/5 items-center gap-6 rounded-lg bg-bg-canvas-light p-6 pt-8 dark:bg-bg-canvas-dark">
          <Image
            source={require('~/assets/images/location.png')}
            className="size-[120px]"
          />
          <View className="items-center gap-2">
            <H2>{title}</H2>
            <P className="text-center text-fg-muted-light dark:text-fg-muted-dark">
              {message}
            </P>
          </View>
          <View className="flex-row gap-3">
            <Button
              className="flex-1 bg-accent-subtle-light dark:bg-accent-subtle-dark"
              textClassName="text-accent-bold-light dark:text-accent-bold-dark"
              label={dismissmLabel}
              onPress={onCancel}
              fullWidth={false}
            />
            <Button
              className="flex-1 bg-accent-moderate"
              label={confirmLabel}
              onPress={onConfirm}
              fullWidth={false}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
