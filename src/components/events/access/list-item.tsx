import { useLocalSearchParams } from 'expo-router';
import * as React from 'react';

import { type IEventAccessCode } from '@/api/events';
import { ShareModal } from '@/components/modals/share';
import { Button, ShareIcon, SmRegularLabel, View } from '@/components/ui';
import { APP_URL } from '@/lib';

type Props = IEventAccessCode;

export const AccessCodeListItem = ({ code, isUsed }: Props) => {
  const { id, slug, title } = useLocalSearchParams<{
    id: string;
    slug: string;
    title: string;
  }>();
  const shareModalRef = React.useRef<any>(null);
  const handleShare = () => shareModalRef.current?.present();

  const shareContent = `You have been invited to ${title}.
    ${APP_URL}/events/${slug || id}
  Your exclusive code is : ${code}`;

  return (
    <View className="h-12 flex-row items-center justify-between rounded-md border border-accent-muted-light py-2 pl-5 pr-3 dark:border-accent-muted-dark">
      <SmRegularLabel>{code}</SmRegularLabel>
      {!isUsed ? (
        <Button
          size="xs"
          label="Share"
          iconPosition="left"
          icon={<ShareIcon width={16} height={16} color="#fff" />}
          iconClassName="left-2"
          textClassName="pl-5"
          onPress={handleShare}
        />
      ) : (
        <Button size="xs" label="Used" disabled />
      )}
      <ShareModal
        shareModalRef={shareModalRef}
        onDismiss={() => shareModalRef.current?.dismiss()}
        content={shareContent}
      />
    </View>
  );
};
