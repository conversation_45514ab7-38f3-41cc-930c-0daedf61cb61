import * as React from 'react';

import { H3, SmRegularLabel, View } from '@/components/ui';

type AnalyticsCardProps = {
  title: string;
  subtitle: string;
  children: React.ReactNode;
};

const AnalyticsCard: React.FC<AnalyticsCardProps> = ({
  title,
  subtitle,
  children,
}) => {
  // const { colorScheme } = useColorScheme();
  // const isDark = colorScheme === 'dark';
  return (
    <View className="rounded-lg border border-border-subtle-light dark:border-border-subtle-dark">
      <View className="flex-row items-center justify-between px-4 py-5">
        <View className="flex-1 gap-1">
          <H3>{title}</H3>
          <SmRegularLabel className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {subtitle}
          </SmRegularLabel>
        </View>
        {/* <Pressable>
          <MoreHorizontal
            height={24}
            width={24}
            color={isDark ? colors.grey[60] : colors.grey[50]}
          />
        </Pressable> */}
      </View>
      {children}
    </View>
  );
};

export default AnalyticsCard;
