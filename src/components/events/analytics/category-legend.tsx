import * as React from 'react';

import { colors, View, XsRegularLabel } from '@/components/ui';

interface TicketCategory {
  label: string;
  value: number;
}

type CategoryLegendProps = {
  categories: TicketCategory[];
  colorKeys: string[];
};

const CategoryLegend: React.FC<CategoryLegendProps> = ({
  categories,
  colorKeys,
}) => (
  <View className="flex-row items-center justify-center gap-4 px-5 py-2">
    {categories.map((ticket, index) => (
      <View key={index} className="flex-row items-center gap-2">
        <View
          className="size-2 rounded-full"
          style={{
            backgroundColor:
              colors[colorKeys[index] as keyof typeof colors][40],
          }}
        />
        <XsRegularLabel>{ticket.label}</XsRegularLabel>
      </View>
    ))}
  </View>
);

export default CategoryLegend;
