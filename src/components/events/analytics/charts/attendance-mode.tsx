import * as React from 'react';
import { Bar<PERSON>hart, type barDataItem } from 'react-native-gifted-charts';

import { View } from '@/components/ui';

import { type HorizontalBarConfig } from './types';

type AttendanceModeChartProps = {
  data: barDataItem[];
  config: HorizontalBarConfig;
};

const AttendanceModeChart: React.FC<AttendanceModeChartProps> = ({
  data,
  config,
}) => (
  <View>
    <BarChart {...config} data={data} />
  </View>
);

export default AttendanceModeChart;
