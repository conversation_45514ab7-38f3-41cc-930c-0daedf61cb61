import * as React from 'react';
import { <PERSON><PERSON><PERSON> } from 'react-native-gifted-charts';

import { H6, View } from '@/components/ui';

import { type PieChartConfig, type PieChartData } from './types';

type ConversionPieChartProps = {
  data: PieChartData[];
  config: PieChartConfig;
};

const ConversionRatePieChart: React.FC<ConversionPieChartProps> = ({
  data,
  config,
}) => (
  <View className="h-[280px] items-center justify-center">
    <PieChart
      {...config}
      data={data}
      externalLabelComponent={(item, index) => (
        <View
          key={index}
          className="w-[54px] flex-row items-center gap-1 rounded-md bg-bg-canvas-light p-2 dark:bg-bg-canvas-dark"
        >
          <View
            className="size-2 rounded-full"
            style={{ backgroundColor: item?.color }}
          />
          <H6>{item?.text}</H6>
        </View>
      )}
    />
  </View>
);

export default ConversionRatePieChart;
