import * as React from 'react';
import { Bar<PERSON>hart, type barDataItem } from 'react-native-gifted-charts';

import { View } from '@/components/ui';

import { type BarChartConfig } from './types';

type TicketSalesChartProps = {
  data: barDataItem[];
  config: BarChartConfig;
  spacing: number;
};

const TicketSalesChart: React.FC<TicketSalesChartProps> = ({
  data,
  config,
  spacing,
}) => (
  <View className="px-4 py-5">
    <BarChart
      {...config}
      data={data}
      spacing={spacing}
      onPress={(item: barDataItem) => console.log('item', item)}
    />
  </View>
);

export default TicketSalesChart;
