import { type pieDataItem } from 'react-native-gifted-charts';

import { type useAnalyticsChartConfig } from '@/lib';

export type AnalyticsChartConfig = ReturnType<typeof useAnalyticsChartConfig>;

export type BarChartConfig = AnalyticsChartConfig['barChartConfig'];
export type PieChartConfig = AnalyticsChartConfig['pieChartConfig'];
export type HorizontalBarConfig = AnalyticsChartConfig['horizontalBarConfig'];

export interface PieChartData
  extends Pick<pieDataItem, 'value' | 'color' | 'text'> {}
