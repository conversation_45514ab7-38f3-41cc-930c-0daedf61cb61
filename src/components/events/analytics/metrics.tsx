import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import * as React from 'react';

import { H3, MdBoldLabel, semanticColors, View } from '@/components/ui';

type MetricDisplayProps = { value: string; change: number };

const MetricDisplay: React.FC<MetricDisplayProps> = ({ value, change }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="flex-row items-center justify-between gap-4 px-4 py-5">
      <H3 className="flex-1">{value}</H3>
      <View className="flex-row items-center">
        <Ionicons
          name={change >= 0 ? 'arrow-up' : 'arrow-down'}
          size={16}
          color={
            isDark
              ? change >= 0
                ? semanticColors.fg.success.dark
                : semanticColors.fg.danger.dark
              : change >= 0
                ? semanticColors.fg.success.light
                : semanticColors.fg.danger.light
          }
        />
        <MdBoldLabel
          className={
            change >= 0
              ? 'text-fg-success-light dark:text-fg-success-dark'
              : 'text-fg-danger-light dark:text-fg-danger-dark'
          }
        >
          {Math.abs(change).toFixed(2)}%
        </MdBoldLabel>
      </View>
    </View>
  );
};

export default MetricDisplay;
