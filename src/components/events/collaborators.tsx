import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { Image, Pressable, Text, View } from '@/components/ui';

export default function CollaboratorCard({
  artist,
  onRemove,
}: {
  artist: { id: string; avatar: string; name: string };
  onRemove?: () => void;
}) {
  return (
    <View
      key={artist.id}
      className="relative h-[92px] w-[95px] gap-2.5 rounded-md"
    >
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0)']}
        locations={[0, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '100%',
          zIndex: 5,
          borderRadius: 4,
        }}
      />
      <Pressable onPress={onRemove} className="absolute -right-1 -top-1 z-10">
        <View className="size-6 items-center justify-center rounded-full bg-bg-danger-primary dark:bg-bg-danger-primary">
          <View className="size-4 items-center justify-center rounded-full border-2 border-accent-on-accent dark:border-accent-on-accent">
            <Ionicons name="close" size={12} color="#fff" />
          </View>
        </View>
      </Pressable>
      <View className="flex-1 items-center justify-center gap-2.5">
        <Image
          source={{
            uri: artist.avatar || require('~/assets/images/avatar.png'),
          }}
          className="size-[51px] rounded-full"
        />
        <Text
          className="text-md text-white"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {artist.name}
        </Text>
      </View>
    </View>
  );
}
