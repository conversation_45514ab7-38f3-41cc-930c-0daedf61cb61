import { Ionicons } from '@expo/vector-icons';
import { type BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { type Point } from 'react-native-google-places-autocomplete';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { toast } from 'sonner-native';
import { z } from 'zod';

import { useEditEvent } from '@/api/events';
import { EventFormat, type ISingleEvent } from '@/api/events/types';
import { IconComponent } from '@/components/common/icon-component';
import LocationInput from '@/components/input/location-autocomplete';
import {
  BottomSheetInput,
  Button,
  colors,
  ControlledBottomSheetInputWithoutContext,
  H2,
  Image,
  Modal,
  P,
  Pressable,
  Text,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import {
  useAuth,
  useFieldBlurAndFilled,
  usePurchaseTicketContext,
} from '@/lib';
import { formatDateTime } from '@/lib/utils/formatDateTime';
import { Location, type LocationType } from '@/types';

const schema = z.object({
  description: z.string(),
  startTime: z.date(),
  endTime: z.date(),
  onlineEventUrl: z.string().url('Invalid URL').optional(),
  location: Location.optional(),
  collaborators: z
    .array(z.object({ id: z.string(), name: z.string(), avatar: z.string() }))
    .default([]),
});

export type EventEditModalProps = {
  event: ISingleEvent;
  onPresent: () => void;
  onDismiss: () => void;
  ref: React.RefObject<BottomSheetModal | null>;
};

type EditEventType = z.infer<typeof schema>;

export function EventEditModal({ event, onDismiss, ref }: EventEditModalProps) {
  const queryClient = useQueryClient();
  const user = useAuth.use.user();
  const { handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<EditEventType>([
      'description',
      'startTime',
      'endTime',
      'collaborators',
    ]);
  const { editType } = usePurchaseTicketContext();
  const onlineModal = useModal();
  const customLinkModal = useModal();
  const generatingLinkModal = useModal();

  const { control, watch, setValue } = useForm<z.infer<typeof schema>>({
    defaultValues: {
      description: event?.description,
      ...(event?.startTime && { startTime: new Date(event?.startTime) }),
      ...(event?.endTime && { endTime: new Date(event?.endTime) }),
      collaborators: event?.artists?.map(
        ({ id, artistName: name, avatar }) => ({ id, name, avatar })
      ),
      location: event?.location,
      onlineEventUrl: event?.onlineEventUrl,
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  const eventUrl = watch('onlineEventUrl');

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const getZoomLink = () => {
    onlineModal.dismiss();
    generatingLinkModal.present();

    setTimeout(() => {
      generatingLinkModal.dismiss();
      // setTimeout(() => {
      //   router.push('/events/create/zoom-generated');
      // }, 300);
    }, 2000);
  };

  const isStartDateValid = (date: Date) => {
    const now = new Date();
    return date >= now;
  };

  const isEndDateValid = (startDate: Date, endDate: Date) => {
    return endDate >= startDate;
  };

  const [openStartDatetime, setOpenStartDatetime] = React.useState(false);
  const [openEndDatetime, setOpenEndDatetime] = React.useState(false);

  const startTime = watch('startTime');

  const startDatetimeLabel = startTime
    ? formatDateTime(startTime)
    : 'Start date and time';

  const endTime = watch('endTime');

  const endDatetimeLabel = endTime
    ? formatDateTime(endTime)
    : 'End date and time';

  const handleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`startTime`, date);
    setOpenStartDatetime(false);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch(`startTime`);
    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue(`endTime`, date);
    setOpenEndDatetime(false);
  };

  const { mutate: editEvent, isPending } = useEditEvent({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          'getEventWithSlug',
          { slug: event.slug, targetCurrency: 'NGN', userId: user?.id },
        ],
      });
      toast.success('Event updated successfully');
      onDismiss();
    },
    onError: (error) => toast.error(error.message),
  });

  const onSubmit = () => {
    const { collaborators: _collaborators, ...form } = watch();
    const formData = new FormData();
    Object.entries(form).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        let stringifiedValue = value as string;
        if (typeof value !== 'string') {
          stringifiedValue = JSON.stringify(value);
        }

        formData.append(key, stringifiedValue);
      }
    });
    editEvent({ form: formData, id: event.id });
  };

  return (
    <Modal ref={ref} index={0} onDismiss={onDismiss} enableDynamicSizing>
      <BottomSheetView className="min-h-[255px] flex-1 justify-between gap-6 p-4">
        <H2>
          Edit Event
          {editType === 'desc'
            ? ' Description'
            : editType === 'date'
              ? ' Date'
              : editType === 'location'
                ? ' Location'
                : ''}
        </H2>
        {editType === 'desc' ? (
          <ControlledBottomSheetInputWithoutContext
            name="description"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            handleFieldBlur={() => handleFieldBlur('description')}
            handleFieldUnBlur={() => handleFieldUnBlur('description')}
            style={{ height: 80 }}
            label="Describe your event"
            control={control}
          />
        ) : editType === 'date' ? (
          <>
            <Pressable
              testID="select-event-start-date-time-button"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={() => setOpenStartDatetime(true)}
            >
              <Text
                className={
                  startTime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {startDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openStartDatetime}
              mode="datetime"
              onConfirm={handleStartDateConfirm}
              onCancel={() => setOpenStartDatetime(false)}
              minimumDate={new Date()}
              date={startTime}
            />
            <Pressable
              testID="select-event-end-date-time-button"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={() => setOpenEndDatetime(true)}
            >
              <Text
                className={
                  endTime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {endDatetimeLabel}
              </Text>
            </Pressable>
            <DateTimePickerModal
              isVisible={openEndDatetime}
              mode="datetime"
              onConfirm={handleEndDateConfirm}
              onCancel={() => setOpenEndDatetime(false)}
              disabled={!startTime}
              minimumDate={startTime}
              date={endTime}
            />
          </>
        ) : editType === 'location' ? (
          <>
            {event.eventFormat !== EventFormat.ONLINE && (
              <LocationInput
                onSelectLocation={onSelectLocation}
                defaultValue={watch('location')}
                useBottomSheetInput
              />
            )}
            {event.eventFormat !== EventFormat.IN_PERSON && (
              <ControlledBottomSheetInputWithoutContext
                name="onlineEventUrl"
                label="Streaming link"
                control={control}
                onPress={() => onlineModal.present()}
                // @ts-ignore
                icon={
                  <Ionicons
                    name="link"
                    size={24}
                    className="text-fg-muted dark:text-fg-muted"
                  />
                }
              />
            )}
          </>
        ) : null}

        <Button
          variant="secondary"
          label="Done"
          loading={isPending}
          disabled={isPending}
          onPress={onSubmit}
          className="mb-4"
        />
      </BottomSheetView>

      <Modal ref={onlineModal.ref} snapPoints={['35%']}>
        <View className="gap-2 p-4">
          <H2 className="text-start">Online events</H2>
          <TouchableOpacity
            className="flex-row items-center justify-start gap-4 py-3"
            onPress={() => {
              onlineModal.dismiss();
              customLinkModal.present();
            }}
          >
            <View className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="feather" iconName="globe" size={24} />
            </View>
            <P className="flex-1 items-center justify-center">
              Use your own URL
            </P>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-start gap-4 py-3"
            onPress={getZoomLink}
          >
            <View className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="custom-svg" iconName="Zoom" size={24} />
            </View>
            <P className="flex-1 items-center justify-center">
              Generate a Zoom link
            </P>
          </TouchableOpacity>
          <Button
            label="Skip for now"
            variant="outline"
            onPress={() => onlineModal.dismiss()}
          />
        </View>
      </Modal>
      <Modal ref={customLinkModal.ref} snapPoints={['30%']}>
        <View className="w-full justify-start gap-2 p-4">
          <View className="flex-row justify-start gap-2 px-2 py-4">
            <TouchableOpacity
              className="size-8"
              onPress={() => {
                customLinkModal.dismiss();
                onlineModal.present();
              }}
            >
              <Ionicons
                name="chevron-back"
                size={24}
                color={colors.brand['60']}
                className="text-accent-moderate dark:text-accent-moderate"
              />
            </TouchableOpacity>
            <P className="font-bold">Enter event URL</P>
          </View>
          <View className="gap-6">
            <BottomSheetInput
              label="Enter link (URL)"
              value={eventUrl ? eventUrl : 'https://'}
              onChangeText={(value) => setValue('onlineEventUrl', value)}
            />

            <Button
              label="Save"
              disabled={!eventUrl}
              onPress={() => {
                customLinkModal.dismiss();
              }}
            />
          </View>
        </View>
      </Modal>
      <Modal ref={generatingLinkModal.ref} snapPoints={['40%']}>
        <View className="items-center justify-center gap-2 p-4">
          <View className="size-[125px]">
            <Image
              source={require('~/assets/icons/events/loading.png')}
              className="size-[105px] self-center p-2.5"
            />
          </View>
          <P className="items-center justify-center text-fg-muted-light dark:text-fg-muted-dark">
            Generating link...
          </P>
        </View>
      </Modal>
    </Modal>
  );
}
