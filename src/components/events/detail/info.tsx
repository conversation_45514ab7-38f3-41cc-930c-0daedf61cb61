import { Feather, FontAwesome6 } from '@expo/vector-icons';
import { isPast } from 'date-fns';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { EventFormat, EventType, type ISingleEvent } from '@/api/events';
import {
  Button,
  colors,
  H5,
  Image,
  KeyIcon,
  MapIcon,
  MdBoldLabel,
  Pencil,
  Pressable,
  semanticColors,
  Small,
  Tiny,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { cn, formatEventDateTime } from '@/lib';

interface Props {
  event?: ISingleEvent;
  isCreator: boolean;
  addToCalendar: () => void;
  openInMaps: (address: string) => void;
  handleEventEdit: (type: 'desc' | 'date' | 'location') => void;
}

export const EventInfo: React.FC<Props> = ({
  event,
  isCreator,
  addToCalendar,
  openInMaps,
  handleEventEdit,
}) => {
  const { push } = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const hasPhysicalLocation =
    event?.eventFormat !== EventFormat.ONLINE && !!event?.location;
  const hasEventEnded = event?.endTime ? isPast(event.endTime) : true;

  const dateTimeLabel = formatEventDateTime(event?.startTime, event?.endTime);
  const calendarColor = isDark ? colors.brand[40] : colors.brand[70];
  const pinColor = isDark ? colors.white : colors.grey[100];

  const renderLocationInfo = () => {
    if (hasPhysicalLocation && !event?.location) return null;

    if (event?.eventFormat === EventFormat.IN_PERSON) {
      const eventAddress =
        event?.location.landmark ||
        event?.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim();
      const { state, country } = event?.location;
      return (
        <View className="flex-1 gap-1">
          <H5 numberOfLines={1}>{eventAddress}</H5>
          <Tiny className="text-grey-60 dark:text-grey-50">
            {state}, {country}
          </Tiny>
        </View>
      );
    }

    if (event?.eventFormat === EventFormat.HYBRID) {
      const eventAddress =
        event?.location.landmark ||
        event?.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim();
      const { state, country } = event?.location;
      return (
        <>
          <View className="gap-1">
            <H5>{eventAddress}</H5>
            <Tiny className="text-grey-60 dark:text-grey-50">
              {state}, {country}
            </Tiny>
          </View>

          {isCreator && (
            <View className="gap-1">
              <H5>Event URL</H5>
              <Tiny className="text-grey-60 dark:text-grey-50">
                {event.onlineEventUrl}
              </Tiny>
            </View>
          )}
        </>
      );
    }

    return null;
  };

  return (
    <React.Fragment>
      {/* Access Code Row */}
      {event?.eventType === EventType.PRIVATE && isCreator && (
        <View className="flex-row justify-between gap-3 px-4">
          <View className="flex-row items-center gap-3">
            <KeyIcon />
            <View className="gap-1">
              <H5>Access code</H5>
              <Tiny className="text-grey-60 dark:text-grey-50">
                See list of access codes
              </Tiny>
            </View>
          </View>
          <Button
            label="View codes"
            size="xs"
            className="h-8"
            onPress={() => {
              push({
                pathname: '/events/[id]/access',
                params: { id: event.id, slug: event.slug, title: event.title },
              });
            }}
            variant="outline"
          />
        </View>
      )}
      {/* Date Row */}
      <View
        className={cn(
          'flex-row justify-between gap-3 px-4',
          !isCreator && 'flex-col gap-2'
        )}
      >
        <View className="flex-row items-center gap-3">
          <Feather name="calendar" size={24} color="white" />
          <View className="gap-1">
            <H5>{dateTimeLabel}</H5>
            {!isCreator && (
              <Tiny className="text-grey-60 dark:text-grey-50">
                Times are displayed in your local time zone.
              </Tiny>
            )}
          </View>
        </View>
        {isCreator ? (
          <Button
            label="Edit"
            className="w-[62px]"
            size="xs"
            iconPosition="right"
            onPress={() => {
              handleEventEdit('date');
            }}
            variant="outline"
            iconClassName="right-2"
            icon={
              <Pencil
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            }
          />
        ) : (
          !hasEventEnded && (
            <TouchableOpacity
              className="ml-9 w-[130px] flex-row items-center gap-1 rounded-[48px] border border-brand-70 p-2 pr-3 dark:border-brand-40"
              onPress={addToCalendar}
            >
              <FontAwesome6
                name="calendar-plus"
                size={16}
                color={calendarColor}
              />
              <XsBoldLabel className="text-brand-70 dark:text-brand-40">
                Add to Calendar
              </XsBoldLabel>
            </TouchableOpacity>
          )
        )}
      </View>

      {/* Location Row */}
      <View
        className={cn(
          'flex-row justify-between gap-3 px-4 items-center',
          !isCreator && 'flex-col gap-2 items-start'
        )}
      >
        <View className="flex-1 flex-row items-center gap-3">
          {event?.eventFormat !== EventFormat.ONLINE && (
            <Feather name="map-pin" size={24} color={pinColor} />
          )}
          <View className="flex-1 gap-2">{renderLocationInfo()}</View>
        </View>
        {isCreator ? (
          <Button
            label="Edit"
            className="w-[62px]"
            size="xs"
            iconPosition="right"
            onPress={() => {
              handleEventEdit('location');
            }}
            variant="outline"
            iconClassName="right-2"
            icon={
              <Pencil
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            }
          />
        ) : (
          <View className="flex-row gap-2">
            {event?.location && (
              <TouchableOpacity
                className="ml-9 w-[113px] flex-row items-center gap-1 rounded-[48px] border border-brand-70 p-2 pr-3 dark:border-brand-40"
                onPress={() => {
                  openInMaps(event.location.address);
                }}
              >
                <MapIcon color={calendarColor} />
                <XsBoldLabel className="text-brand-70 dark:text-brand-40">
                  View on map
                </XsBoldLabel>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Organizer Info */}
      {!isCreator && (
        <Pressable
          className="flex-1 flex-row gap-2 px-4"
          onPress={() =>
            push({
              pathname: '/users/[id]',
              params: { id: event?.organizerId || event?.organizer.id || '' },
            })
          }
        >
          <Image
            source={
              event?.organizer.profileImageUrl ||
              require('~/assets/images/avatar.png')
            }
            className="size-12 rounded-full"
            priority="high"
          />
          <View className="gap-2">
            <MdBoldLabel>{event?.organizer.username}</MdBoldLabel>
            <Small className="text-[#929497] dark:text-[#929497]">
              Organizer
            </Small>
          </View>
        </Pressable>
      )}
    </React.Fragment>
  );
};
