import React from 'react';

import { type TicketCategories } from '@/api/events';
import { Text, View, XsBoldLabel } from '@/components/ui';
import { Image } from '@/components/ui';
import { formatCurrency } from '@/lib/utils/formatCurrency';
import EventTicketMoreDropdown from './ticket-dropdown';

type TicketCategory = TicketCategories[keyof TicketCategories];

type EventTicketCardProps = {
  ticket: TicketCategory;
  index: number;
  category: string;
  onEdit: () => void;
  onDelete?: () => void;
  isDark: boolean;
};

function PresaleBadge() {
  return (
    <View className="absolute right-2 top-1 z-10 h-5 w-[84px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
      <XsBoldLabel className="text-xs text-green-60 dark:text-green-40">
        Presale incl.
      </XsBoldLabel>
    </View>
  );
}

export function EventTicketCard({
  ticket,
  category,
  onEdit,
  onDelete,
}: EventTicketCardProps) {
  const handleDropdownChange = (value: 'edit' | 'delete') => {
    if (value === 'edit') {
      onEdit();
    } else if (value === 'delete' && onDelete) {
      onDelete();
    }
  };

  return (
    <View className="relative h-[98px] flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
      <View className="flex-1 flex-row items-center gap-2">
        <Image
          source={require('~/assets/images/session/disc.png')}
          className="size-10"
          alt="Ticket"
        />
        <View className="gap-2">
          <Text className="font-bold text-fg-base-light dark:text-fg-base-dark">
            {category}
          </Text>
          <Text className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {formatCurrency(ticket.cost)}
          </Text>
          {ticket.description && (
            <Text className="text-fg-subtle-light dark:text-fg-subtle-dark">
              {ticket.description.length > 30
                ? ticket.description.slice(0, 30) + '...'
                : ticket.description}
            </Text>
          )}
        </View>
      </View>
      {ticket.hasPresale && <PresaleBadge />}

      <View className="relative flex-row items-center gap-2">
        <EventTicketMoreDropdown
          onValueChange={handleDropdownChange}
          triggerClassName="size-6 bg-transparent dark:bg-transparent border-transparent"
          itemClassName="native:px-4 native:py-2.5 justify-between h-11"
        />
      </View>
    </View>
  );
}
