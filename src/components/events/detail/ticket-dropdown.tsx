import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { colors, DeleteIcon, semanticColors } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/picker';
import { cn } from '@/lib';

type DropdownOptions = 'edit' | 'delete';

const dropdownOptions: { label: string; value: DropdownOptions }[] = [
  { label: 'Edit ticket', value: 'edit' },
  { label: 'Delete ticket', value: 'delete' },
];

export interface EventTicketMoreDropdownProps {
  onValueChange: (value: DropdownOptions) => void;
  value?: DropdownOptions;
  disabled?: boolean;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  itemTextClassName?: string;
  contentInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

const EventTicketMoreDropdown: React.FC<EventTicketMoreDropdownProps> = ({
  onValueChange,
  disabled = false,
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-grey-100 dark:text-white',
  contentInsets,
}) => {
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const defaultContentInsets = {
    top: insets.top,
    bottom: insets.bottom + 8,
    right: 8,
    ...contentInsets,
  };

  const renderOptionIcon = (value: DropdownOptions) => {
    switch (value) {
      case 'edit':
        return (
          <Feather
            name="edit"
            size={16}
            color={
              isDark
                ? semanticColors.fg.base.dark
                : semanticColors.fg.base.light
            }
          />
        );
      case 'delete':
        return (
          <DeleteIcon
            color={
              isDark
                ? semanticColors.fg.danger.dark
                : semanticColors.fg.danger.light
            }
          />
        );
      default:
        return null;
    }
  };

  return (
    <Select
      onValueChange={(option) => {
        if (option && !disabled) {
          onValueChange(option.value as DropdownOptions);
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          'native:size-8 rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark native:items-center native:justify-center p-0',
          disabled && 'opacity-50',
          triggerClassName
        )}
        hideChevron
      >
        <Feather
          name="more-horizontal"
          size={24}
          color={isDark ? colors.white : colors.grey[100]}
        />
      </SelectTrigger>

      <SelectContent
        insets={defaultContentInsets}
        className={cn(
          'w-[220px] bg-black/45 border-0 dark:bg-black/70 rounded-lg',
          contentClassName
        )}
        // position="item-aligned"
      >
        <SelectGroup>
          {dropdownOptions.map(({ label, value }) => (
            <SelectItem
              key={value}
              label={label}
              value={value}
              textClassName={cn(
                'native:text-fg-base-light native:dark:text-fg-base-dark',
                value === 'delete' &&
                  'native:text-fg-danger-light native:dark:text-fg-danger-dark',
                itemTextClassName
              )}
              className={itemClassName}
              hasCheck={false}
              customIcon={renderOptionIcon(value as DropdownOptions)}
            >
              {label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default EventTicketMoreDropdown;
