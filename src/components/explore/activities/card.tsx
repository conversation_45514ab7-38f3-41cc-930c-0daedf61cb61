import * as React from 'react';

import { Image, Tiny, View } from '@/components/ui';
import { cn } from '@/lib';

type Props = {
  category: string;
  className?: string;
  image?: string;
};

export const ActivityCard = ({ category, className, image }: Props) => {
  return (
    <View className={cn('gap-y-2 items-center w-[136px]', className)}>
      <Image
        source={image || require('~/assets/images/explore/thing.png')}
        className="size-[136px] rounded-md"
      />
      <Tiny className="font-bold" numberOfLines={1}>
        {category}
      </Tiny>
    </View>
  );
};
