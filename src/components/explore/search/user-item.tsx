import React from 'react';

import { type UserObjectData } from '@/api/auth';
import { type DJProfile } from '@/api/session';
import {
  UserActionModal,
  type UserActionModalRef,
} from '@/components/modals/quester-action-modal';
import {
  Image,
  MdRegularLabel,
  SmRegularLabel,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { cn } from '@/lib';

interface SearchListItemProps {
  user: UserObjectData | DJProfile;
  className?: string;
  isLive?: boolean;
}
export const SearchListItem: React.FC<SearchListItemProps> = ({
  user,
  isLive,
  className,
}) => {
  const userActionModalRef = React.useRef<UserActionModalRef>(null);

  const handleMorePress = React.useCallback(() => {
    userActionModalRef.current?.present();
  }, []);

  return (
    <React.Fragment>
      <TouchableOpacity
        className={cn(
          'h-16 flex-row items-center gap-x-4',
          isLive && 'gap-x-2',
          className
        )}
        onPress={!isLive ? handleMorePress : () => {}}
      >
        <Image
          className={cn('size-10 rounded-full', isLive && 'size-12')}
          source={user.profileImageUrl || require('~/assets/images/avatar.png')}
          priority="high"
        />
        <View className="flex-1 flex-row gap-2">
          <View className={cn('gap-2.5', isLive && 'gap-2')}>
            <MdRegularLabel>{user.fullName}</MdRegularLabel>
            <SmRegularLabel className="text-grey-50 dark:text-grey-60">
              {user.username}
            </SmRegularLabel>
          </View>

          {isLive && (
            <View className="h-4 flex-row items-center justify-center gap-1">
              <View className="size-2.5 rounded-full bg-fg-success-light dark:bg-fg-success-dark" />
              <XsBoldLabel className="text-fg-success-light dark:text-fg-success-dark">
                Live
              </XsBoldLabel>
            </View>
          )}
        </View>
      </TouchableOpacity>
      <UserActionModal ref={userActionModalRef} user={user as UserObjectData} />
    </React.Fragment>
  );
};
