import React from 'react';
import { useFormContext } from 'react-hook-form';

import { ControlledInput } from '@/components/ui';
import { type PurchaseTicketFormType } from '@/lib/hooks/use-purchase-ticket';

export const AddDiscountForm = () => {
  const { control } = useFormContext<PurchaseTicketFormType>();

  return (
    <ControlledInput
      testID="add-discount-input"
      control={control}
      name="discountCode"
      label="Discount code"
    />
  );
};
