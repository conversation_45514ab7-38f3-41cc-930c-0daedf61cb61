import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { useGuessNuban, useVerifyNuban } from '@/api/transactions';
import {
  ActivityIndicator,
  colors,
  ControlledInput,
  Modal,
  P,
  Pressable,
  SearchInput,
  Text,
  useModal,
  View,
} from '@/components/ui';
import {
  type BankFormType as BankSetupFormType,
  useFieldBlurAndFilled,
} from '@/lib';

export type BankFormProps = {};

export const BankForm = () => {
  const { watch, control, setValue, reset } =
    useFormContext<BankSetupFormType>();

  const { handleFieldBlur, handleFieldUnBlur, fieldStates } =
    useFieldBlurAndFilled<BankSetupFormType>(['bankName', 'accountNumber']);
  const bankModal = useModal();
  const [searchQuery, setSearchQuery] = React.useState('');

  const isAcctNoFieldDirty = fieldStates.accountNumber.isDirty;
  const selectedBank = watch('bankName');
  const accountNumber = watch('accountNumber');
  const bankCode = watch('bankCode');

  const { data, isLoading: isVerifyingNuban } = useVerifyNuban({
    variables: {
      bankCode: bankCode,
      accountNumber,
    },
    enabled: accountNumber.length === 10 && !!bankCode && isAcctNoFieldDirty,
  });
  const accountDetails = data?.[0];

  React.useEffect(() => {
    setValue('accountName', accountDetails?.account_name || '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountDetails]);

  React.useEffect(() => {
    if (isAcctNoFieldDirty) {
      setValue('accountName', '');
      setValue('bankName', '');
      setValue('bankCode', '');
    } else {
      reset(undefined, { keepDirtyValues: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAcctNoFieldDirty]);

  const { data: possibleBanks, isLoading: isGuessingBanks } = useGuessNuban({
    variables: { accountNumber },
    enabled: accountNumber.length === 10,
  });

  const filteredBanks = (possibleBanks ?? []).filter((bank) =>
    bank.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectBank = React.useCallback(
    (bank: { label: string; value: string; code: string }) => {
      setValue('bankName', bank.value);
      setValue('bankCode', bank.code);
      handleFieldBlur('bankName');
      bankModal.dismiss();
    },
    [setValue, handleFieldBlur, bankModal]
  );
  const bankLabel = possibleBanks?.find(
    (bank) => bank.name === selectedBank
  )?.name;

  return (
    <>
      <View className="gap-4">
        <ControlledInput
          testID="accountNumber-input"
          control={control}
          name="accountNumber"
          label="Account number"
          handleFieldBlur={() => handleFieldBlur('accountNumber')}
          handleFieldUnBlur={() => handleFieldUnBlur('accountNumber')}
          keyboardType="number-pad"
          maxLength={10}
        />
        <View>
          {isGuessingBanks ? (
            <ActivityIndicator color={colors.brand[60]} />
          ) : (possibleBanks && possibleBanks.length > 0) || selectedBank ? (
            <Pressable
              testID="bankName-input"
              className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              onPress={bankModal.present}
              disabled={!isAcctNoFieldDirty}
            >
              <View className="flex-row items-center gap-2">
                {/* {selectedBank && (
                  <Image
                    source={require('~/assets/icon.png')}
                    className="size-9 rounded-xl"
                  />
                )} */}
                <Text
                  className={
                    bankLabel
                      ? 'dark:text-neutral-100'
                      : 'text-neutral-400 dark:text-neutral-500'
                  }
                >
                  {bankLabel ?? 'Bank'}
                </Text>
              </View>
              <Ionicons name="chevron-down" size={24} color={colors.grey[70]} />
            </Pressable>
          ) : null}
        </View>
        {isVerifyingNuban ? (
          <ActivityIndicator color={colors.brand[60]} />
        ) : (
          <P>{watch('accountName')}</P>
        )}
      </View>

      <Modal ref={bankModal.ref} snapPoints={['75%']}>
        <View className="px-4 pb-4">
          <SearchInput
            placeholder="Find a bank"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />

          {filteredBanks?.map((bank) => (
            <Pressable
              key={bank.name}
              className="flex-row items-center border-b border-neutral-300 dark:border-neutral-700"
              onPress={() =>
                selectBank({
                  label: bank.name,
                  value: bank.name,
                  code: bank.bank_code,
                })
              }
            >
              {/* <View className="p-2">
                <Image
                  source={require('~/assets/icon.png')}
                  className="size-10 gap-2 rounded-xl"
                />
              </View> */}
              <Text className="flex-1 py-4 dark:text-neutral-100">
                {bank.name}
              </Text>
              {selectedBank === bank.name && (
                <Ionicons
                  name="checkmark"
                  size={20}
                  color={colors.brand['60']}
                />
              )}
            </Pressable>
          ))}
        </View>
      </Modal>
    </>
  );
};
