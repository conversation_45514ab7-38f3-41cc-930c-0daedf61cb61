import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  ActivityIndicator,
  AtIcon,
  colors,
  ControlledInput,
  MessageFilledIcon,
  P,
  UserFilledIcon,
  View,
} from '@/components/ui';
import { type EditAccountFormType, useLoggedInUser } from '@/lib';
import { useAccountSetup } from '@/lib/contexts/account-setup-context';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';

export type EditUserDetailsFormProps = {};

export const EditUserDetailsForm = () => {
  const { usernameRefinement, isValidatingUsername, setIsValidatingUsername } =
    useAccountSetup();
  const { control, watch } = useFormContext<EditAccountFormType>();

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<EditAccountFormType>(['username']);

  const { data: user } = useLoggedInUser();

  const username = watch('username');

  const UsernameStatusIndicator = () => {
    const originalUsername = user?.username;

    const hasChanged = username && username !== originalUsername;

    if (!hasChanged && username.trim().length < 3) return null;
    return (
      <View className="flex flex-row items-center">
        {isValidatingUsername ? (
          <ActivityIndicator size="small" color={colors.brand['60']} />
        ) : (
          <P>{fieldStates.username.isValid ? '✅' : '❌'}</P>
        )}
        <P className="ml-1 text-grey-50 dark:text-grey-60">
          {isValidatingUsername
            ? 'Checking username'
            : fieldStates.username.isValid
              ? 'Username available'
              : 'Username unavailable'}
        </P>
      </View>
    );
  };

  return (
    <View className="flex-1 gap-4 px-4">
      <ControlledInput
        testID="firstName-input"
        control={control}
        name="firstName"
        readOnly
        label="First name"
        icon={<UserFilledIcon color={colors.brand['60']} />}
      />
      <ControlledInput
        testID="lastName-input"
        control={control}
        readOnly
        name="lastName"
        label="Last name"
        icon={<UserFilledIcon color={colors.brand['60']} />}
      />
      <ControlledInput
        testID="email-input"
        control={control}
        name="email"
        readOnly
        label="Email"
        icon={<MessageFilledIcon color={colors.brand['60']} />}
      />
      <View>
        <ControlledInput
          testID="username-input"
          control={control}
          name="username"
          label="Username"
          handleFieldBlur={() => handleFieldBlur('username')}
          handleFieldUnBlur={() => handleFieldUnBlur('username')}
          hideErrorMessage={
            fieldStates.username.error === 'Username is already taken'
          }
          onChange={() => {
            setIsValidatingUsername(true);
            usernameRefinement.invalidate();
          }}
          icon={<AtIcon color={colors.brand['60']} />}
        />
        <UsernameStatusIndicator />
      </View>
    </View>
  );
};
