import React from 'react';
import { useFormContext } from 'react-hook-form';

import { type FormType as ForgotPasswordFormType } from '@/app/forgot-password';
import {
  colors,
  ControlledInput,
  MessageFilledIcon,
  MessageIcon,
} from '@/components/ui';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';

export const ForgotPasswordForm = () => {
  const { control } = useFormContext<ForgotPasswordFormType>();

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<ForgotPasswordFormType>(['email']);

  return (
    <ControlledInput
      testID="email-input"
      control={control}
      name="email"
      label="Email"
      handleFieldBlur={() => handleFieldBlur('email')}
      handleFieldUnBlur={() => handleFieldUnBlur('email')}
      // @ts-ignore
      icon={
        fieldStates.email.isFilledAndBlurred ? (
          <MessageFilledIcon color={colors.brand['60']} />
        ) : (
          <MessageIcon color={colors.brand['60']} />
        )
      }
    />
  );
};
