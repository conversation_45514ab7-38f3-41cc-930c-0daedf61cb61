import { Link } from 'expo-router';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { type FormType as LoginFormType } from '@/app/login';
import {
  colors,
  ControlledInput,
  MdBoldLabel,
  MessageFilledIcon,
  MessageIcon,
  PasswordFilledIcon,
  PasswordIcon,
  Pressable,
  View,
} from '@/components/ui';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';

export type LoginFormProps = {};

export const LoginForm = () => {
  const { control } = useFormContext<LoginFormType>();

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<LoginFormType>(['email', 'password']);

  return (
    <View className="gap-4">
      <ControlledInput
        testID="email-input"
        control={control}
        name="email"
        label="Email"
        handleFieldBlur={() => handleFieldBlur('email')}
        handleFieldUnBlur={() => handleFieldUnBlur('email')}
        // @ts-ignore
        icon={
          fieldStates.email.isFilledAndBlurred ? (
            <MessageFilledIcon color={colors.brand['60']} />
          ) : (
            <MessageIcon color={colors.brand['60']} />
          )
        }
      />
      <ControlledInput
        testID="password-input"
        control={control}
        name="password"
        label="Password"
        handleFieldBlur={() => handleFieldBlur('password')}
        handleFieldUnBlur={() => handleFieldUnBlur('password')}
        isPassword
        // @ts-ignore
        icon={
          fieldStates.password.isFilledAndBlurred ? (
            <PasswordFilledIcon color={colors.brand['60']} />
          ) : (
            <PasswordIcon color={colors.brand['60']} />
          )
        }
      />
      <Link href="/forgot-password" asChild>
        <Pressable>
          <MdBoldLabel className="text-accent-moderate dark:text-accent-moderate">
            Forgot password?
          </MdBoldLabel>
        </Pressable>
      </Link>
    </View>
  );
};
