import { useFormContext } from 'react-hook-form';

import { ControlledInput, View } from '@/components/ui';
import { type EditAccountFormType } from '@/lib';
import { useFieldBlurAndFilled } from '@/lib/hooks/use-field-blur-and-filled';

import { IconComponent } from '../common/icon-component';

export type SocialMediaFormProps = {};

export const SocialMediaForm = () => {
  const { control } = useFormContext<EditAccountFormType>();

  const { handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<EditAccountFormType>([
      'socials.instagram',
      'socials.x',
      'socials.snapchat',
      'socials.facebook',
      'socials.tiktok',
      'socials.youtube',
    ]);

  return (
    <View className="flex-1 gap-4 px-4">
      <ControlledInput
        testID="instagram-input"
        control={control}
        name="socials.instagram"
        label="Instagram"
        handleFieldBlur={() => handleFieldBlur('socials.instagram')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.instagram')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="entypo"
            iconName="instagram"
            size={24}
            color="#bc2a8d"
          />
        }
      />
      <ControlledInput
        testID="x-input"
        control={control}
        name="socials.x"
        label="X"
        handleFieldBlur={() => handleFieldBlur('socials.x')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.x')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="font-awesome-6"
            iconName="square-x-twitter"
            size={24}
            color="#14171a"
          />
        }
      />
      <ControlledInput
        testID="snapchat-input"
        control={control}
        name="socials.snapchat"
        label="Snapchat"
        handleFieldBlur={() => handleFieldBlur('socials.snapchat')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.snapchat')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="material-community"
            iconName="snapchat"
            size={24}
            color={'#fffc00'}
          />
        }
      />
      <ControlledInput
        testID="facebook-input"
        control={control}
        name="socials.facebook"
        label="Facebook"
        handleFieldBlur={() => handleFieldBlur('socials.facebook')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.facebook')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="ionicons"
            iconName="logo-facebook"
            size={24}
            color={'#1877F2'}
          />
        }
      />
      <ControlledInput
        testID="tiktok-input"
        control={control}
        name="socials.tiktok"
        label="TikTok"
        handleFieldBlur={() => handleFieldBlur('socials.tiktok')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.tiktok')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="ionicons"
            iconName="logo-tiktok"
            size={24}
            color={'#000000'}
          />
        }
      />
      <ControlledInput
        testID="youtube-input"
        control={control}
        name="socials.youtube"
        label="YouTube"
        handleFieldBlur={() => handleFieldBlur('socials.youtube')}
        handleFieldUnBlur={() => handleFieldUnBlur('socials.youtube')}
        // @ts-ignore
        icon={
          <IconComponent
            iconType="entypo"
            iconName="youtube"
            size={24}
            color={'#FF0000'}
          />
        }
      />
    </View>
  );
};
