import { useColorScheme } from 'nativewind';
import { StyleSheet } from 'react-native';

import {
  colors,
  Image,
  Tick,
  Tiny,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { cn } from '@/lib';

interface GenreProps {
  genre: string;
  imageSource: string;
  isSelected: boolean;
  onToggle: () => void;
}

const GenreItem = ({
  genre,
  imageSource,
  isSelected,
  onToggle,
}: GenreProps) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <TouchableOpacity
      className="items-center gap-2"
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <View
        className={cn(
          'relative size-[74px] justify-center items-center overflow-hidden rounded-full',
          isSelected && 'border-2 border-brand-60'
        )}
      >
        {isSelected && (
          <View className="absolute z-50 size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
            <Tick color={isDark ? '#B4A6FF' : '#5336E2'} />
          </View>
        )}
        {isSelected && <View className="z-40" style={styles.selectedBg} />}
        <Image source={imageSource} className="size-full" contentFit="cover" />
      </View>
      <Tiny className="text-grey-100 dark:text-white">{genre}</Tiny>
    </TouchableOpacity>
  );
};

export default GenreItem;

const styles = StyleSheet.create({
  selectedBg: {
    backgroundColor: colors.brand[50],
    ...StyleSheet.absoluteFillObject,
    opacity: 0.49,
  },
});
