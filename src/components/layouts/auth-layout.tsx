import React from 'react';
import { Keyboard, TouchableWithoutFeedback } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import {
  FocusAwareStatusBar,
  H1,
  P,
  SafeAreaView,
  View,
} from '@/components/ui';

import { Back } from '../common/back';
import { Close } from '../common/close';
import { Skip } from '../common/skip';
import DotStepIndicator, {
  type DotStepIndicatorProps,
} from '../pagination/dot-step-indicator';

type AuthLayoutProps = {
  title: string;
  subTitle?: string;
  skipLink?: string;
  showCloseButton?: boolean;
  onBackPress?: () => void;
  children: React.ReactNode;
  footer?: React.ReactNode;
  showBackButton?: boolean;
  pagination?: DotStepIndicatorProps;
};

export const AuthLayout = ({
  title,
  subTitle,
  skipLink,
  showCloseButton = false,
  showBackButton = true,
  onBackPress,
  children,
  footer,
  pagination,
}: AuthLayoutProps) => (
  <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark">
    <FocusAwareStatusBar />
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior="padding"
        keyboardVerticalOffset={10}
      >
        <View className="flex-1 px-4">
          <View className="flex flex-row items-center justify-between py-3">
            {showBackButton && <Back onBackPress={onBackPress} />}
            {pagination && (
              <DotStepIndicator
                activeStep={pagination.activeStep}
                totalSteps={pagination.totalSteps}
              />
            )}

            {skipLink && !showCloseButton && <Skip link={skipLink} />}
            {showCloseButton && !skipLink && <Close />}
            {!showCloseButton && !skipLink && <View className="size-8" />}
          </View>
          <View className="mb-4 justify-start">
            <H1>{title}</H1>
            {subTitle && (
              <P className="mb-4 mt-1 text-grey-60 dark:text-grey-50">
                {subTitle}
              </P>
            )}
          </View>

          {/* Main content */}
          <View className="flex-1">{children}</View>

          {/* Footer content that sticks to bottom */}
          {footer && <View className="mt-auto">{footer}</View>}
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  </SafeAreaView>
);
