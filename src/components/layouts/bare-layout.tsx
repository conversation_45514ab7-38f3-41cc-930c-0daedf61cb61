import React from 'react';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import {
  FocusAwareStatusBar,
  H1,
  P,
  SafeAreaView,
  View,
} from '@/components/ui';
import { cn } from '@/lib';

import { Back } from '../common/back';

type BareLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  contentClassName?: string;
};

export const BareLayout = ({
  title,
  subTitle,
  children,
  footer,
  contentClassName,
}: BareLayoutProps) => (
  <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
    <FocusAwareStatusBar />
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <View className="flex-1">
        <View className="flex flex-row items-center justify-between px-2 py-3">
          <Back />
        </View>
        <View className="mx-4 justify-start gap-1 pb-4">
          {title && <H1>{title}</H1>}
          {subTitle && (
            <P className="text-grey-60 dark:text-grey-50">{subTitle}</P>
          )}
        </View>

        {/* Main content */}
        <View className={cn('flex-1 gap-4 p-4', contentClassName)}>
          {children}
        </View>

        {/* Footer content that sticks to bottom */}
        {footer && <View className="mt-auto">{footer}</View>}
      </View>
    </KeyboardAvoidingView>
  </SafeAreaView>
);
