import { useRouter } from 'expo-router';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import {
  FocusAwareStatusBar,
  H1,
  P,
  SafeAreaView,
  semanticColors,
  View,
} from '@/components/ui';

import { Back } from '../common/back';
import { IconComponent } from '../common/icon-component';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  onBackPress?: () => void;
};

export const CreateEventLayout = ({
  title,
  subTitle,
  children,
  footer,
  onBackPress,
}: CreateEventLayoutProps) => {
  const router = useRouter();
  return (
    <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
      <FocusAwareStatusBar />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior="padding"
        keyboardVerticalOffset={10}
      >
        <View className="flex-1">
          <View className="flex flex-row items-center justify-between px-2 py-3">
            {onBackPress ? <Back onBackPress={onBackPress} /> : <Back />}
            <TouchableOpacity
              className="px-2"
              onPress={() => router.dismissTo('/(app)')}
            >
              <IconComponent
                iconType="entypo"
                iconName="home"
                size={24}
                color={semanticColors.accent.moderate}
              />
            </TouchableOpacity>
          </View>
          <View className="mx-4 justify-start gap-1 pb-4">
            {title && <H1>{title}</H1>}
            {subTitle && (
              <P className="text-grey-60 dark:text-grey-50">{subTitle}</P>
            )}
          </View>

          <View className="flex-1 gap-4 p-4">{children}</View>

          {footer && <View className="mt-auto">{footer}</View>}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
