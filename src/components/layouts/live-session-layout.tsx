import React from 'react';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { FocusAwareStatusBar, View } from '@/components/ui';
import { useKeyboardVisible } from '@/lib';

interface LiveSessionLayoutProps {
  children: React.ReactNode;
}

export const LiveSessionLayout: React.FC<LiveSessionLayoutProps> = ({
  children,
}) => {
  const insets = useSafeAreaInsets();
  const isKeyboardVisible = useKeyboardVisible();

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <View
        className="flex-1"
        style={{
          marginBottom: isKeyboardVisible ? 0 : insets.bottom,
        }}
      >
        <FocusAwareStatusBar />
        {children}
      </View>
    </KeyboardAvoidingView>
  );
};
