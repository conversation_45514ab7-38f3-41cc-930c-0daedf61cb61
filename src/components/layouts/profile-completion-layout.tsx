import React from 'react';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import {
  FocusAwareStatusBar,
  H1,
  P,
  SafeAreaView,
  View,
} from '@/components/ui';

import { Back } from '../common/back';
import { Close } from '../common/close';

type ProfileCompletionLayoutProps = {
  title: string;
  subTitle: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
};

export const ProfileCompletionLayout = ({
  title,
  subTitle,
  children,
  footer,
}: ProfileCompletionLayoutProps) => (
  <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
    <FocusAwareStatusBar />
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <View className="flex-1 px-4">
        <View className="flex flex-row items-center justify-between py-3">
          <Back />
          <View className="size-8" />
          <Close />
        </View>
        <View className="mb-4 justify-start">
          <H1>{title}</H1>
          {subTitle && (
            <P className="mb-4 mt-1 text-grey-60 dark:text-grey-50">
              {subTitle}
            </P>
          )}
        </View>

        {/* Main content */}
        <View className="flex-1">{children}</View>

        {/* Footer content that sticks to bottom */}
        {footer && <View className="mt-auto">{footer}</View>}
      </View>
    </KeyboardAvoidingView>
  </SafeAreaView>
);
