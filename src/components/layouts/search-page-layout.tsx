import React from 'react';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import { FocusAwareStatusBar, SafeAreaView, View } from '@/components/ui';

import { Back } from '../common/back';
import { SearchInput } from '../ui/search-input';

interface SearchPageLayoutProps {
  placeholder?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  searchValue?: string;
  onSearchChange?: (text: string) => void;
}

export const SearchPageLayout = ({
  placeholder,
  children,
  footer,
  searchValue = '',
  onSearchChange = () => {},
}: SearchPageLayoutProps) => (
  <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
    <FocusAwareStatusBar />
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <View className="flex-1">
        <View className="flex flex-row items-center justify-between px-2 pt-4">
          <Back />
          <View className="ml-2 flex-1">
            <SearchInput
              value={searchValue}
              onChangeText={onSearchChange}
              placeholder={placeholder}
              showClearButton={searchValue !== ''}
            />
          </View>
        </View>

        {/* Main content */}
        <View className="flex-1 gap-4 p-2">{children}</View>

        {/* Footer content that sticks to bottom */}
        {footer && <View className="mt-auto">{footer}</View>}
      </View>
    </KeyboardAvoidingView>
  </SafeAreaView>
);
