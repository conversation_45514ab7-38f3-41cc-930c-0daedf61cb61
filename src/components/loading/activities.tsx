import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

export const ActivitiesLoading: React.FC = () => (
  <View className="flex-1">
    <List
      data={Array.from({ length: 6 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={({ index }) => (
        <View className={cn('gap-y-2 items-center', index === 0 && 'ml-4')}>
          <Skeleton className="size-[136px] rounded-md" />
          <Skeleton className="h-3 w-[74px]" />
        </View>
      )}
      horizontal
      showsHorizontalScrollIndicator={false}
      estimatedItemSize={6}
      keyExtractor={(item) => item.id}
      ItemSeparatorComponent={() => <View className="size-4 " />}
    />
  </View>
);
