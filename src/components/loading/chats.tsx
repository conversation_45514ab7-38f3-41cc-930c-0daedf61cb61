import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

interface Props {
  className?: string;
}
export const ChatsLoading: React.FC<Props> = ({ className }) => (
  <View className={cn('flex-1 px-4', className)}>
    <List
      data={Array.from({ length: 10 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={() => (
        <View className="h-[62px] flex-row items-center gap-x-3.5">
          <Skeleton className="size-10 rounded-full" />
          <View className="flex-1 gap-1">
            <Skeleton className="h-4 w-24 rounded-sm" />
            <Skeleton className="h-3 w-48 rounded-sm" />
          </View>
          <Skeleton className="h-3 w-10 rounded-sm" />
        </View>
      )}
      estimatedItemSize={10}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
    />
  </View>
);
