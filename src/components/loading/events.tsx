import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

interface Props {
  className?: string;
}
export const EventsLoading: React.FC<Props> = ({ className }) => (
  <View className={cn('flex-1 gap-4 py-4', className)}>
    <List
      data={Array.from({ length: 10 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={({ index }) => (
        <Skeleton
          className={cn(
            'h-[300px] rounded-lg flex-1',
            index % 2 === 0 ? 'mr-2' : 'ml-2'
          )}
        />
      )}
      estimatedItemSize={10}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={() => <View className="size-4 " />}
      numColumns={2}
    />
  </View>
);
