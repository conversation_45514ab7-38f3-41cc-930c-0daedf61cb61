import React from 'react';
import { ActivityIndicator, View } from 'react-native';

import { colors, FocusAwareStatusBar, SafeAreaView } from '../ui';

const LoadingScreen = () => {
  return (
    <SafeAreaView className="flex-1">
      <FocusAwareStatusBar
        defaultStatusBar
        backgroundColor="transparent"
        translucent={false}
      />
      <View className="flex-1 justify-center">
        <ActivityIndicator color={colors.brand[60]} size="large" />
      </View>
    </SafeAreaView>
  );
};

export default LoadingScreen;
