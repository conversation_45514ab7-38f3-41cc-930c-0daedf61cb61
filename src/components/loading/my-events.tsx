import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

interface Props {
  className?: string;
  itemClassName?: string;
}
export const MyEventsLoading: React.FC<Props> = ({
  className,
  itemClassName,
}) => (
  <View className={cn('flex-1 gap-4', className)}>
    <List
      data={Array.from({ length: 10 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={() => (
        <Skeleton
          className={cn('mx-2 h-[152px] flex-1 rounded-lg', itemClassName)}
        />
      )}
      estimatedItemSize={152}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={() => <View className="size-4 " />}
    />
  </View>
);
