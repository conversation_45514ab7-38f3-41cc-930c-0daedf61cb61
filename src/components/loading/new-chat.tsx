import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

interface Props {
  className?: string;
}
export const NewChatLoading: React.FC<Props> = ({ className }) => (
  <View className={cn('flex-1 mt-4 mx-4', className)}>
    <List
      data={Array.from({ length: 10 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={() => (
        <View className="h-16 flex-row items-center gap-x-4">
          <Skeleton className="size-10 rounded-full" />
          <View className="w-1/2 gap-2.5">
            <Skeleton className="h-4 w-full rounded-sm" />
            <Skeleton className="h-3 w-3/4 rounded-sm" />
          </View>
        </View>
      )}
      estimatedItemSize={10}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
    />
  </View>
);
