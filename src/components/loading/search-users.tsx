import { List, Skeleton, View } from '../ui';

export const SearchUsersLoading: React.FC<{ numColumns: number }> = ({
  numColumns,
}) => (
  <View className="flex-1 p-4">
    <List
      data={Array.from({ length: 10 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={() => (
        <View className="h-[90px] items-center justify-center gap-3.5">
          <Skeleton className="size-10 rounded-full" />
          <Skeleton className="h-3.5 w-[74px]" />
        </View>
      )}
      estimatedItemSize={10}
      keyExtractor={(item) => item.id}
      ItemSeparatorComponent={() => <View className="size-4 " />}
      numColumns={numColumns}
    />
  </View>
);
