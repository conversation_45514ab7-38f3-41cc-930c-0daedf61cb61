import { cn } from '@/lib';

import { List, Skeleton, View } from '../ui';

interface Props {
  className?: string;
}
export const TrendingEventsLoading: React.FC<Props> = ({ className }) => (
  <View className={cn('flex-1 gap-4 p-4', className)}>
    <List
      data={Array.from({ length: 2 }, (_, index) => ({
        id: index.toString(),
      }))}
      renderItem={() => <Skeleton className="h-[278px] flex-1 rounded-lg" />}
      estimatedItemSize={2}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={() => <View className="size-4 " />}
    />
  </View>
);
