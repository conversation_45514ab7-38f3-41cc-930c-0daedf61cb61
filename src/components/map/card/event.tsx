import * as React from 'react';

import { type Point } from '@/api/auth';
import { type IEvent } from '@/api/events';
import { Image, Pressable, Tiny, View, XsBoldLabel } from '@/components/ui';
import { cn, getDistanceFromLatLonInKm } from '@/lib';
import { type LocationType } from '@/types';

type Props = {
  event: IEvent;
  userLocation: LocationType;
  onSelectEvent: (coordinates: Point) => void;
  className?: string;
};

export const MapEventCard = ({
  event,
  userLocation,
  className,
  onSelectEvent,
}: Props) => {
  if (!event.location?.coordinates) return null;

  const dist = getDistanceFromLatLonInKm(
    userLocation.coordinates.lat,
    userLocation.coordinates.lng,
    event.location.coordinates.lat,
    event.location.coordinates.lng
  );
  const distanceText =
    dist < 1
      ? `${Math.round(dist * 1000)}m away`
      : `${Math.round(dist)}km away`;

  return (
    <Pressable
      className={cn('gap-y-3', className)}
      onPress={() => onSelectEvent(event.location.coordinates)}
    >
      <Image
        source={event.bannerUrl}
        className="size-[136px] rounded-lg"
        priority="high"
      />
      <View className="gap-y-1">
        <XsBoldLabel>{event.title}</XsBoldLabel>
        <Tiny className="text-fg-muted-light dark:text-fg-muted-dark">
          {distanceText}
        </Tiny>
      </View>
    </Pressable>
  );
};
