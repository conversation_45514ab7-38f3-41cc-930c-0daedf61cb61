import * as React from 'react';

import { type Point } from '@/api/auth';
import { type LiveSessionResponse } from '@/api/session';
import { LiveUserCard } from '@/components/cards/live-user-card';
import { Pressable, Tiny } from '@/components/ui';
import { cn, getDistanceFromLatLonInKm } from '@/lib';
import { type LocationType } from '@/types';

type Props = {
  session: LiveSessionResponse;
  userLocation: LocationType;
  index: number;
  handleJoinLiveSession: () => void;
  onSelectLiveSession: (coordinates: Point) => void;
  className?: string;
};

export const MapSessionCard = ({
  session,
  userLocation,
  index,
  onSelectLiveSession,
  handleJoinLiveSession,
  className,
}: Props) => {
  if (!session.location?.coordinates) return null;
  const dist = getDistanceFromLatLonInKm(
    userLocation.coordinates.lat,
    userLocation.coordinates.lng,
    session.location.coordinates.lat,
    session.location.coordinates.lng
  );
  const distanceText =
    dist < 1
      ? `${Math.round(dist * 1000)}m away`
      : `${Math.round(dist)}km away`;

  return (
    <Pressable
      className={cn('gap-y-1', className)}
      onPress={() => onSelectLiveSession(session.location.coordinates)}
    >
      <LiveUserCard
        status="LIVE"
        username={session.dj.username}
        avatar={session.dj.profileImageUrl}
        bgImage={
          index % 2 === 0
            ? require('~/assets/gradient-bg/gradient-card-bg.png')
            : require('~/assets/gradient-bg/gradient-card-bg-2.png')
        }
        className={cn('ml-0', index === 0 && 'ml-4')}
        onPress={handleJoinLiveSession}
      />
      <Tiny className="text-center text-fg-muted-light dark:text-fg-muted-dark">
        {distanceText}
      </Tiny>
    </Pressable>
  );
};
