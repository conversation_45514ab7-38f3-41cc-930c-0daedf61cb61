import React from 'react';
import { Image, View } from 'react-native';

import { Button, H2, P } from '../ui';

const AccountCreatedModal: React.FC<{
  visible: boolean;
  onClose: () => void;
}> = ({ visible = true, onClose }) => {
  if (!visible) return null;

  return (
    <View className="flex-1 items-center justify-center p-8">
      <View className="h-[357px] w-full items-center gap-6 rounded-lg bg-bg-canvas-light p-6 pt-8 dark:bg-bg-canvas-dark">
        <View className="size-[120px] rounded-full">
          <Image
            source={require('~/assets/images/success.png')}
            resizeMode="contain"
          />
        </View>
        <View className="w-[279px] items-center gap-2">
          <H2>Account Created</H2>
          <P className="text-center text-grey-60 dark:text-grey-50">
            Welcome onboard
          </P>
        </View>

        <Button label="Done" className="w-full" onPress={onClose} />
      </View>
    </View>
  );
};

export default AccountCreatedModal;
