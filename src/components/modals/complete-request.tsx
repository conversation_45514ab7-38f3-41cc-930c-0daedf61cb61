import { BottomSheetView } from '@gorhom/bottom-sheet';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ModalActions } from '../session/request/modal-actions';
import { ShoutoutMessageBox } from '../session/request/shoutout-box';
import { SongPreview } from '../session/request/song-preview';
import { WarningMessage } from '../session/request/warning';

interface CompletedRequestModalProps {
  title: string;
  subtitle?: string;
  iconSource?: string;
  shoutoutMessage?: string;
  warningMessage: string;
  onDismiss: () => void;
  onProceed: () => void;
  isPlaying: boolean;
}

export const CompletedRequestModal: React.FC<CompletedRequestModalProps> = ({
  title,
  subtitle,
  iconSource,
  shoutoutMessage,
  onDismiss,
  onProceed,
  warningMessage,
  isPlaying,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <BottomSheetView
      className="min-h-[311px] gap-4 px-4 pt-6"
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      {shoutoutMessage && <ShoutoutMessageBox message={shoutoutMessage} />}

      {title && (
        <SongPreview
          title={title}
          subtitle={subtitle}
          iconSource={iconSource}
        />
      )}

      <WarningMessage message={warningMessage} />

      <ModalActions
        primaryLabel="Proceed"
        secondaryLabel="Cancel"
        onPrimaryAction={onProceed}
        onSecondaryAction={onDismiss}
        isLoading={isPlaying}
      />
    </BottomSheetView>
  );
};
