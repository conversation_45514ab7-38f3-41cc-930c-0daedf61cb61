import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'react-native';

import { useAuth } from '@/lib';

import { Button, colors, H2, P } from '../ui';
import { useDeleteUser } from '@/api/user';

interface ConfirmDeleteAccountModalProps {
  visible: boolean;
  onClose: () => void;
}
const ConfirmDeleteAccountModal: React.FC<ConfirmDeleteAccountModalProps> = ({
  visible = true,
  onClose,
}) => {
  const router = useRouter();
  const user = useAuth.use.user();
  if (!user) return null;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const { mutate: deleteUser, isPending: deletingUser } = useDeleteUser();

  if (!visible) return null;

  return (
    <View className="relative flex-1 items-center justify-center">
      <BlurView
        tint={isDark ? 'systemMaterialDark' : 'extraLight'}
        intensity={isDark ? 90 : 70}
        style={styles.blurView}
      >
        <View style={styles.overlay} />
      </BlurView>

      <View className="w-full max-w-[327px] items-center gap-7 rounded-lg bg-transparent">
        <H2>Are you sure?</H2>
        <P className="text-grey-60 dark:text-gray-50">
          Your account will be permanently deleted.
        </P>

        {/* Button Group */}
        <View className="flex-row gap-2 px-5">
          <Button
            label="Cancel"
            className="flex-1 bg-brand-20 dark:bg-brand-90"
            textClassName="text-brand-70 dark:text-brand-40"
            onPress={onClose}
          />
          <Button
            label="Delete"
            loading={deletingUser}
            className="flex-1 bg-red-50"
            textClassName="text-white"
            onPress={() => {
              deleteUser({ id: user?.id });
              router.replace('/onboarding');
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default ConfirmDeleteAccountModal;

const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
