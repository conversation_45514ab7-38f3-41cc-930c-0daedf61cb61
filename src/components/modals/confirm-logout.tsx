import { useQueryClient } from '@tanstack/react-query';
import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'react-native';

import { useAuth } from '@/lib';

import { Button, colors, H2, P } from '../ui';

interface ConfirmLogoutModalProps {
  visible: boolean;
  onClose: () => void;
}
const ConfirmLogoutModal: React.FC<ConfirmLogoutModalProps> = ({
  visible = true,
  onClose,
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const signOut = useAuth.use.signOut();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  if (!visible) return null;

  return (
    <View className="relative flex-1 items-center justify-center">
      <BlurView
        tint={isDark ? 'systemMaterialDark' : 'extraLight'}
        intensity={isDark ? 90 : 70}
        style={styles.blurView}
      >
        <View style={styles.overlay} />
      </BlurView>

      <View className="w-full max-w-[327px] items-center gap-7 rounded-lg bg-transparent">
        <H2>Are you sure?</H2>
        <P className="text-grey-60 dark:text-gray-50">You will be logged out</P>

        {/* Button Group */}
        <View className="flex-row gap-2 px-5">
          <Button
            label="Cancel"
            className="flex-1 bg-brand-20 dark:bg-brand-90"
            textClassName="text-brand-70 dark:text-brand-40"
            onPress={onClose}
          />
          <Button
            label="Logout"
            className="flex-1 bg-red-50"
            textClassName="text-white"
            onPress={() => {
              signOut();
              queryClient.clear();
              router.replace('/onboarding');
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default ConfirmLogoutModal;

const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
