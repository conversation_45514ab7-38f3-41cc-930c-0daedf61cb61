import { BottomSheetView } from '@gorhom/bottom-sheet';
import React from 'react';
import { type Control, type UseFormSetValue } from 'react-hook-form';
import { View } from 'react-native';
import { type EdgeInsets } from 'react-native-safe-area-context';

import { Button, LgBoldLabel } from '@/components/ui';
import { type CreateLiveFormType } from '@/lib';

import { Back } from '../common/back';
import { CostSelectorWithBottomSheetInput } from '../session/create/cost-selector-with-bottom-sheet';

type CostSettingModalProps = {
  insets: EdgeInsets;
  control: Control<CreateLiveFormType>;
  fieldName: 'cost' | 'shoutoutCost';
  title: string;
  testID: string;
  onDismiss: () => void;
  setValue: UseFormSetValue<CreateLiveFormType>;
};

export const PRESET_COSTS = [0, 10000, 20000, 50000];

export const CostSettingModalContent: React.FC<CostSettingModalProps> = ({
  insets,
  control,
  fieldName,
  title,
  testID,
  onDismiss,
  setValue,
}) => {
  return (
    <BottomSheetView
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      <View className="flex-row items-center gap-2 px-2 py-4">
        <Back onBackPress={onDismiss} />
        <LgBoldLabel>{title}</LgBoldLabel>
      </View>

      <View className="gap-6 p-4">
        <CostSelectorWithBottomSheetInput
          control={control}
          setValue={setValue}
          name={fieldName}
          label={
            fieldName === 'cost' ? 'Cost per request' : 'Cost per shoutout'
          }
          costOptions={PRESET_COSTS}
          testID={testID}
        />

        <Button label="Continue" onPress={onDismiss} />
      </View>
    </BottomSheetView>
  );
};
