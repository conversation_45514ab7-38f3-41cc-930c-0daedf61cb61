import Ionicons from '@expo/vector-icons/Ionicons';
import React, { useCallback, useState } from 'react';
import { Modal } from 'react-native';
import { TextInput } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import {
  Button,
  colors,
  FocusAwareStatusBar,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from '@/components/ui';

interface EditBioModalProps {
  visible: boolean;
  onConfirm: (bio: string) => void;
  onCancel: () => void;
  bioText: string;
}

const BioEditModal: React.FC<EditBioModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  bioText,
}) => {
  const charLimit = 100;

  const [tempBio, setTempBio] = useState(bioText);

  const saveBio = useCallback(() => {
    onConfirm(tempBio);
  }, [tempBio, onConfirm]);

  return (
    <Modal visible={visible} transparent animationType="slide">
      <SafeAreaView className="flex-1 bg-bg-canvas-light dark:bg-grey-100">
        <FocusAwareStatusBar />
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior="padding"
          keyboardVerticalOffset={10}
        >
          <View className="flex-1 p-4">
            <View className="flex flex-row items-center justify-between py-3">
              <TouchableOpacity className="size-8" onPress={onCancel}>
                <Ionicons
                  name="chevron-back"
                  size={32}
                  color={colors.brand['70']}
                  className="text-fg-link"
                />
              </TouchableOpacity>
              <Text className="absolute inset-x-0 text-center text-lg font-bold">
                Bio
              </Text>
              <Button
                label="Save"
                onPress={saveBio}
                variant="secondary"
                size="modal"
                className="bg-accent-subtle-light font-bold text-accent-bold-light dark:bg-accent-subtle-dark dark:text-accent-bold-dark"
                disabled={tempBio === bioText}
              />
            </View>
            <TextInput
              className="flex-1 gap-4 px-2 text-lg/150 dark:text-white"
              multiline
              value={tempBio}
              onChangeText={setTempBio}
              placeholder="Write something"
              placeholderTextColor={colors.grey[70]}
              autoFocus
              maxLength={charLimit}
            />
            <View className="items-end p-4">
              <Text className="text-neutral-500">
                {charLimit - tempBio.length}
              </Text>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

export default BioEditModal;
