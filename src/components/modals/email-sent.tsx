import React from 'react';
import { Image, View } from 'react-native';

import { Button, H2, P } from '../ui';

const EmailCheckModal: React.FC<{ visible: boolean; onClose: () => void }> = ({
  visible = true,
  onClose,
}) => {
  if (!visible) return null;

  return (
    <View className="flex-1 items-center justify-center p-8">
      <View className="bg-bg-surface h-[357px] w-full items-center gap-6 rounded-lg p-6 pt-8 dark:bg-grey-80">
        {/* Email Icon */}
        <View className="size-[120px] rounded-full">
          <Image
            source={require('~/assets/images/email-message.png')}
            resizeMode="contain"
          />
        </View>

        {/* Text Content */}
        <View className="w-[279px] items-center gap-2">
          <H2>Check your inbox</H2>
          <P className="text-center text-grey-60 dark:text-grey-50">
            We sent you instructions to reset your email
          </P>
        </View>

        {/* Button */}
        <Button label="Okay" className="w-full" onPress={onClose} />
      </View>
    </View>
  );
};

export default EmailCheckModal;
