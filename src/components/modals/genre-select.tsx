import { BottomSheetView } from '@gorhom/bottom-sheet';
import React from 'react';

import { Button, H2, P, View } from '@/components/ui';

import { GenreOption } from '../session/create/genre-option';

type Genre = string;

type GenreSelectorModalProps = {
  insets: { bottom: number };
  selectedGenres: Genre[];
  onToggleGenre: (genre: Genre) => void;
  onDismiss: () => void;
};

export const AVAILABLE_GENRES = [
  'Anniversaries 🍰',
  'Webinar 📹',
  'Gaming 🎮',
  'Hip-Hop 🥁',
  'Afrobeats 🪘',
  'Entertainment 🎹',
  'Tutorials 🧑🏽‍🏫',
];

export const GenreSelectorModalContent: React.FC<GenreSelectorModalProps> = ({
  insets,
  selectedGenres,
  onToggleGenre,
  onDismiss,
}) => {
  return (
    <BottomSheetView
      className="min-h-[308px] gap-4 p-4"
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      <View className="gap-2">
        <H2>Live type</H2>
        <P className="text-fg-muted-light dark:text-fg-muted-dark">
          You can select up to three
        </P>
      </View>

      <View className="flex-row flex-wrap gap-x-4 gap-y-3">
        {AVAILABLE_GENRES.map((genre, index) => (
          <GenreOption
            key={index}
            option={genre}
            selected={selectedGenres.includes(genre)}
            onPress={onToggleGenre}
          />
        ))}
      </View>

      <Button label="Continue" onPress={onDismiss} />
    </BottomSheetView>
  );
};
