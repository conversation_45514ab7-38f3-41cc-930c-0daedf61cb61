import { BlurView } from 'expo-blur';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'react-native';

import { type ISingleEvent } from '@/api/events';
import { formatAmount, isPresaleActive, toAmountInMajor } from '@/lib';

import Counter from '../common/counter';
import { Button, colors, H2, H4, H6, Small, XsBoldLabel } from '../ui';

interface GetTicketModalProps {
  visible: boolean;
  onClose: () => void;
  handleTicketQuantityChange: (category: string, quantity: number) => void;
  hasSelectedTickets: boolean;
  selectedTickets: Record<string, number>;
  onProceed: () => void;
  event?: ISingleEvent;
  isLoading?: boolean;
}
const GetTicketModal: React.FC<GetTicketModalProps> = ({
  visible = true,
  onClose,
  event,
  handleTicketQuantityChange,
  hasSelectedTickets,
  selectedTickets,
  onProceed,
  isLoading,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  const ticketCategories = event?.ticketCategories || {};

  if (!visible) return null;

  return (
    <React.Fragment>
      <BlurView
        tint={isDark ? 'systemMaterialLight' : 'extraLight'}
        intensity={isDark ? 20 : 70}
        style={styles.blurView}
      >
        <View style={styles.overlay} />
      </BlurView>

      <View className="w-full gap-2 bg-transparent px-4 pb-4">
        <H2>Tickets</H2>
        <View>
          {Object.entries(ticketCategories).map(
            ([categoryKey, ticketCategory], index) => {
              const presaleTicket = event?.presaleConfig?.find(
                (presale) => presale.ticketCategoryId === ticketCategory.id
              );

              const shouldUsePresale =
                presaleTicket && isPresaleActive(presaleTicket);

              const activeTicket = shouldUsePresale
                ? {
                    ...ticketCategory,
                    cost: presaleTicket.price,
                    quantity: presaleTicket.quantity,
                    purchaseLimit:
                      presaleTicket.purchaseLimit ||
                      ticketCategory.purchaseLimit,
                    description:
                      presaleTicket.description || ticketCategory.description,
                  }
                : ticketCategory;

              return (
                <View key={index} className="flex-row items-start gap-6 py-4">
                  <View className="flex-1 gap-2">
                    <View className="flex-row items-center justify-between">
                      <H4>{categoryKey}</H4>
                      {shouldUsePresale && (
                        <View className="h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                          <XsBoldLabel className="text-green-60 dark:text-green-40">
                            Presale
                          </XsBoldLabel>
                        </View>
                      )}
                    </View>
                    <H6 className="text-fg-muted-light dark:text-fg-muted-dark">
                      {formatAmount(
                        Number(
                          toAmountInMajor(
                            activeTicket.convertedCost || activeTicket.cost
                          )
                        )
                      )}
                    </H6>
                    <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                      {activeTicket.description}
                    </Small>
                  </View>
                  <Counter
                    initialValue={selectedTickets[categoryKey] || 0}
                    minimum={1}
                    maximum={
                      activeTicket.quantity > 10 ? 10 : activeTicket.quantity
                    }
                    onValueChange={(quantity) =>
                      handleTicketQuantityChange(categoryKey, quantity)
                    }
                    className="border border-border-subtle-light dark:border-border-subtle-dark"
                  />
                </View>
              );
            }
          )}
        </View>

        {/* Button Group */}
        <Button
          label="Continue"
          disabled={!hasSelectedTickets || isLoading}
          loading={isLoading}
          onPress={onProceed}
        />
        <Button
          label="Cancel"
          variant="outline"
          disabled={isLoading}
          onPress={onClose}
        />
      </View>
    </React.Fragment>
  );
};

export default GetTicketModal;

const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
