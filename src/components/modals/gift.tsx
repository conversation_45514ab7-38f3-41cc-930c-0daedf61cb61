import { BottomSheetView } from '@gorhom/bottom-sheet';
import React from 'react';
import type { ImageSourcePropType } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  Image,
  LgBoldLabel,
  Pressable,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { GIFTS } from '@/lib';

interface GiftModalProps {
  onGiftSelect: (gift: { name: string; image: any }) => void;
}

const GiftItem: React.FC<{
  name: string;
  image: ImageSourcePropType;
  onPress: () => void;
}> = ({ name, image, onPress }) => (
  <Pressable className="items-center gap-2" onPress={onPress}>
    <Image source={image} className="size-[99px]" />
    <XsBoldLabel>{name}</XsBoldLabel>
  </Pressable>
);

export const GiftModal: React.FC<GiftModalProps> = ({ onGiftSelect }) => {
  const insets = useSafeAreaInsets();

  return (
    <BottomSheetView
      className="min-h-[521px] gap-1"
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      <View className="h-16 w-full items-start justify-center px-8">
        <LgBoldLabel>Send gift</LgBoldLabel>
      </View>
      <View className="mx-auto flex-row flex-wrap justify-center gap-x-8 gap-y-4 px-8">
        {GIFTS.map((gift, index) => (
          <GiftItem
            key={index}
            image={gift.image}
            name={gift.name}
            onPress={() => onGiftSelect(gift)}
          />
        ))}
      </View>
    </BottomSheetView>
  );
};
