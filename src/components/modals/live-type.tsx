import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { type UseFormSetValue } from 'react-hook-form';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { colors, H2, Music, ShoutoutIcon } from '@/components/ui';
import { type CreateLiveFormType } from '@/lib';

import { LiveTypeOption } from '../session/type/item';

interface LiveTypeModalContentProps {
  onSelectType: (type: 'REQUEST' | 'SHOUTOUT') => void;
  setValue: UseFormSetValue<CreateLiveFormType>;
  onDismiss: () => void;
}

export const LiveTypeModalContent: React.FC<LiveTypeModalContentProps> = ({
  onSelectType,
  setValue,
  onDismiss,
}) => {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleTypeSelection = (type: 'REQUEST' | 'SHOUTOUT') => {
    setValue('type', type);
    router.push('/session/create');
    onDismiss();
    onSelectType(type);
  };

  return (
    <BottomSheetView
      className="min-h-[221px] gap-2 p-4 pt-2"
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      <H2>Live type</H2>

      <LiveTypeOption
        icon={<Music color={isDark ? colors.brand[40] : colors.brand[70]} />}
        label="Song request mode"
        onPress={() => handleTypeSelection('REQUEST')}
      />

      <LiveTypeOption
        icon={
          <ShoutoutIcon color={isDark ? colors.brand[40] : colors.brand[70]} />
        }
        label="Shout out mode"
        onPress={() => handleTypeSelection('SHOUTOUT')}
      />
    </BottomSheetView>
  );
};
