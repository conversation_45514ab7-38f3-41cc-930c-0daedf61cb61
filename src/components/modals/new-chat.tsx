import { Feather } from '@expo/vector-icons';
import {
  BottomSheetScrollView,
  BottomSheetTextInput,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import { useColorScheme } from 'nativewind';
import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { EmptyState as AppEmptyState } from '@/components/ui';
import {
  colors,
  LgBoldLabel,
  List,
  Pressable,
  semanticColors,
  View,
} from '@/components/ui';
import { useSearchUsers } from '@/lib';

import { EmptyState } from '../chats/list/empty';
import { ListLoadingComponent } from '../chats/list/loading';
import { SearchListItem } from '../chats/list/search-list-item';
import { NewChatLoading } from '../loading/new-chat';

interface NewChatModalContentProps {
  onDismiss: () => void;
}

export const NewChatModalContent: React.FC<NewChatModalContentProps> = ({
  onDismiss,
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  const { filteredUsers, queryLoading, favoriteAccounts } = useSearchUsers(
    searchQuery,
    {
      mode: 'newChat',
    }
  );

  return (
    <BottomSheetScrollView
      showsVerticalScrollIndicator={false}
      className="flex-1"
    >
      <View className="flex-row items-center justify-between p-4">
        <View className="size-8" />
        <LgBoldLabel>New chat</LgBoldLabel>
        <CloseButton close={onDismiss} />
      </View>
      <View className="flex-row items-center p-4">
        <View className="relative flex-1">
          <BottomSheetTextInput
            className="h-12 w-full rounded-full bg-bg-subtle-light px-4 py-3 pl-[50px] font-aeonik-regular text-base/100 font-medium dark:bg-bg-subtle-dark dark:text-white"
            placeholderClassName="text-fg-subtle-light dark:text-fg-subtle-dark font-aeonik-regular text-base/100 font-medium"
            placeholder="Search your favourites"
            onChangeText={setSearchQuery}
          />
          <Feather
            name="search"
            className="absolute left-4 top-3.5"
            size={20}
            color={colors.grey[50]}
          />
        </View>
      </View>

      {queryLoading ? (
        <NewChatLoading />
      ) : (
        <View className="mx-4 mt-4 min-h-96 flex-1">
          <List
            data={filteredUsers}
            estimatedItemSize={12}
            showsVerticalScrollIndicator={false}
            renderItem={({ item: user, index }) => (
              <SearchListItem key={index} user={user} selectUser={onDismiss} />
            )}
            ListEmptyComponent={
              searchQuery === '' || favoriteAccounts?.length === 0 ? (
                <AppEmptyState text="You don't have any accounts favourited" />
              ) : (
                <EmptyState type="users" />
              )
            }
            ListFooterComponent={
              <ListLoadingComponent loading={queryLoading || false} />
            }
            keyExtractor={(item) => item.id}
            ItemSeparatorComponent={() => <View className="size-4 " />}
          />
        </View>
      )}
    </BottomSheetScrollView>
  );
};

const CloseButton = ({ close }: { close: () => void }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  return (
    <Pressable
      onPress={close}
      className="size-8 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
      hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
      accessibilityLabel="close modal"
      accessibilityRole="button"
      accessibilityHint="closes the modal"
    >
      <Svg width={16} height={16} fill="none" viewBox="0 0 16 16">
        <Path
          d="M4 4L8 8M8 8L12 12M8 8L12 4M8 8L4 12"
          stroke={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </Svg>
    </Pressable>
  );
};
