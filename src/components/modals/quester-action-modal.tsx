import { FontAwesome6 } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { createOrUpdateFirebaseUser } from '@/api';
import {
  Button,
  ChatIcon,
  colors,
  H4,
  HeartFilledIcon,
  HeartIcon,
  Image,
  MdRegularLabel,
  Modal,
  Pressable,
  useModal,
  View,
} from '@/components/ui';
import { useAccountFavourite, useAuth } from '@/lib';
import { useFirestoreUser } from '@/lib/hooks/use-firestore-users';

type User = {
  id: string;
  username: string;
  profileImageUrl: string;
  fullName: string;
};

interface UserActionModalProps {
  user: User;
  onDismiss?: () => void;
}

interface UserActionModalRef {
  present: () => void;
  dismiss: () => void;
}

const UserActionModal = React.forwardRef<
  UserActionModalRef,
  UserActionModalProps
>(({ user: targetUser, onDismiss }, ref) => {
  const modal = useModal();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const currentUser = useAuth.use.user();
  const { push } = useRouter();

  const { isInFirestore } = useFirestoreUser(targetUser.id, targetUser);

  const chatRoomId = React.useMemo(
    () => [currentUser?.id, targetUser.id].sort().join('_'),
    [currentUser?.id, targetUser.id]
  );

  const { favourited, toggleFavourite } = useAccountFavourite(targetUser.id);

  // Expose modal controls through ref
  React.useImperativeHandle(
    ref,
    () => ({
      present: modal.present,
      dismiss: modal.dismiss,
    }),
    [modal]
  );

  const handleDismiss = React.useCallback(() => {
    modal.dismiss();
    onDismiss?.();
  }, [modal, onDismiss]);

  const handleChatPress = React.useCallback(async () => {
    handleDismiss();
    if (!isInFirestore) {
      await createOrUpdateFirebaseUser(
        {
          id: targetUser.id,
          username: targetUser.username,
          profileImageUrl: targetUser.profileImageUrl || '',
        },
        'offline'
      );
    }
    push({
      pathname: '/chats/[chatRoomId]' as const,
      params: {
        chatRoomId,
        username: targetUser.username,
        userId: targetUser.id,
        userAvatar: targetUser.profileImageUrl,
      },
    });
  }, [handleDismiss, push, chatRoomId, targetUser]);

  const handleProfilePress = React.useCallback(() => {
    handleDismiss();
    push({ pathname: '/users/[id]', params: { id: targetUser.id } });
  }, [handleDismiss, push, targetUser.id]);

  const handleFavouritePress = React.useCallback(() => {
    toggleFavourite();
  }, [toggleFavourite]);

  return (
    <Modal
      ref={modal.ref}
      index={0}
      enableDynamicSizing
      backgroundStyle={{
        backgroundColor: 'transparent',
      }}
      hasHandle={false}
    >
      <BottomSheetView
        className="min-h-96 flex-1 gap-4 px-4"
        style={{
          paddingBottom: insets.bottom,
        }}
      >
        <View className="rounded-lg bg-bg-subtle-light dark:bg-bg-subtle-dark">
          <View className="items-center justify-center p-4">
            <View className="items-center">
              <View className="rounded-full border-2 border-bg-canvas-light dark:border-bg-canvas-dark">
                <Image
                  source={
                    targetUser.profileImageUrl ||
                    require('~/assets/images/avatar.png')
                  }
                  className="size-16 rounded-full"
                  priority="high"
                />
              </View>
              <H4>{targetUser.username}</H4>
            </View>
          </View>

          <Pressable
            onPress={handleChatPress}
            className="h-16 flex-row items-center justify-between px-4"
          >
            <View className="size-6 items-center justify-center">
              <ChatIcon
                strokeColor={isDark ? colors.grey[60] : colors.grey[50]}
              />
            </View>
            <MdRegularLabel>Chat</MdRegularLabel>
            <View className="size-6" />
          </Pressable>

          <Pressable
            onPress={handleFavouritePress}
            className="h-16 flex-row items-center justify-between px-4"
          >
            <View className="size-6 items-center justify-center">
              {favourited ? (
                <HeartFilledIcon color={colors.red[50]} />
              ) : (
                <HeartIcon color={isDark ? colors.grey[60] : colors.grey[50]} />
              )}
            </View>
            <MdRegularLabel>
              {favourited ? 'Unfavourite' : 'Favourite'} user
            </MdRegularLabel>
            <View className="size-4" />
          </Pressable>

          <Pressable
            onPress={handleProfilePress}
            className="h-16 flex-row items-center justify-between px-4"
          >
            <View className="size-6 items-center justify-center">
              <FontAwesome6
                name="user"
                size={16}
                color={isDark ? colors.grey[60] : colors.grey[50]}
              />
            </View>
            <MdRegularLabel>View profile</MdRegularLabel>
            <View className="size-6" />
          </Pressable>
        </View>

        <Button
          label="Cancel"
          className="h-16 rounded-full bg-bg-subtle-light dark:bg-bg-subtle-dark"
          onPress={handleDismiss}
        />
      </BottomSheetView>
    </Modal>
  );
});

UserActionModal.displayName = 'UserActionModal';

export { UserActionModal };
export type { User, UserActionModalProps, UserActionModalRef };
