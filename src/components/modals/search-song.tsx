import { Feather } from '@expo/vector-icons';
import {
  BottomSheetScrollView,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { type SongRequestPayload, useSpotifySearchMusic } from '@/api/session';
import {
  colors,
  List,
  MdRegularLabel,
  P,
  Pressable,
  View,
} from '@/components/ui';
import { formatAmount, toAmountInMajor, useLoggedInUser } from '@/lib';
import { getSpotifyToken } from '@/lib/session/utils';

import { SongsLoading } from '../loading/songs';
import { TrackItem } from '../session/request/track-item';

interface SearchSongModalContentProps {
  onDismiss: () => void;
  onSelectSong?: (song: SongRequestPayload) => void;
  sessionId: string;
}

export const SearchSongModalContent: React.FC<SearchSongModalContentProps> = ({
  onDismiss,
  onSelectSong,
  sessionId,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const spotifyData = getSpotifyToken();

  const [searchQuery, setSearchQuery] = React.useState('');

  const { data: spotifyResponse, isLoading } = useSpotifySearchMusic({
    variables: {
      query: searchQuery,
      accessToken: spotifyData?.access_token || '',
    },
    enabled: !!spotifyData?.access_token && !!searchQuery,
  });

  const { data: user } = useLoggedInUser();

  return (
    <BottomSheetScrollView keyboardDismissMode="on-drag" className="flex-1">
      <View className="flex-row items-center p-4">
        <View className="relative flex-1">
          <BottomSheetTextInput
            className="h-12 w-full rounded-full bg-bg-subtle-light px-4 py-3 pl-[50px] font-aeonik-regular text-base/100 font-medium dark:bg-bg-subtle-dark dark:text-white"
            placeholderClassName="text-fg-subtle-light dark:text-fg-subtle-dark font-aeonik-regular text-base/100 font-medium"
            placeholder="Search music"
            onChangeText={setSearchQuery}
          />
          <Feather
            name="search"
            className="absolute left-4 top-3.5"
            size={20}
            color={isDark ? colors.white : colors.grey[100]}
          />
        </View>
        <Pressable
          className="h-8 items-center justify-center px-4 py-2"
          onPress={onDismiss}
        >
          <MdRegularLabel>Cancel</MdRegularLabel>
        </Pressable>
      </View>

      <View className="mx-4 mt-2 h-12 justify-center rounded-md bg-bg-info-contrast px-4">
        <P>
          Wallet balance:{' '}
          {formatAmount(toAmountInMajor(user?.walletBalance || 0))}
        </P>
      </View>

      {isLoading ? (
        <SongsLoading />
      ) : (
        <View className="mx-4 mt-4 min-h-96 flex-1">
          <List
            data={spotifyResponse?.tracks.items}
            estimatedItemSize={74}
            renderItem={({ item }) => (
              <TrackItem
                {...item}
                closeSongRequestModal={onDismiss}
                onSelectSong={onSelectSong}
                sessionId={sessionId}
              />
            )}
            keyExtractor={(item) => String(item.id)}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}
    </BottomSheetScrollView>
  );
};
