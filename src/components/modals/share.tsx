import { BottomSheetView } from '@gorhom/bottom-sheet';
import * as Clipboard from 'expo-clipboard';
import { Share } from 'react-native';
import { Linking } from 'react-native';

import {
  Modal,
  semanticColors,
  Text,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { type IconType } from '@/types';

import { IconComponent } from '../common/icon-component';

const SHARE_OPTIONS = [
  {
    id: 'copy',
    name: 'Copy',
    iconType: 'material' as IconType,
    iconName: 'link',
  },
  {
    id: 'message',
    name: 'Message',
    iconType: 'material' as IconType,
    iconName: 'mail-outline',
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    iconType: 'ionicons' as IconType,
    iconName: 'logo-whatsapp',
    bgColor: semanticColors.fg.success.dark,
  },
  {
    id: 'instagram',
    name: 'Instagram',
    iconType: 'custom-svg' as IconType,
    iconName: 'Instagram',
    bgColor: '#DA337A',
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    iconType: 'custom-svg' as IconType,
    iconName: 'X',
    bgColor: '#3D3D3D',
  },
  // {
  //   id: 'tiktok',
  //   name: 'TikTok',
  //   iconType: 'ionicons' as IconType,
  //   iconName: 'logo-tiktok',
  //   bgColor: '#3D3D3D',
  // },
  {
    id: 'facebook',
    name: 'Facebook',
    iconType: 'ionicons' as IconType,
    iconName: 'logo-facebook',
    bgColor: semanticColors.social.facebook.primary,
  },
  {
    id: 'menu',
    name: 'More',
    size: '16',
    color: semanticColors.accent.bold.dark,
    iconType: 'entypo' as IconType,
    iconName: 'dots-three-horizontal',
    bgColor: semanticColors.accent.subtle.dark,
  },
];

export function ShareModal({
  shareModalRef,
  onDismiss,
  content,
}: {
  shareModalRef: any;
  onDismiss: () => void;
  content: string;
}) {
  const handleShare = async (platform: string) => {
    onDismiss();

    switch (platform) {
      case 'copy':
        await Clipboard.setStringAsync(content);
        alert('Link copied to clipboard!');
        break;
      case 'message':
        Linking.openURL(`sms:&body=${encodeURIComponent(content)}`);
        break;
      case 'whatsapp':
        Linking.openURL(`whatsapp://send?text=${encodeURIComponent(content)}`);
        break;
      case 'twitter':
        Linking.openURL(
          `https://twitter.com/intent/tweet?text=${encodeURIComponent(content)}`
        );
        break;
      case 'facebook':
        Linking.openURL(
          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(content)}`
        );
        break;
      case 'instagram':
        await Share.share({
          message: content,
        });
        break;
      case 'tiktok':
        alert(`${platform} sharing is not supported directly.`);
        break;
      case 'menu':
        try {
          await Share.share({
            message: content,
          });
        } catch (error) {
          alert('An error occurred while sharing.');
          console.error(error);
        }
        break;
      default:
        break;
    }
  };

  return (
    <Modal ref={shareModalRef} enableDynamicSizing title="Share to">
      <BottomSheetView className="min-h-[250px] w-full flex-row flex-wrap justify-center gap-7 px-4 py-6">
        {SHARE_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.id}
            className="h-[76px] w-16 items-center justify-center gap-2 px-px"
            onPress={() => handleShare(option.id)}
          >
            <View
              className="size-12 items-center justify-center rounded-full bg-accent-moderate dark:bg-accent-moderate"
              style={
                option.bgColor ? { backgroundColor: option.bgColor } : undefined
              }
            >
              <IconComponent
                iconType={option.iconType}
                iconName={option.iconName}
                size={option.size ? parseInt(option.size) : 24}
                color={option.color}
              />
            </View>
            <Text className="text-center text-xs/[150%] font-[400] text-fg-muted-light dark:text-fg-muted-dark">
              {option.name}
            </Text>
          </TouchableOpacity>
        ))}
      </BottomSheetView>
    </Modal>
  );
}
