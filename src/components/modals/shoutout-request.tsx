import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  colors,
  Image,
  MdRegularLabel,
  ShoutoutIcon,
  View,
} from '@/components/ui';

import { ModalActions } from '../session/request/modal-actions';
import { ShoutoutMessageBox } from '../session/request/shoutout-box';
import { SongPreview } from '../session/request/song-preview';

interface ShoutoutRequestModalProps {
  id: string;
  title: string;
  subtitle?: string;
  username: string;
  message: string[] | string;
  imageSource?: string | null;
  iconSource?: string;
  onAccept?: (id: string) => void;
  onDecline?: (id: string) => void;
  onDismissShoutoutModal?: () => void;
}

export const ShoutoutRequestModal: React.FC<ShoutoutRequestModalProps> = ({
  id,
  title,
  subtitle,
  message,
  iconSource,
  imageSource,
  onAccept,
  onDecline,
  onDismissShoutoutModal,
  username,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();

  return (
    <BottomSheetView
      className="min-h-[300px] gap-5 px-4 pt-6"
      style={{
        paddingBottom: insets.bottom,
      }}
    >
      <View className="items-center gap-3">
        {imageSource ? (
          <Image
            className="size-16 rounded-full"
            source={imageSource || require('~/assets/images/avatar.png')}
          />
        ) : (
          <View className="size-16 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
            <ShoutoutIcon
              color={isDark ? colors.brand[40] : colors.brand[70]}
            />
          </View>
        )}
        <MdRegularLabel>{username}</MdRegularLabel>

        {Array.isArray(message) ? (
          message.map((msg, index) => (
            <ShoutoutMessageBox key={index} message={msg} />
          ))
        ) : (
          <ShoutoutMessageBox message={message} />
        )}
      </View>

      {/* Has Song Request?? */}
      {title && (
        <SongPreview
          title={title}
          subtitle={subtitle}
          iconSource={iconSource}
        />
      )}

      <ModalActions
        primaryLabel="Accept"
        secondaryLabel="Decline"
        onPrimaryAction={() => {
          onAccept?.(id);
          onDismissShoutoutModal?.();
        }}
        onSecondaryAction={() => {
          onDecline?.(id);
          onDismissShoutoutModal?.();
        }}
        secondaryVariant="destructive"
        className="flex-row-reverse gap-3 py-4"
        isBtnGroupRow
      />
    </BottomSheetView>
  );
};
