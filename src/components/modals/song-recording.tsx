import { AudioModule, RecordingPresets, useAudioRecorder } from 'expo-audio';
import * as Linking from 'expo-linking';
import React, { useEffect } from 'react';
import { Alert, Image, View } from 'react-native';
import { toast } from 'sonner-native';

import { useSendSongRecording, useUpdateSongRequest } from '@/api/session';
import { useRecordingTimer } from '@/lib';

import LoadingScreen from '../loading';
import { H2, P } from '../ui';

const SongRecordingModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  songId: string;
  sessionId: string;
}> = ({ visible = true, onClose, sessionId, songId }) => {
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const { mutate: sendSongRecording, isPending: isSongRecording } =
    useSendSongRecording();
  const { mutate: updateSongRequest, isPending: playingSongRequest } =
    useUpdateSongRequest();
  // Use useRef to store the onClose function
  const onCloseRef = React.useRef(onClose);

  const [isRecording, setIsRecording] = React.useState(false);

  // Update the ref when onClose changes
  React.useEffect(() => {
    onCloseRef.current = onClose;
  }, [onClose]);

  // Request permission on mount
  useEffect(() => {
    (async () => {
      const status = await AudioModule.requestRecordingPermissionsAsync();
      if (!status.granted) {
        Alert.alert(
          'Permission to access microphone was denied',
          'To use this feature, please enable microphone access in your settings.',
          [
            {
              text: 'Open Settings',
              style: 'default',
              onPress: () => Linking.openSettings(),
            },
            { text: 'Cancel', style: 'cancel', onPress: onClose },
          ]
        );
      } else {
        // Auto-start recording when modal appears and permissions are granted
        startRecording();
      }
    })();

    // Cleanup function to ensure recording stops if component unmounts
    return () => {
      console.log('🚀 ~ unmount ~ stopRecording:', isRecording);
      if (isRecording) {
        audioRecorder
          .stop()
          .catch((err) =>
            console.error('Error stopping recording on unmount:', err)
          );
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Start recording function
  const startRecording = async () => {
    try {
      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();
      setIsRecording(true);
      console.log('Recording started for song ID:', songId);
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert(
        'Recording Error',
        'Failed to start recording. Please try again.'
      );
    }
  };

  const stopRecording = React.useCallback(async () => {
    try {
      // The recording will be available on `audioRecorder.uri`.
      await audioRecorder.stop();

      if (audioRecorder.uri) {
        const formData = new FormData();
        formData.append('recording', {
          uri: audioRecorder.uri,
          name: 'audio.aac',
          type: 'audio/aac',
        } as any);
        sendSongRecording(
          { query: { sessionId, songId }, body: formData },
          {
            onSuccess: () => {
              updateSongRequest(
                { sessionId, songId, status: 'PLAYED' },
                {
                  onSuccess: () => {
                    setIsRecording(false);
                    // Use setTimeout to defer the state update until after the current render cycle
                    setTimeout(() => {
                      onCloseRef.current();
                    }, 0);
                  },
                  onError: (error) => toast.error(error.message),
                }
              );
            },
            onError: (error) => toast.error(error.message),
          }
        );
      }
    } catch (error) {
      setIsRecording(false);
      console.error('Failed to stop recording:', error);
      Alert.alert(
        'Recording Error',
        'Failed to save recording. Please try again.'
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { formattedTime } = useRecordingTimer(
    visible && isRecording,
    10,
    stopRecording
  );

  if (isSongRecording || playingSongRequest) return <LoadingScreen />;

  return (
    <View className="flex-1 items-center justify-center p-8">
      <View className="h-[278px] w-full items-center gap-6 rounded-lg bg-bg-canvas-light p-6 pt-8 dark:bg-[#070707]">
        {/* Disc Icon */}
        <View className="size-[120px] rounded-full">
          <Image
            source={require('~/assets/images/session/disc.png')}
            resizeMode="contain"
            className="size-[120px]"
          />
        </View>

        {/* Text Content */}
        <View className="w-[279px] items-center gap-2">
          <H2>Recording... {formattedTime}</H2>
          <P className="text-center text-grey-60 dark:text-grey-50">
            Request will automatically be moved to history after recording
          </P>
        </View>
      </View>
    </View>
  );
};

export default SongRecordingModal;
