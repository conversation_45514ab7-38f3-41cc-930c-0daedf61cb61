import { useEffect, useRef } from 'react';
import { Animated } from 'react-native';

import { View } from '@/components/ui';
import { cn } from '@/lib';

export interface DotStepIndicatorProps {
  activeStep: number;
  totalSteps: number;
  activeStepClassName?: string;
  hasOneActiveStep?: boolean;
}

const DotStepIndicator: React.FC<DotStepIndicatorProps> = ({
  activeStep,
  totalSteps,
  activeStepClassName,
  hasOneActiveStep,
}) => {
  const steps = Array.from({ length: totalSteps }, (_, index) => index + 1);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const widthAnim = useRef(new Animated.Value(8)).current;

  useEffect(() => {
    // Animate the active dot
    Animated.parallel([
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]),
      Animated.timing(widthAnim, {
        toValue: activeStep === 1 ? 32 : 8,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [activeStep, scaleAnim, widthAnim]);

  return (
    <View className="flex flex-row gap-2">
      {steps.map((step) => {
        const isActive = step === activeStep;
        const isCompleted = step <= activeStep;

        const indicatorClass = hasOneActiveStep
          ? isActive
            ? `bg-accent-moderate dark:bg-accent-moderate ${activeStepClassName}`
            : 'bg-grey-20 dark:bg-grey-80'
          : isCompleted
            ? `bg-accent-moderate dark:bg-accent-moderate ${activeStepClassName}`
            : 'bg-grey-20 dark:bg-grey-80';

        return (
          <Animated.View
            key={step}
            className={cn('size-2 rounded-full', indicatorClass)}
            style={
              isActive && {
                transform: [{ scale: scaleAnim }],
              }
            }
          />
        );
      })}
    </View>
  );
};

export default DotStepIndicator;
