import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { MdRegularLabel, TouchableOpacity, View } from '@/components/ui';

interface AccountItemProps {
  text: string;
  onPress: () => void;
}
export const AccountItem: React.FC<AccountItemProps> = ({ text, onPress }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <TouchableOpacity className="flex-row items-center" onPress={onPress}>
      <View className="flex-1 ">
        <MdRegularLabel>{text}</MdRegularLabel>
      </View>
      <TouchableOpacity
        className="h-16 w-10 items-end justify-center"
        onPress={onPress}
      >
        <Ionicons
          name="chevron-forward"
          size={24}
          color={isDark ? 'white' : '#070707'}
          className="text-grey-100 dark:text-white"
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};
