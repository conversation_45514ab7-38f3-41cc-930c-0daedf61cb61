import { BlurView } from 'expo-blur';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';

import { colors, Image, View } from '../ui';

interface ProfileGradientBgProps {
  image: string;
}

export const ProfileGradientBg: React.FC<ProfileGradientBgProps> = ({
  image,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);

  return (
    <React.Fragment>
      <Image
        source={image}
        contentFit="cover"
        className="absolute size-full opacity-100"
      />
      <BlurView
        tint={isDark ? 'systemThickMaterialDark' : 'extraLight'}
        intensity={isDark ? 85 : 70}
        style={styles.blurView}
      >
        <View style={styles.overlay} />
      </BlurView>
    </React.Fragment>
  );
};

export const styling = (isDark: boolean) =>
  StyleSheet.create({
    statusBarOverlay: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      backgroundColor: '#000000',
      opacity: 0.1,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
    overlay: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      backgroundColor: isDark ? colors.black : colors.white,
      opacity: 0.4,
    },
  });
