import {
  doc,
  getDoc,
  getFirestore,
  updateDoc,
} from '@react-native-firebase/firestore';
import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { toast } from 'sonner-native';

import { usePatchFcmToken, useUpdateProfile } from '@/api/user';
import { MdRegularLabel, Switch, View } from '@/components/ui';
import {
  type ChatOption,
  type NotificationOption,
  useLoggedInUser,
} from '@/lib';
import {
  GetFCMToken,
  RemoveFCMToken,
  requestUserPermission,
} from '@/lib/notification';
import { type UserType } from '@/types';

interface NotificationItemProps {
  text: string;
  type?: ChatOption | NotificationOption;
  nativeID?: string | undefined;
}
export const NotificationItem: React.FC<NotificationItemProps> = ({
  text,
  type,
  nativeID,
}) => {
  const queryClient = useQueryClient();
  const { data: currentUser } = useLoggedInUser();
  const [checked, setChecked] = React.useState(false);

  const { mutate: updateProfile } = useUpdateProfile({
    onSuccess: () => {
      toast.success(`${text} Preference updated successfully`);
      queryClient.invalidateQueries({ queryKey: ['getUser'] });
    },
  });
  const { mutate } = usePatchFcmToken();

  const db = getFirestore();
  const userRef = doc(db, 'Users', currentUser?.id || '');

  React.useEffect(() => {
    if (!type || !currentUser?.id) return;

    const fetchStatus = async () => {
      const snapshot = await getDoc(userRef);
      const userData = snapshot.data() as UserType;

      switch (type) {
        case 'Online status':
          setChecked(!userData.hideOnlineStatus);
          break;
        case 'Last seen':
          setChecked(!userData.hideLastSeen);
          break;
        case 'Near me':
          setChecked(currentUser.allowNearbyDiscovery ?? false);
          break;
        case 'Email':
          setChecked(currentUser.emailNotification ?? false);
          break;
        case 'Push':
          setChecked(currentUser.pushNotification ?? false);
          break;
      }
    };

    fetchStatus();
  }, [type, currentUser?.id]);

  const handleToggle = async (value: boolean) => {
    setChecked(value);
    if (!type || !currentUser?.id) return;

    switch (type) {
      case 'Online status':
        await updateDoc(userRef, { hideOnlineStatus: !value });
        break;
      case 'Last seen':
        await updateDoc(userRef, { hideLastSeen: !value });
        break;
      case 'Near me':
        updateProfile({
          userId: currentUser.id,
          payload: {
            allowNearbyDiscovery: value,
          },
        });
        break;
      case 'Email':
        updateProfile({
          userId: currentUser.id,
          payload: {
            emailNotification: value,
          },
        });
        break;
      case 'Push':
        updateProfile(
          {
            userId: currentUser.id,
            payload: {
              pushNotification: value,
            },
          },
          {
            onSuccess: async ({ pushNotification }) => {
              if (pushNotification) {
                await requestUserPermission();
                const fcmToken = await GetFCMToken();
                if (fcmToken) {
                  mutate({ userId: currentUser.id, fcmToken });
                }
              } else {
                await RemoveFCMToken();
              }
            },
            onError: (error) => toast.error(error.message),
          }
        );
        break;
    }
  };

  return (
    <View className="flex-row items-center">
      <View className="flex-1 ">
        <MdRegularLabel>{text}</MdRegularLabel>
      </View>
      <View className="h-16 w-[72px] items-end justify-center">
        <Switch
          checked={checked}
          onChange={handleToggle}
          nativeID={nativeID}
          accessibilityLabel={text}
        />
      </View>
    </View>
  );
};
