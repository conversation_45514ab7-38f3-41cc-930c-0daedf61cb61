import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import { cn, useSelectedTheme } from '@/lib';
import { type ProfileItem as ProfileItemType } from '@/types';

import { IconComponent } from '../common/icon-component';
import {
  ChevronRight,
  colors,
  MdRegularLabel,
  Switch,
  TouchableOpacity,
  View,
} from '../ui';

interface ProfileItemProps {
  item: ProfileItemType;
  className?: string;
}
export const ProfileItem: React.FC<ProfileItemProps> = ({
  item,
  className,
}) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const { setSelectedTheme } = useSelectedTheme();

  const [checked, setChecked] = React.useState(colorScheme === 'dark');

  const handleThemeSwitch = (checked: boolean) => {
    setChecked(checked);
    setSelectedTheme(checked ? 'dark' : 'light');
  };

  return (
    <TouchableOpacity
      className={cn('h-16 flex-row gap-4 px-4', className)}
      onPress={() => {
        if (item.isToggle) {
          handleThemeSwitch(!checked);
        } else if (item.handleExternalLink) {
          item.handleExternalLink();
        } else if (item.hasWebView && item.externalUrl) {
          router.push({
            pathname: '/webview',
            params: { uri: item.externalUrl },
          });
        } else {
          router.navigate(item.navigateTo);
        }
      }}
    >
      <View className="flex-1 flex-row items-center gap-4 ">
        <IconComponent
          iconType={item.iconType}
          iconName={item.icon}
          color={
            item.iconColor
              ? item.iconColor
              : colorScheme === 'dark'
                ? 'white'
                : '#070707'
          }
        />
        <View className="py-3">
          <MdRegularLabel className={item.textColor}>
            {item.label}
          </MdRegularLabel>
        </View>
      </View>

      {item.isToggle ? (
        <View className="h-16 items-end justify-center">
          <Switch
            checked={checked}
            onChange={handleThemeSwitch}
            nativeID="theme-mode"
            accessibilityLabel="Theme switcher"
          />
        </View>
      ) : !item.hideChevronForward ? (
        <TouchableOpacity
          className="h-16 w-10 items-end justify-center"
          onPress={() => router.navigate(item.navigateTo)}
        >
          <ChevronRight color={colors.brand['60']} />
        </TouchableOpacity>
      ) : null}
    </TouchableOpacity>
  );
};
