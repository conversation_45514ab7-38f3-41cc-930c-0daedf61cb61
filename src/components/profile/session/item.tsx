import React from 'react';

import {
  MdRegularLabel,
  SmRegularLabel,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';

interface SessionHistoryItemProps {
  text: string;
  date: string;
  onPress: () => void;
}
export const SessionHistoryItem: React.FC<SessionHistoryItemProps> = ({
  text,
  date,
  onPress,
}) => {
  return (
    <View className="h-16 flex-1 flex-row items-center justify-between px-4">
      <View className="flex-1 justify-center gap-2.5">
        <MdRegularLabel>{text}</MdRegularLabel>
        <SmRegularLabel className="text-grey-60 dark:text-grey-50">
          {date}
        </SmRegularLabel>
      </View>
      <TouchableOpacity
        className="items-end justify-center px-4 py-2"
        onPress={onPress}
      >
        <XsBoldLabel className="text-brand-70 dark:text-brand-40">
          Report a problem
        </XsBoldLabel>
      </TouchableOpacity>
    </View>
  );
};
