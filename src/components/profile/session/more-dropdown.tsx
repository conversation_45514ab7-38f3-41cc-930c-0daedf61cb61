import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { MoreHorizontal, semanticColors } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/picker';
import { cn } from '@/lib';

type DropdownOptions = 'report';

const dropdownOptions: { label: string; value: DropdownOptions }[] = [
  { label: 'Report a problem', value: 'report' },
];

export interface EventDetailMoreDropdownProps {
  onValueChange: (value: DropdownOptions) => void;
  value?: DropdownOptions;
  disabled?: boolean;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  itemTextClassName?: string;
  contentInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

const SessionHistoryMoreDropdown: React.FC<EventDetailMoreDropdownProps> = ({
  onValueChange,
  disabled = false,
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-grey-100 dark:text-white',
  contentInsets,
}) => {
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const defaultContentInsets = {
    top: insets.top,
    bottom: insets.bottom,
    right: 8,
    ...contentInsets,
  };

  return (
    <Select
      onValueChange={(option) => {
        if (option && !disabled) {
          onValueChange(option.value as DropdownOptions);
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          'native:border-0 native:items-center native:justify-center p-0',
          disabled && 'opacity-50',
          triggerClassName
        )}
        hideChevron
      >
        <MoreHorizontal
          width={24}
          height={24}
          color={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
          fill={
            isDark ? semanticColors.fg.base.dark : semanticColors.fg.base.light
          }
        />
      </SelectTrigger>

      <SelectContent
        insets={defaultContentInsets}
        className={cn(
          'bg-black/45 border-0 dark:bg-black/70 rounded-lg',
          contentClassName
        )}
      >
        <SelectGroup>
          {dropdownOptions.map(({ label, value }) => (
            <SelectItem
              key={value}
              label={label}
              value={value}
              textClassName={cn(
                'native:text-fg-base-light native:dark:text-fg-base-dark',
                itemTextClassName
              )}
              className={itemClassName}
              hasCheck={false}
            >
              {label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default SessionHistoryMoreDropdown;
