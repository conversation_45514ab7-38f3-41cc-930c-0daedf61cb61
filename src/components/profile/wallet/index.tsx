import { Feather, Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Link } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';

import {
  colors,
  FundIcon,
  MdRegularLabel,
  SmRegularLabel,
  SongIcon,
  TicketIcon,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { cn, formatAmount, formatEnumLabel, toAmountInMajor } from '@/lib';
import { type Transaction } from '@/types';

interface TransactionHistoryItemProps {
  item: Transaction;
}
export const TransactionHistoryItem: React.FC<TransactionHistoryItemProps> = ({
  item,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const IconComponent = () => {
    switch (item.category) {
      case 'FUND_WALLET':
        return (
          <Ionicons
            name="wallet-outline"
            size={16}
            color={isDark ? colors.brand[40] : colors.brand[70]}
          />
        );
      case 'SONG_REQUEST':
        return (
          <Ionicons
            name="musical-notes-outline"
            size={16}
            color={isDark ? colors.brand[40] : colors.brand[70]}
          />
        );
      case 'WITHDRAW_WALLET':
        return (
          <Feather
            name="arrow-up-right"
            size={16}
            color={isDark ? colors.brand[40] : colors.brand[70]}
          />
        );
      case 'EVENT_TICKET_PURCHASE':
      case 'EVENT_TICKET_EARNINGS':
        return (
          <TicketIcon color={isDark ? colors.brand[40] : colors.brand[70]} />
        );
      case 'DJ_SESSION_EARNINGS':
        return (
          // <Feather
          //   name="headphones"
          //   size={16}
          //   color={isDark ? colors.brand[40] : colors.brand[70]}
          // />

          <SongIcon color={isDark ? colors.brand[40] : colors.brand[70]} />
        );

      default:
        return (
          <FundIcon color={isDark ? colors.brand[40] : colors.brand[70]} />
          // <Ionicons
          //   name="wallet-outline"
          //   size={16}
          //   color={isDark ? colors.brand[40] : colors.brand[70]}
          // />
        );
    }
  };

  return (
    <Link
      href={{
        pathname: '/profile/wallet/[id]',
        params: {
          id: item.id,
        },
      }}
      asChild
    >
      <TouchableOpacity className="h-16 flex-row items-center justify-between">
        <View className="flex-1 flex-row items-center gap-4">
          <View className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
            {/* TODO: render custom icons */}
            <IconComponent />
          </View>
          <View className="gap-2.5">
            <MdRegularLabel>
              {item.category === 'DJ_SESSION_EARNINGS'
                ? 'Live Session Earnings'
                : item.category === 'SONG_REQUEST'
                  ? 'Live Request'
                  : formatEnumLabel(item.category)}
            </MdRegularLabel>
            <SmRegularLabel className="text-grey-60 dark:text-grey-50">
              {format(item.createdAt || '', 'yyyy-MM-dd HH:mm')}
            </SmRegularLabel>
          </View>
        </View>

        {/* TODO: render color based on debit/credit */}
        <SmRegularLabel
          className={cn(
            item.type === 'CREDIT' && item.status === 'SUCCESS'
              ? 'text-green-60 dark:text-green-40'
              : 'text-red-60 dark:text-red-40'
          )}
        >
          {formatAmount(toAmountInMajor(item.amount), undefined, item.currency)}
        </SmRegularLabel>
      </TouchableOpacity>
    </Link>
  );
};
