import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';

import { Image, Small, TouchableOpacity, View } from '@/components/ui';
import { cn, useGetLiveSessionQuery } from '@/lib';

export const SideActions: React.FC = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { push } = useRouter();
  const { data: sessionData } = useGetLiveSessionQuery(id);

  const sessionInfo = sessionData?.djSession;
  const shoutoutEnabled = sessionInfo?.shoutoutEnabled;

  return (
    <View className="gap-2">
      <ActionButton
        imagePath={require('~/assets/images/session/request.png')}
        label="Request"
        onPress={() =>
          push({ pathname: '/session/[id]/request', params: { id } })
        }
      />
      {shoutoutEnabled && (
        <ActionButton
          imagePath={require('~/assets/images/session/shoutout.png')}
          label="Shoutout"
          onPress={() =>
            push({ pathname: '/session/[id]/shoutout', params: { id } })
          }
        />
      )}
      {/* <ActionButton
        imagePath={require('~/assets/images/session/gift.png')}
        label="Gift"
      /> */}
    </View>
  );
};

interface ActionButtonProps {
  imagePath: any;
  label: string;
  onPress?: () => void;
  className?: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  imagePath,
  label,
  onPress,
  className,
}) => (
  <TouchableOpacity
    className={cn('items-center gap-1', className)}
    onPress={onPress}
  >
    <View className="size-[35px] rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark">
      <Image source={imagePath} className="size-[35px]" />
    </View>
    <Small>{label}</Small>
  </TouchableOpacity>
);
