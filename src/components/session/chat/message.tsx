import * as React from 'react';
import { Animated, Pressable, StyleSheet } from 'react-native';

import { Image, SmBoldLabel, Tiny, View } from '@/components/ui';
import { type LiveSessionChatMessage } from '@/lib/hooks/use-session-chat';

import { CreatorBadge } from './pinned';

type ChatMessagesProps = {
  item: LiveSessionChatMessage;
  onFadeComplete?: () => void;
  handleChatLongPress?: () => void;
  opacity: number;
};
export const ChatMessage = ({
  item,
  handleChatLongPress,
  opacity,
}: ChatMessagesProps) => {
  // const fadeAnim = React.useRef(new Animated.Value(1)).current;

  // React.useEffect(() => {
  //   const timer = setTimeout(() => {
  //     Animated.timing(fadeAnim, {
  //       toValue: 0,
  //       duration: 1000,
  //       useNativeDriver: true,
  //     }).start(() => {
  //       onFadeComplete?.();
  //     });
  //   }, 5000);

  //   return () => clearTimeout(timer);
  // }, []);

  return (
    <Pressable
      className="flex-1"
      style={{ opacity }}
      onLongPress={handleChatLongPress}
    >
      <Animated.View style={[styles.container]}>
        <View className="relative">
          <Image
            source={item.image || require('~/assets/images/avatar.png')}
            className="size-10 rounded-full"
            priority="high"
          />
          {item.songRequestInfo && (
            <View className="absolute -bottom-1.5 right-[-7px]">
              <Image
                source={
                  item.songRequestInfo.isShoutout
                    ? require('~/assets/images/session/shoutout.png')
                    : require('~/assets/images/session/request.png')
                }
                className="size-6"
              />
            </View>
          )}
        </View>
        <View className="flex-1 gap-1">
          <View className="flex-row items-center gap-2">
            <SmBoldLabel>{item.username}</SmBoldLabel>
            {item.isCreator && <CreatorBadge />}
          </View>
          {item.text && <Tiny className="flex-1">{item.text}</Tiny>}
          {item.songRequestInfo &&
            item.songRequestInfo.status === 'PLAYING' && (
              <Tiny className="flex-1 italic">
                {item.isCreator
                  ? `▶️ Now Playing: ${item.songRequestInfo.title} - ${item.songRequestInfo.artist}`
                  : item.songRequestInfo.isShoutout
                    ? `Sent a shout out${item.songRequestInfo.title !== '' ? ' + song request' : ''}`
                    : 'Sent a song request'}
              </Tiny>
            )}
        </View>
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
});
