import { useColorScheme } from 'nativewind';
import React from 'react';

import {
  colors,
  Image,
  PinIcon,
  Pressable,
  SmBoldLabel,
  Tiny,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { LiveSessionChatMessage } from '@/lib/hooks/use-session-chat';

interface Props {
  avatar: string;
  username: string;
  message: string;
  isSessionOwner: boolean;
  handleChatLongPress?: (() => void) | undefined;
}
export const PinnedMessage: React.FC<Props> = ({
  avatar,
  username,
  message,
  isSessionOwner,
  handleChatLongPress,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Pressable
      className="flex-row items-center gap-3 px-4 py-2"
      onLongPress={handleChatLongPress}
    >
      <Image className="size-10 rounded-full" source={avatar} priority="high" />
      <View className="flex-1 gap-1">
        <View className="flex-row items-center gap-2">
          <SmBoldLabel>{username}</SmBoldLabel>
          {isSessionOwner && <CreatorBadge />}
        </View>
        <Tiny>{message}</Tiny>
      </View>
      <PinIcon color={isDark ? colors.white : colors.grey[100]} />
    </Pressable>
  );
};

export const CreatorBadge: React.FC = () => (
  <View className="rounded-full bg-brand-20 px-2 py-1 dark:bg-brand-90">
    <XsBoldLabel className="text-brand-70 dark:text-brand-40">
      Creator
    </XsBoldLabel>
  </View>
);
