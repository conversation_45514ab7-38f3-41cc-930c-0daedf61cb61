import React from 'react';

import { List, View } from '@/components/ui';
import { type LiveSessionChatMessage } from '@/lib/hooks/use-session-chat';

import { ChatMessage } from './message';

export interface ChatSectionProps {
  messages: LiveSessionChatMessage[];
  onMessageFade: (id: number | string) => void;
  scrollViewRef?: React.RefObject<any>;
  handleChatLongPress?: (item: LiveSessionChatMessage) => void;
}

export const ChatSection: React.FC<ChatSectionProps> = ({
  messages,
  scrollViewRef,
  onMessageFade,
  handleChatLongPress,
}) => {
  const getOpacity = (index: number) => {
    if (index < 5) {
      return 1; // Full opacity for the last 5 messages
    } else {
      // Calculate fading opacity for older messages
      // Minimum opacity of 0.5 for the oldest messages
      return Math.max(0.5, 1 - (index - 4) * 0.1);
    }
  };
  return (
    <List
      ref={scrollViewRef}
      data={messages}
      estimatedItemSize={74}
      inverted
      showsVerticalScrollIndicator={false}
      renderItem={({ item, index }) => (
        <ChatMessage
          item={item}
          onFadeComplete={() => onMessageFade(item.id)}
          opacity={getOpacity(index)}
          handleChatLongPress={() => handleChatLongPress?.(item)}
        />
      )}
      ItemSeparatorComponent={() => <View className="size-3" />}
      keyExtractor={(item) => String(item.id)}
    />
  );
};
