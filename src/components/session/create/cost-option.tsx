import React from 'react';

import { TouchableOpacity, XsBoldLabel } from '@/components/ui';

export type CostOptionProps = {
  option: string;
  onPress: () => void;
};

export const CostOption: React.FC<CostOptionProps> = ({ option, onPress }) => (
  <TouchableOpacity
    className="rounded-full bg-accent-subtle-light px-4 py-2 dark:bg-accent-subtle-dark"
    onPress={onPress}
  >
    <XsBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
      {option}
    </XsBoldLabel>
  </TouchableOpacity>
);
