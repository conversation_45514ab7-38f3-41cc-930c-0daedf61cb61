import React from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  type UseFormSetValue,
} from 'react-hook-form';

import { CostOption } from '@/components/session/create/cost-option';
import { ControlledInput, ScrollView, View } from '@/components/ui';
import { type CreateLiveFormType, formatAmount } from '@/lib';

interface CostSelectorProps<T extends FieldValues> {
  /**
   * The control from react-hook-form
   */
  control: Control<T>;

  /**
   * The setValue function from react-hook-form
   */
  setValue: UseFormSetValue<T>;

  /**
   * The name of the field in the form
   */
  name?: Path<T>;
  /**
   * Custom label for the cost input
   */
  label?: string;

  /**
   * Predefined cost options to display
   */
  costOptions?: number[];

  /**
   * Optional test ID for the input component
   */
  testID?: string;
}

/**
 * A component for inputing and selecting cost values with predefined options
 */
export const CostSelector: React.FC<CostSelectorProps<CreateLiveFormType>> = ({
  control,
  setValue,
  name = 'cost',
  label = 'Cost per request',
  costOptions = [0, 10000, 20000, 50000],
  testID = 'cost-input',
}) => {
  return (
    <View>
      <ControlledInput
        testID={testID}
        control={control}
        name={name}
        label={label}
        keyboardType="number-pad"
        onChangeText={(cost) => setValue(name, Number(cost))}
      />
      <ScrollView
        horizontal
        contentContainerStyle={{
          gap: 8,
        }}
      >
        {costOptions.map((cost, index) => (
          <CostOption
            key={index}
            option={cost === 0 ? 'Free' : formatAmount(cost)}
            onPress={() => setValue(name, String(cost))}
          />
        ))}
      </ScrollView>
    </View>
  );
};
