import React from 'react';

import { MdRegularLabel, SmRegularLabel, Switch, View } from '@/components/ui';

type FeatureToggleProps = {
  title: string;
  subtitle?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  accessibilityLabel: string;
  disabled?: boolean;
};

export const FeatureToggle: React.FC<FeatureToggleProps> = ({
  title,
  subtitle,
  checked,
  onChange,
  accessibilityLabel,
  disabled,
}) => (
  <View className="h-[54px] flex-row items-center justify-center gap-4 rounded-md bg-bg-subtle-light px-4 dark:bg-bg-subtle-dark">
    <View className="flex-1 gap-2.5">
      <MdRegularLabel>{title}</MdRegularLabel>
      {subtitle ? (
        <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
          {subtitle}
        </SmRegularLabel>
      ) : null}
    </View>
    <Switch
      checked={checked}
      onChange={onChange}
      nativeID={accessibilityLabel.toLowerCase().replace(/\s+/g, '-')}
      accessibilityLabel={accessibilityLabel}
      disabled={disabled}
    />
  </View>
);
