import React from 'react';

import { SmRegularLabel, TouchableOpacity } from '@/components/ui';
import { cn } from '@/lib';

type GenreOptionProps = {
  option: string;
  selected: boolean;
  onPress: (option: string) => void;
};

export const GenreOption: React.FC<GenreOptionProps> = ({
  option,
  selected,
  onPress,
}) => (
  <TouchableOpacity
    className={cn(
      'rounded-full border border-accent-muted-light dark:border-accent-muted-dark py-3 px-5',
      selected &&
        'bg-brand-60 dark:bg-brand-60 border-brand-60 dark:border-brand-60'
    )}
    onPress={() => onPress(option)}
  >
    <SmRegularLabel>{option}</SmRegularLabel>
  </TouchableOpacity>
);
