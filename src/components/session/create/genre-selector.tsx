import { Feather, Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import React from 'react';

import {
  colors,
  MdRegularLabel,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { cn } from '@/lib';

export type Genre = string;

type GenreSelectorProps = {
  selectedGenres: Genre[];
  onToggleGenre: (genre: Genre) => void;
  onOpenModal: () => void;
};

export const GenreSelector: React.FC<GenreSelectorProps> = ({
  selectedGenres,
  onToggleGenre,
  onOpenModal,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View
      className={cn(
        'border border-border-subtle-light dark:border-border-subtle-dark rounded-md h-[52px] pl-4 pr-3 flex-row items-center gap-3.5',
        selectedGenres.length > 0 && 'pl-1.5'
      )}
    >
      <View className="flex-1">
        {selectedGenres.length > 0 ? (
          <View className="flex-row gap-1.5">
            {selectedGenres.slice(0, 2).map((genre, index) => (
              <View
                key={index}
                className="h-8 flex-row items-center gap-2 rounded-full bg-accent-subtle-light pl-4 pr-3 dark:bg-accent-subtle-dark"
              >
                <MdRegularLabel>{genre}</MdRegularLabel>
                <TouchableOpacity
                  onPress={() => {
                    onToggleGenre(genre);
                  }}
                >
                  <Feather name="x" size={16} color="white" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        ) : (
          <MdRegularLabel>Genre</MdRegularLabel>
        )}
      </View>
      <TouchableOpacity onPress={onOpenModal}>
        <Ionicons
          name="chevron-down"
          size={24}
          color={isDark ? colors.grey[50] : colors.grey[60]}
        />
      </TouchableOpacity>
    </View>
  );
};
