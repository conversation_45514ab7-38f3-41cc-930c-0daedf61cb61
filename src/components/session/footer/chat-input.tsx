import React from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';

import { SendIcon } from '@/components/ui';

interface ChatInputProps {
  message: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  message,
  onChangeText,
  onSend,
}) => (
  <View className="relative flex-1 overflow-hidden">
    <TextInput
      placeholder="Add comment"
      placeholderClassName="text-base/100  font-aeonik-regular text-grey-50 dark:text-grey-60"
      className="max-h-24 overflow-hidden rounded-full bg-grey-20 p-4 pr-14 text-grey-100 dark:bg-grey-80 dark:text-white"
      value={message}
      onChangeText={onChangeText}
      multiline
      scrollEnabled={true}
    />
    <TouchableOpacity
      className="absolute right-3 top-1/2 size-8 -translate-y-1/2 items-center justify-center rounded-full bg-brand-60 dark:bg-brand-60"
      onPress={onSend}
    >
      <SendIcon />
    </TouchableOpacity>
  </View>
);
