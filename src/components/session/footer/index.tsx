import React from 'react';

import { View } from '@/components/ui';
import { useGetLiveSessionQueueQuery, useRefetchOnFocus } from '@/lib';

import { ChatInput } from './chat-input';
import { MoreButton } from './more';
import { RequestButton } from './request';
import { ShareButton } from './share';

export interface SessionFooterProps {
  sessionId: string;
  message: string;
  setNewMessage: React.Dispatch<React.SetStateAction<string>>;
  handleSend: () => void;
  isSessionOwner: boolean;
  handleRequestOpen: () => void;
}

export const SessionFooter: React.FC<SessionFooterProps> = ({
  sessionId,
  message,
  setNewMessage,
  handleSend,
  isSessionOwner,
  handleRequestOpen,
}) => {
  const { data: queueData, refetch } = useGetLiveSessionQueueQuery(sessionId);
  useRefetchOnFocus(refetch, 0);

  const requests = queueData?.requests ?? [];

  const sessionOwnerRequestCount = React.useMemo(
    () => requests.length,
    [requests]
  );

  return (
    <View className="w-full flex-row items-center gap-2.5 p-4">
      <ChatInput
        message={message}
        onChangeText={setNewMessage}
        onSend={handleSend}
      />
      {isSessionOwner && (
        <RequestButton
          onPress={handleRequestOpen}
          count={sessionOwnerRequestCount}
        />
      )}
      <ShareButton sessionId={sessionId} />
      {!isSessionOwner && <MoreButton />}
    </View>
  );
};
