import { Feather, Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { type ReactNode } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import {
  Button,
  colors,
  MdRegularLabel,
  Modal,
  Pressable,
  Tiny,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';

import { ReportModal } from '../report';

export const MoreButton: React.FC = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const snapPoints = React.useMemo(() => ['26%'], []);
  const reportSnapPoints = React.useMemo(() => ['63%'], []);
  const moreOptionModal = useModal();
  const reportModal = useModal();

  return (
    <>
      <TouchableOpacity
        className="items-center gap-1"
        onPress={moreOptionModal.present}
      >
        <Feather
          name="more-horizontal"
          size={24}
          color={isDark ? colors.white : colors.grey[100]}
        />
        <Tiny>More</Tiny>
      </TouchableOpacity>

      <Modal
        ref={moreOptionModal.ref}
        index={0}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
        }}
      >
        <BottomSheetView
          className="gap-4 px-4"
          style={{
            paddingBottom: insets.bottom,
          }}
        >
          <View className="rounded-lg bg-bg-surface-light px-4 dark:bg-bg-surface-dark">
            <MoreItem
              icon={
                <Ionicons
                  name="eye-outline"
                  size={24}
                  color={isDark ? colors.white : colors.grey[100]}
                />
              }
              item="Viewers"
              onPress={() => {
                moreOptionModal.dismiss();
                router.push({
                  pathname: '/session/[id]/viewers',
                  params: { id },
                });
              }}
            />
            <MoreItem
              icon={
                <Ionicons
                  name="warning-outline"
                  size={24}
                  color={isDark ? colors.white : colors.grey[100]}
                />
              }
              item="Report this live"
              onPress={reportModal.present}
            />
          </View>
          <Button
            className="bg-accent-subtle-light dark:bg-accent-subtle-dark"
            label="Cancel"
            textClassName="text-accent-bold-light dark:text-accent-bold-dark"
            onPress={moreOptionModal.dismiss}
          />
        </BottomSheetView>
      </Modal>

      <ReportModal
        ref={reportModal.ref}
        dismiss={reportModal.dismiss}
        onSubmit={() => {
          toast.success('Your report has been submitted successfully');
          moreOptionModal.dismiss();
          reportModal.dismiss();
        }}
        reportSnapPoints={reportSnapPoints}
      />
    </>
  );
};

const MoreItem: React.FC<{
  item: string;
  icon: ReactNode;
  onPress: () => void;
}> = ({ icon, item, onPress }) => (
  <Pressable className="h-16 flex-row items-center gap-4" onPress={onPress}>
    <View className="flex-1">
      <MdRegularLabel>{item}</MdRegularLabel>
    </View>
    {icon}
  </Pressable>
);
