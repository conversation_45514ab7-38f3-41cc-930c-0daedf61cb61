/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import {
  Image,
  Tiny,
  TouchableOpacity,
  View,
  XsRegularLabel,
} from '@/components/ui';

interface RequestButtonProps {
  onPress: () => void;
  count: number;
}

export const RequestButton: React.FC<RequestButtonProps> = ({
  onPress,
  count,
}) => (
  <TouchableOpacity className="relative items-center gap-1" onPress={onPress}>
    <Image
      source={require('~/assets/images/session/request.png')}
      className="size-6"
    />
    <Tiny>Request</Tiny>
    {!!count && (
      <View className="absolute -right-1 -top-[7px] size-5 items-center justify-center rounded-full bg-red-60 dark:bg-red-60">
        <XsRegularLabel className="text-white">{count}</XsRegularLabel>
      </View>
    )}
  </TouchableOpacity>
);
