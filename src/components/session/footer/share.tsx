import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { useRef } from 'react';

import { ShareModal } from '@/components/modals/share';
import { colors, Tiny, TouchableOpacity } from '@/components/ui';
import { APP_URL } from '@/lib';

export const ShareButton: React.FC<{ sessionId: string }> = ({ sessionId }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const shareModalRef = useRef<any>(null);
  // const shareModal = useModal();
  const handleShare = () => shareModalRef.current?.present();

  return (
    <>
      <TouchableOpacity
        className="size-11 items-center gap-1"
        onPress={handleShare}
      >
        <Feather
          name="share"
          size={24}
          color={isDark ? colors.white : colors.grey[100]}
        />
        <Tiny>Share</Tiny>
      </TouchableOpacity>
      <ShareModal
        shareModalRef={shareModalRef}
        onDismiss={() => shareModalRef.current?.dismiss()}
        content={`${APP_URL}/session/${sessionId}`}
      />
    </>
  );
};
