import { SmBoldLabel, TouchableOpacity } from '@/components/ui';

interface Props {
  onEndSession: () => void;
}
export const EndSession: React.FC<Props> = ({ onEndSession }) => {
  return (
    <TouchableOpacity
      className="h-[30px] items-center justify-center rounded-full bg-red-50 px-4"
      onPress={onEndSession}
    >
      <SmBoldLabel className="text-white dark:text-white">End Live</SmBoldLabel>
    </TouchableOpacity>
  );
};
