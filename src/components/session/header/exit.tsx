import { Feather } from '@expo/vector-icons';

import { TouchableOpacity } from '@/components/ui';

interface Props {
  onExitSession: () => void;
}
export const ExitSession: React.FC<Props> = ({ onExitSession }) => {
  return (
    <TouchableOpacity
      className="size-10 items-center justify-center rounded-full bg-bg-danger-primary opacity-80"
      onPress={onExitSession}
    >
      <Feather name="x" size={20} color="white" />
    </TouchableOpacity>
  );
};
