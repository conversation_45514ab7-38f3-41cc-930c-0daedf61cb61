import { useState } from 'react';

import { SmBoldLabel, TouchableOpacity } from '@/components/ui';

export const FavouriteButton: React.FC = () => {
  const [isFavourited, setIsFavourited] = useState(false);

  return (
    <TouchableOpacity
      className="items-center justify-center rounded-full bg-brand-60 px-4 py-2"
      onPress={() => setIsFavourited(!isFavourited)}
    >
      <SmBoldLabel className="text-white dark:text-white">
        {isFavourited ? 'Unfavourite' : 'Favourite'}
      </SmBoldLabel>
    </TouchableOpacity>
  );
};
