import React from 'react';
import { TouchableOpacity } from 'react-native';

import { type DJProfile } from '@/api/session';
import {
  UserActionModal,
  type UserActionModalRef,
} from '@/components/modals/quester-action-modal';
import {
  CameraHideIcon,
  CameraIcon,
  Image,
  MuteIcon,
  P,
  Pressable,
  SmBoldLabel,
  SwitchToggleIcon,
  UnmuteIcon,
  View,
} from '@/components/ui';

import { EndSession } from './end';
import { ExitSession } from './exit';
import { LiveStatusIndicator } from './live-indicator';
import { Viewers } from './viewers';
// import { WalletBalance } from './wallet-balance';

interface Props {
  isSessionOwner: boolean;
  onEndSession: () => void;
  onExitSession: () => void;
  onSwitchCamera?: () => void;
  onMutePress?: () => void;
  showCamera: () => Promise<void>;
  hideCamera: () => Promise<void>;
  isMuted: boolean;
  isCameraHidden: boolean;
  dj: DJProfile;
  viewersCount?: number;
  walletBalance?: number;
  isSessionVideoEnabled?: boolean;
}
export const SessionHeader: React.FC<Props> = ({
  isSessionOwner,
  onEndSession,
  onExitSession,
  isMuted,
  onMutePress,
  onSwitchCamera,
  dj,
  viewersCount = 0,
  // walletBalance = 0,
  isSessionVideoEnabled,
  hideCamera,
  showCamera,
  isCameraHidden,
}) => {
  const userActionModalRef = React.useRef<UserActionModalRef>(null);

  const handleAvatarPress = React.useCallback(() => {
    userActionModalRef.current?.present();
  }, []);

  return (
    <View className="flex-row items-center gap-4 px-4">
      <View className="flex-1 flex-row items-center gap-2">
        {!isSessionOwner && (
          <Pressable onPress={handleAvatarPress}>
            <Image
              className="size-12 rounded-full"
              source={
                dj.profileImageUrl || require('~/assets/images/avatar.png')
              }
              priority="high"
            />
          </Pressable>
        )}
        <View className="flex-1 gap-2">
          <SmBoldLabel>{isSessionOwner ? 'You' : dj.username}</SmBoldLabel>
          <View className="flex-row items-center gap-2">
            <LiveStatusIndicator />
            <Viewers count={viewersCount} />
          </View>
        </View>
      </View>

      <View className="flex-row items-center gap-2">
        {isSessionOwner ? (
          <>
            {isSessionVideoEnabled && (
              <>
                <TouchableOpacity
                  className="size-10 items-center justify-center rounded-full bg-black/20"
                  onPress={onSwitchCamera}
                >
                  <SwitchToggleIcon />
                </TouchableOpacity>
                <TouchableOpacity
                  className="size-10 items-center justify-center rounded-full bg-black/20"
                  onPress={isCameraHidden ? showCamera : hideCamera}
                >
                  {isCameraHidden ? <CameraHideIcon /> : <CameraIcon />}
                </TouchableOpacity>
                <TouchableOpacity
                  className="size-10 items-center justify-center rounded-full bg-black/20"
                  onPress={onMutePress}
                >
                  <P>{isMuted ? <UnmuteIcon /> : <MuteIcon />}</P>
                </TouchableOpacity>
              </>
            )}
            {/* <WalletBalance balance={walletBalance} /> */}
            <EndSession onEndSession={onEndSession} />
          </>
        ) : (
          <ExitSession onExitSession={onExitSession} />
        )}
      </View>
      <UserActionModal
        ref={userActionModalRef}
        user={{ ...dj, id: dj.id || '' }}
      />
    </View>
  );
};
