import { useLocalSearchParams, useRouter } from 'expo-router';

import { PeopleIcon, TouchableOpacity, XsBoldLabel } from '@/components/ui';

interface Props {
  count: number;
}
export const Viewers: React.FC<Props> = ({ count }) => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { push } = useRouter();

  return (
    <TouchableOpacity
      className="w-12 flex-row items-center justify-center gap-1 rounded-sm"
      onPress={() =>
        push({
          pathname: '/session/[id]/viewers',
          params: { id },
        })
      }
    >
      <PeopleIcon />
      <XsBoldLabel>{count}</XsBoldLabel>
    </TouchableOpacity>
  );
};
