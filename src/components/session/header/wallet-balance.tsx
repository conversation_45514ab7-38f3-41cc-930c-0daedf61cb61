import { colors, PaperMoneyIcon, View, XsBoldLabel } from '@/components/ui';
import { formatAmount } from '@/lib';

interface Props {
  balance: number;
}
export const WalletBalance: React.FC<Props> = ({ balance }) => {
  return (
    <View className="h-6 flex-row items-center gap-1 rounded-md bg-bg-overlay-light px-2 dark:bg-bg-overlay-dark">
      <PaperMoneyIcon height={16} width={16} color={colors.white} />
      <XsBoldLabel>{formatAmount(balance / 100)}</XsBoldLabel>
    </View>
  );
};
