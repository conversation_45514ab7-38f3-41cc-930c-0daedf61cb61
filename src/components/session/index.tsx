/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import { useColorScheme } from 'nativewind';
import React from 'react';
import { toast } from 'sonner-native';

import {
  type Queue,
  type RequestStatus,
  useUpdateSongRequest,
} from '@/api/session';
import { SearchSongModalContent } from '@/components/modals/search-song';
import { LiveRequestItem } from '@/components/session/request/item';
import {
  Button,
  colors,
  H1,
  Image,
  List,
  MdRegularLabel,
  Modal,
  P,
  TouchableOpacity,
  useModal,
  View,
  XsBoldLabel,
  XsRegularLabel,
} from '@/components/ui';
import {
  cn,
  useGetLiveSessionQuery,
  useGetLiveSessionQueueQuery,
  useInitializeSpotify,
  useLoggedInUser,
} from '@/lib';
import { type TabType } from '@/types';

import { Back } from '../common/back';

export const SessionTabsComponent: React.FC<{
  sessionId: string;
  className?: string;
  hasHeader?: boolean;
}> = ({ className, hasHeader, sessionId }) => {
  useInitializeSpotify();

  const { data: sessionData } = useGetLiveSessionQuery(sessionId);
  const sessionInfo = sessionData?.djSession;
  console.log('🚀 ~ SessionTabsComponent ~ sessionInfo:', sessionInfo?.type);

  const queueEnabled = React.useMemo(
    () => !!sessionData && sessionData.djSession?.status !== 'ENDED',
    [sessionData]
  );
  const { data: queueData } = useGetLiveSessionQueueQuery(sessionId, {
    enabled: queueEnabled,
  });

  const playQueue = queueData?.playQueue ?? [];
  const playedSongs = queueData?.playedSongs ?? [];
  const requests = queueData?.requests ?? [];
  const queue = React.useMemo(
    () => (Array.isArray(queueData) ? queueData : queueData?.queue || []),
    [queueData]
  );

  const { mutate: updateSongRequest } = useUpdateSongRequest();
  const modal = useModal();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const snapPoints = React.useMemo(() => ['60%', '80%'], []);

  const { data: user } = useLoggedInUser();

  const isSessionCreator = React.useMemo(
    () => user?.username === sessionInfo?.dj.username,
    [user?.username, sessionInfo?.dj.username]
  );

  const handleSongRequestUpdate = React.useCallback(
    (songId: string, status: RequestStatus) => {
      updateSongRequest(
        { sessionId, songId, status },
        { onError: (error) => toast.error(error.message) }
      );
    },
    [sessionId, updateSongRequest]
  );

  const sessionOwnerRequestCount = React.useMemo(
    () => requests.length,
    [requests]
  );

  const sessionOwnerQueueCount = React.useMemo(
    () => playQueue.length,
    [playQueue]
  );

  const [selectedTab, setSelectedTab] = React.useState<TabType>('Request');

  const filteredRequests = React.useMemo(() => {
    switch (selectedTab) {
      case 'Request':
        return isSessionCreator
          ? requests
          : queue.filter(
              (item) => item.status === 'PENDING' || item.status === 'ACCEPTED'
            );
      case 'Queue':
        return playQueue;
      case 'History':
        return isSessionCreator
          ? playedSongs
          : queue.filter(
              (item) =>
                item.status === 'CANCELLED' ||
                item.status === 'REJECTED' ||
                item.status === 'PLAYED'
            );
      default:
        return requests;
    }
  }, [selectedTab, requests, isSessionCreator, queue, playQueue, playedSongs]);

  const handleAccept = React.useCallback(
    (id: string) => {
      handleSongRequestUpdate(id, 'ACCEPTED');
    },
    [handleSongRequestUpdate]
  );

  const handleDecline = React.useCallback(
    (id: string) => {
      handleSongRequestUpdate(id, 'REJECTED');
    },
    [handleSongRequestUpdate]
  );

  const renderItem = React.useCallback(
    ({ item }: { item: Queue }) => (
      <LiveRequestItem
        {...item}
        onAccept={handleAccept}
        onDecline={handleDecline}
        isModal={!hasHeader}
        sessionId={sessionId}
      />
    ),
    [handleAccept, handleDecline, hasHeader, sessionId]
  );
  const keyExtractor = React.useCallback(
    (item: Queue) => String(item.songId ?? item.id),
    []
  );
  const Separator = () => <View className="size-2" />;
  const EmptyState = React.useCallback(
    () => (
      <View className="mt-16 flex-1 items-center gap-4">
        <Image
          className="size-[186px]"
          source={require('~/assets/images/session/empty.png')}
        />
        <MdRegularLabel>It's empty here</MdRegularLabel>
      </View>
    ),
    []
  );

  return (
    <>
      <View className={cn('flex-1', className)}>
        {hasHeader && (
          <>
            <View className="px-4 py-3">
              <Back />
            </View>

            {/* Title and subtitle */}
            <View className="gap-1 px-4 pb-4">
              <H1>Live request</H1>
              <P className="text-grey-60 dark:text-grey-50">
                Manage all your live session requests
              </P>
            </View>
          </>
        )}
        {/* Tab */}
        <View className="px-4">
          <View className="h-12 w-full flex-row items-center rounded-full bg-grey-10 p-0.5 dark:bg-grey-90">
            <TouchableOpacity
              className={cn(
                'relative text-grey-50 dark:text-grey-60 flex-1 items-center bg-transparent h-full justify-center',
                selectedTab === 'Request' &&
                  'bg-white dark:bg-grey-80 rounded-full'
              )}
              onPress={() => setSelectedTab('Request')}
            >
              <XsBoldLabel
                className={cn(
                  'text-grey-50 dark:text-grey-60',
                  selectedTab === 'Request' &&
                    'text-brand-60 dark:text-brand-50'
                )}
              >
                Request
              </XsBoldLabel>
              {!!sessionOwnerRequestCount && (
                <View className="absolute -top-[9px] right-[7px] size-5 items-center justify-center rounded-full bg-red-60 dark:bg-red-60">
                  <XsRegularLabel className="text-white">
                    {sessionOwnerRequestCount}
                  </XsRegularLabel>
                </View>
              )}
            </TouchableOpacity>
            {isSessionCreator && (
              <TouchableOpacity
                className={cn(
                  'text-grey-50 dark:text-grey-60 flex-1 items-center bg-transparent h-full justify-center',
                  selectedTab === 'Queue' &&
                    'bg-white dark:bg-grey-80 rounded-full'
                )}
                onPress={() => setSelectedTab('Queue')}
              >
                <XsBoldLabel
                  className={cn(
                    'text-grey-50 dark:text-grey-60',
                    selectedTab === 'Queue' &&
                      'text-brand-60 dark:text-brand-50'
                  )}
                >
                  Queue
                </XsBoldLabel>
                {!!sessionOwnerQueueCount && (
                  <View className="absolute -top-[9px] right-[7px] size-5 items-center justify-center rounded-full bg-red-60 dark:bg-red-60">
                    <XsRegularLabel className="text-white">
                      {sessionOwnerQueueCount}
                    </XsRegularLabel>
                  </View>
                )}
              </TouchableOpacity>
            )}

            <TouchableOpacity
              className={cn(
                'text-grey-50 dark:text-grey-60 flex-1 items-center bg-transparent h-full justify-center',
                selectedTab === 'History' &&
                  'bg-white dark:bg-grey-80 rounded-full'
              )}
              onPress={() => setSelectedTab('History')}
            >
              <XsBoldLabel
                className={cn(
                  'text-grey-50 dark:text-grey-60',
                  selectedTab === 'History' &&
                    'text-brand-60 dark:text-brand-50'
                )}
              >
                History
              </XsBoldLabel>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tab View */}
        <View className="flex-1 gap-2 p-4">
          <List
            data={filteredRequests}
            estimatedItemSize={74}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={EmptyState}
            renderItem={renderItem}
            ItemSeparatorComponent={Separator}
            keyExtractor={keyExtractor}
          />
        </View>
      </View>

      {!isSessionCreator && sessionInfo?.type === 'REQUEST' && (
        <Button
          className="mx-4 mb-6"
          label="Request a Song"
          onPress={modal.present}
        />
      )}

      <Modal
        ref={modal.ref}
        index={0}
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
        snapPoints={snapPoints}
      >
        <SearchSongModalContent
          onDismiss={modal.dismiss}
          sessionId={sessionId}
        />
      </Modal>
    </>
  );
};
