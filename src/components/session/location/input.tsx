import { useColorScheme } from 'nativewind';
import React from 'react';
import { TextInput } from 'react-native';

import { colors, LocationIcon, View } from '@/components/ui';

type PhysicalLocationInputProps = {
  address: string;
  setAddress: (value: string) => void;
  onBlur: () => void;
  onFocus: () => void;
};

export const PhysicalLocationInput: React.FC<PhysicalLocationInputProps> = ({
  address,
  setAddress,
  onBlur,
  onFocus,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="relative">
      <TextInput
        className="h-12 flex-row items-center gap-3 rounded-md bg-bg-subtle-light pl-12 pr-3 text-base/100 text-grey-100 dark:bg-bg-subtle-dark dark:text-white"
        placeholder="Enter address"
        placeholderClassName="text-grey-50 dark:text-grey-60 text-base/100 font-aeonik-black font-bold"
        value={address}
        onChangeText={setAddress}
        onBlur={onBlur}
        onFocus={onFocus}
      />
      <View className="absolute left-3 top-3">
        <LocationIcon
          color="transparent"
          strokeColor={isDark ? colors.grey[60] : colors.grey[50]}
        />
      </View>
    </View>
  );
};
