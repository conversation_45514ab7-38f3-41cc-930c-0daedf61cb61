import React from 'react';
import { View } from 'react-native';

import {
  MdRegularLabel,
  RadioGroupItem,
  SmRegularLabel,
} from '@/components/ui';

type LocationOptionProps = {
  value: string;
  currentValue: string;
  title: string;
  description: string;
  customContent?: React.ReactNode;
  onPress?: () => void;
  labelClassName?: string;
};

export const LocationOption: React.FC<LocationOptionProps> = ({
  value,
  currentValue,
  title,
  description,
  customContent,
  onPress,
  labelClassName,
}) => {
  const isSelected = currentValue === value;

  return (
    <View className="flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 py-3 dark:bg-bg-subtle-dark">
      <View className="flex-1 gap-2.5">
        <MdRegularLabel className={labelClassName}>{title}</MdRegularLabel>
        {customContent || (
          <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
            {description}
          </SmRegularLabel>
        )}
      </View>
      <View className="items-center justify-center">
        <RadioGroupItem
          value={value}
          aria-selected={isSelected}
          aria-labelledby={`label-for-${value}`}
          onPress={onPress}
        />
      </View>
    </View>
  );
};
