import { Feather } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { type BottomSheetModalMethods } from '@gorhom/bottom-sheet/src/types';
import { useColorScheme } from 'nativewind';
import React, { forwardRef } from 'react';
import { TextInput } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  Button,
  colors,
  LgBoldLabel,
  Modal,
  P,
  RadioGroup,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { REPORT_OPTIONS } from '@/lib';
import { type ReportType } from '@/types';

import { ReportItem } from './item';

interface ReportModalProps {
  reportSnapPoints: string[];
  dismiss: () => void;
  onSubmit: () => void;
}

export const ReportModal = forwardRef<
  BottomSheetModalMethods,
  ReportModalProps
>(({ reportSnapPoints, dismiss, onSubmit }, ref) => {
  const insets = useSafeAreaInsets();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [reportType, setReportType] = React.useState<ReportType>('none');

  return (
    <Modal
      ref={ref}
      index={1}
      snapPoints={reportSnapPoints}
      backgroundStyle={{
        backgroundColor: isDark ? colors.grey[100] : colors.white,
      }}
    >
      <BottomSheetView
        style={{
          paddingBottom: insets.bottom,
        }}
      >
        <View className="h-16 flex-row items-center justify-between px-2">
          <TouchableOpacity onPress={dismiss}>
            <Feather
              name="x"
              size={32}
              color={isDark ? colors.white : colors.grey[100]}
            />
          </TouchableOpacity>
          <LgBoldLabel>Report</LgBoldLabel>
          <View className="size-8" />
        </View>

        <RadioGroup
          className="px-4"
          value={reportType}
          onValueChange={(value) => setReportType(value as ReportType)}
        >
          <P className="text-fg-muted-light dark:text-fg-muted-dark">
            This will only be shared with us.
          </P>
          {REPORT_OPTIONS.map(({ label, value }, index) => (
            <ReportItem
              key={index}
              label={label}
              onPress={() => {
                setReportType(value);
              }}
              currentValue={reportType}
              value={value}
            />
          ))}
        </RadioGroup>

        <View className="gap-4 px-4 pt-6">
          <TextInput
            className="h-[100px] rounded-md border border-border-subtle-light p-4 font-aeonik-regular text-base/150 text-grey-100 dark:border-border-subtle-dark dark:text-white"
            multiline
            placeholder="Write a report"
            placeholderClassName="text-fg-subtle-light dark:text-fg-subtle-dark text-base/150 font-aeonik-regular"
          />
          <Button label="Submit" onPress={onSubmit} />
        </View>
      </BottomSheetView>
    </Modal>
  );
});

ReportModal.displayName = 'ReportModal';
