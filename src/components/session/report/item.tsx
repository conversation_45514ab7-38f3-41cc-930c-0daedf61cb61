import { MdRegularLabel, Pressable, RadioGroupItem } from '@/components/ui';
import { type ReportType } from '@/types';

interface ReportTypeProps {
  label: string;
  value: string;
  currentValue: ReportType;
  onPress?: () => void;
}

export const ReportItem: React.FC<ReportTypeProps> = ({
  label,
  value,
  currentValue,
  onPress,
}) => {
  const isSelected = currentValue === value;

  return (
    <Pressable
      className="h-16 flex-row items-center gap-4 border-b border-b-border-subtle-light dark:border-b-border-subtle-dark"
      onPress={onPress}
    >
      <MdRegularLabel className="flex-1">{label}</MdRegularLabel>
      <RadioGroupItem
        value={value}
        aria-selected={isSelected}
        aria-labelledby={`label-for-${value}`}
        onPress={onPress}
      />
    </Pressable>
  );
};
