import { Ionicons } from '@expo/vector-icons';
import React from 'react';

import { type RequestStatus } from '@/api/session';
import {
  Image,
  MicIcon,
  SmBoldLabel,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { cn } from '@/lib';

interface ActionButtonsProps {
  id: string;
  status: RequestStatus;
  isShoutout: boolean;
  isSessionCreator: boolean;
  isBoosted?: boolean;
  isSessionHistory?: boolean;
  onAccept?: (id: string) => void;
  onDecline?: (id: string) => void;
  onView?: () => void;
  onCompleteView?: () => void;
  onBoostRequest?: () => void;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  id,
  status,
  isShoutout,
  isSessionCreator,
  isBoosted,
  isSessionHistory,
  onAccept,
  onDecline,
  onView,
  onCompleteView,
  onBoostRequest,
}) => {
  if (status === 'PENDING') {
    if (isSessionCreator) {
      if (isShoutout) {
        return (
          <TouchableOpacity
            className="justify-center rounded-full bg-brand-60 px-4 py-2"
            onPress={onView}
          >
            <SmBoldLabel className="text-white dark:text-white">
              View
            </SmBoldLabel>
          </TouchableOpacity>
        );
      } else {
        return (
          <>
            <TouchableOpacity
              className="justify-center rounded-full bg-red-50 px-4 py-2"
              onPress={() => onDecline?.(id)}
            >
              <SmBoldLabel className="text-white dark:text-white">
                Decline
              </SmBoldLabel>
            </TouchableOpacity>
            <TouchableOpacity
              className="justify-center rounded-full bg-brand-60 px-4 py-2"
              onPress={() => onAccept?.(id)}
            >
              <SmBoldLabel className="text-white dark:text-white">
                Accept
              </SmBoldLabel>
            </TouchableOpacity>
          </>
        );
      }
    } else {
      return (
        <>
          <TouchableOpacity
            className={cn(
              'size-8 items-center justify-center rounded-md bg-green-60 dark:bg-green-50',
              !isBoosted && 'bg-accent-subtle-light dark:bg-accent-subtle-dark'
            )}
            onPress={onBoostRequest}
          >
            <Image
              source={require('~/assets/images/session/lightening.png')}
              className="size-4"
            />
          </TouchableOpacity>
          <SmBoldLabel className="text-bg-warning-contrast dark:text-bg-warning-contrast">
            Pending
          </SmBoldLabel>
        </>
      );
    }
  } else if (status === 'PLAYED') {
    return (
      <>
        {isBoosted && (
          <View className="size-6 items-center justify-center rounded-md bg-green-10 dark:bg-green-80">
            <Image
              source={require('~/assets/images/session/lightening.png')}
              className="size-4"
            />
          </View>
        )}
        <View
          className={cn(
            'w-[88px] flex-row items-center justify-center gap-1 rounded-full bg-green-60 px-4 py-2 dark:bg-green-50',
            (isSessionCreator || isSessionHistory) && 'px-2'
          )}
        >
          <SmBoldLabel className="text-white dark:text-white">
            {isSessionCreator || isSessionHistory ? 'Completed' : 'Played'}
          </SmBoldLabel>
        </View>
      </>
    );
  } else if (status === 'REJECTED' || status === 'CANCELLED') {
    return (
      <View className="w-[88px] justify-center rounded-full bg-red-50 px-4 py-2">
        <SmBoldLabel className="text-white dark:text-white">
          Declined
        </SmBoldLabel>
      </View>
    );
  } else {
    if (isSessionCreator) {
      return (
        <>
          {isBoosted && (
            <View className="size-6 items-center justify-center rounded-md bg-green-10 dark:bg-green-80">
              <Image
                source={require('~/assets/images/session/lightening.png')}
                className="size-4"
              />
            </View>
          )}
          <TouchableOpacity
            className="flex-row justify-center gap-1 rounded-full bg-green-60 py-2 pl-2 pr-3 dark:bg-green-50"
            onPress={onCompleteView}
          >
            {isShoutout ? (
              <MicIcon />
            ) : (
              <Ionicons name="play" size={16} color="white" />
            )}

            <SmBoldLabel className="text-white dark:text-white">
              {isShoutout ? 'Complete' : 'Play'}
            </SmBoldLabel>
          </TouchableOpacity>
        </>
      );
    } else {
      return (
        <>
          {isBoosted && (
            <View className="size-8 items-center justify-center rounded-md bg-green-60 dark:bg-green-50">
              <Image
                source={require('~/assets/images/session/lightening.png')}
                className="size-4"
              />
            </View>
          )}
          <SmBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
            On queue
          </SmBoldLabel>
        </>
      );
    }
  }
};
