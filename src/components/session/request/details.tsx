import React from 'react';

import { MdRegularLabel, SmRegularLabel, View } from '@/components/ui';

interface RequestDetailsProps {
  title: string;
  subtitle?: string;
}

export const RequestDetails: React.FC<RequestDetailsProps> = ({
  title,
  subtitle,
}) => {
  return (
    <View className="flex-1 gap-2">
      <MdRegularLabel numberOfLines={1}>{title}</MdRegularLabel>
      {subtitle && (
        <SmRegularLabel
          numberOfLines={1}
          className="text-grey-60 dark:text-grey-50"
        >
          {subtitle}
        </SmRegularLabel>
      )}
    </View>
  );
};
