import React from 'react';

import { View, XsRegularLabel } from '@/components/ui';
import { cn } from '@/lib';

interface RequestFooterProps {
  username?: string;
  timestamp: string;
  isSessionCreator?: boolean;
  isHistoryItem?: boolean;
  sessionName?: string;
  djName?: string;
}

export const RequestFooter: React.FC<RequestFooterProps> = ({
  username,
  timestamp,
  isSessionCreator,
  isHistoryItem = false,
  sessionName,
  djName,
}) => {
  return (
    <View
      className={cn(
        'flex-row justify-between',
        !isSessionCreator && 'justify-end',
        isHistoryItem && 'justify-between'
      )}
    >
      {isSessionCreator && (
        <XsRegularLabel className="text-grey-60 dark:text-grey-50">
          Request from {username}
        </XsRegularLabel>
      )}
      {isHistoryItem && (
        <XsRegularLabel className="text-grey-60 dark:text-grey-50">
          Session: {sessionName} by {djName}
        </XsRegularLabel>
      )}

      <XsRegularLabel className="text-grey-60 dark:text-grey-50">
        {timestamp}
      </XsRegularLabel>
    </View>
  );
};
