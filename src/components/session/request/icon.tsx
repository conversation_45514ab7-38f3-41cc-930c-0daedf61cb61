import { useColorScheme } from 'nativewind';
import React from 'react';

import { Image, View } from '@/components/ui';
import { ShoutoutIcon } from '@/components/ui';
import { colors } from '@/components/ui';

interface RequestIconProps {
  isShoutout?: boolean;
  iconSource?: string;
}

export const RequestIcon: React.FC<RequestIconProps> = ({
  isShoutout,
  iconSource,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (isShoutout) {
    return (
      <View className="size-10 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
        <ShoutoutIcon color={isDark ? colors.brand[40] : colors.brand[70]} />
      </View>
    );
  }

  return (
    <Image
      source={iconSource || require('~/assets/gradient-bg/event-gradient.png')}
      className="size-10 rounded-md"
      priority="high"
      transition={200}
    />
  );
};
