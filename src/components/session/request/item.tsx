import { Feather } from '@expo/vector-icons';
import { BottomSheetView, useBottomSheetModal } from '@gorhom/bottom-sheet';
import {
  addDoc,
  collection,
  getFirestore,
  serverTimestamp,
} from '@react-native-firebase/firestore';
import dayjs from 'dayjs';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { Modal as RNModal, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { type Queue, useBoostSongRequest } from '@/api/session';
import { BlurredBackground } from '@/components/background/blur';
import { CompletedRequestModal } from '@/components/modals/complete-request';
import { ShoutoutRequestModal } from '@/components/modals/shoutout-request';
import SongRecordingModal from '@/components/modals/song-recording';
import {
  Button,
  colors,
  Image,
  LgBoldLabel,
  MdBoldLabel,
  MdRegularLabel,
  Modal,
  P,
  PersistentModal,
  TouchableOpacity,
  useModal,
  View,
} from '@/components/ui';
import {
  cn,
  formatAmount,
  removeUndefined,
  toAmountInMajor,
  useGetLiveSessionQuery,
  useLoggedInUser,
  useVisibility,
} from '@/lib';

import { ActionButtons } from './action-buttons';
import { RequestDetails } from './details';
import { RequestFooter } from './footer';
import { RequestIcon } from './icon';
import { toast } from 'sonner-native';
import { useRouter } from 'expo-router';

interface LiveRequestItemProps extends Queue {
  sessionId: string;
  onAccept?: (id: string) => void;
  onDecline?: (id: string) => void;
  isModal?: boolean;
}

export const LiveRequestItem: React.FC<LiveRequestItemProps> = ({
  id,
  song,
  songId,
  requestedBy,
  timestamp,
  status,
  isBoosted,
  onAccept,
  onDecline,
  isModal,
  shoutOuts,
  shoutOutTo,
  sessionId,
}) => {
  const { push } = useRouter();
  const insets = useSafeAreaInsets();
  const { data: user } = useLoggedInUser();
  const { data: sessionData } = useGetLiveSessionQuery(sessionId);

  const sessionInfo = sessionData?.djSession;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = styling(isDark);
  const modal = useModal();
  const viewShoutoutModal = useModal();
  const boostConfirmationModal = useModal();
  const { dismissAll: dismissAllModals } = useBottomSheetModal();
  const { isOpen, onOpen, onClose } = useVisibility();

  const { mutate: boostSong } = useBoostSongRequest();

  const [isPlaying, setIsPlaying] = React.useState(false);

  const isSessionCreator = React.useMemo(
    () => user?.username === sessionInfo?.dj.username,
    [user?.username, sessionInfo?.dj.username]
  );

  const hasShoutout = (shoutOuts && shoutOuts.length > 0) || !!shoutOutTo;

  const markAsPlaying = async () => {
    try {
      setIsPlaying(true);
      if (sessionInfo?.enableVideo) {
        const db = getFirestore();
        const liveChatRef = collection(db, 'liveChats');
        if (requestedBy) {
          await addDoc(
            liveChatRef,
            removeUndefined({
              text: '',
              createdAt: serverTimestamp(),
              userId: requestedBy[0]?.id,
              username: requestedBy[0]?.username,
              image: requestedBy[0]?.profileImageUrl || '',
              sessionId,
              isCreator: false,
              songRequestInfo: {
                title: song.title,
                artist: song.artist,
                status: 'PLAYING',
                isShoutout: hasShoutout,
              },
            })
          );
        }
        if (song.title !== '')
          await addDoc(
            liveChatRef,
            removeUndefined({
              text: '',
              createdAt: serverTimestamp(),
              userId: user?.id,
              username: user?.username,
              image: user?.profileImageUrl || '',
              sessionId,
              isCreator: user?.username === sessionInfo?.dj.username,
              songRequestInfo: {
                title: song.title,
                artist: song.artist,
                status: 'PLAYING',
                isShoutout: hasShoutout,
              },
            })
          );
      }
      setIsPlaying(false);
      // router.replace({
      //   pathname: '/session/recording',
      //   params: {
      //     songId: songId || id,
      //     sessionId,
      //   },
      // });
      onOpen();
      modal.dismiss();
    } catch (error) {
      setIsPlaying(false);
      console.error('Error marking song as playing:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  return (
    <View
      className={cn(
        'gap-4 rounded-md bg-grey-10 px-4 py-3 dark:bg-grey-90',
        isModal && 'bg-transparent dark:bg-transparent'
      )}
    >
      <View className="flex-row items-center justify-between gap-3">
        <View className="flex-1 flex-row gap-2">
          <RequestIcon isShoutout={!!hasShoutout} iconSource={song.coverUrl} />
          <RequestDetails
            title={hasShoutout ? 'Shout out' : song.title}
            subtitle={
              hasShoutout ? shoutOuts?.[0] || shoutOutTo || '' : song.artist
            }
          />
        </View>

        <View
          className={cn(
            'flex-row items-center gap-2',
            isSessionCreator && 'gap-1'
          )}
        >
          <ActionButtons
            id={songId || id || ''}
            status={status}
            isShoutout={!!hasShoutout}
            isBoosted={isBoosted}
            onAccept={onAccept}
            onDecline={onDecline}
            onView={viewShoutoutModal.present}
            onCompleteView={modal.present}
            onBoostRequest={() => {
              if (
                !sessionInfo?.isFree &&
                user?.walletBalance &&
                user?.walletBalance < (sessionInfo?.cost || 0)
              ) {
                toast.info(
                  'Insufficient wallet balance to make this request, please top up',
                  {
                    action: (
                      <Button
                        label="Top up"
                        onPress={() => {
                          dismissAllModals();
                          push({ pathname: '/profile/top-up' });
                          toast.dismiss();
                        }}
                        size="sm"
                        className="w-40"
                      />
                    ),
                  }
                );
              } else {
                boostConfirmationModal.present();
              }
            }}
            isSessionCreator={isSessionCreator}
          />
        </View>
      </View>

      <RequestFooter
        timestamp={dayjs(timestamp).format('h:mm A')}
        username={requestedBy?.map(({ username }) => username).join(', ') || ''}
        isSessionCreator={isSessionCreator}
      />

      <PersistentModal
        ref={modal.ref}
        backgroundColor={isDark ? colors.grey[80] : colors.white}
        handleClassName="bg-bg-interactive-secondary-light dark:bg-bg-interactive-secondary-dark"
      >
        <CompletedRequestModal
          isPlaying={isPlaying}
          onDismiss={modal.dismiss}
          onProceed={markAsPlaying}
          shoutoutMessage={shoutOuts?.[0] || shoutOutTo || ''}
          title={song.title}
          iconSource={song.coverUrl}
          subtitle={song.artist}
          warningMessage="Avoid account suspension, ensure the request is currently being played or performed before you hit the proceed button"
        />
      </PersistentModal>

      <Modal
        ref={viewShoutoutModal.ref}
        index={1}
        enableDynamicSizing
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
        handleClassName="bg-bg-interactive-secondary-light dark:bg-bg-interactive-secondary-dark"
      >
        <ShoutoutRequestModal
          id={songId || id || ''}
          message={shoutOuts || []}
          onAccept={onAccept}
          onDecline={onDecline}
          onDismissShoutoutModal={viewShoutoutModal.dismiss}
          title={song.title}
          username={
            requestedBy?.map(({ username }) => username).join(',') || ''
          }
          iconSource={song.coverUrl}
          imageSource={
            requestedBy?.[0].profileImageUrl ||
            require('~/assets/images/avatar.png')
          }
          subtitle={song.artist}
        />
      </Modal>
      <Modal
        ref={boostConfirmationModal.ref}
        index={2}
        enableDynamicSizing
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
      >
        <BottomSheetView
          className="min-h-[250px] gap-6 px-4 py-6"
          style={{
            paddingBottom: insets.bottom,
          }}
        >
          <LgBoldLabel>Boost request</LgBoldLabel>
          <View className="gap-4">
            <View className="flex-row items-center gap-6 py-4">
              <View className="h-9 flex-row items-center justify-center rounded-full border border-border-subtle-light bg-bg-canvas-light px-4 dark:border-border-subtle-dark dark:bg-bg-canvas-dark">
                <View className="w-7 items-center justify-center">
                  <Feather name="x" size={20} color={colors.brand[60]} />
                </View>
                <MdRegularLabel>2</MdRegularLabel>
              </View>

              <TouchableOpacity
                className="flex-row items-center justify-center gap-1 rounded-full bg-accent-moderate py-2 pl-3 pr-2"
                onPress={() => {
                  boostSong(
                    {
                      sessionId,
                      payload: {
                        songId: songId || '',
                        userCurrency: 'NGN',
                      },
                    },
                    { onSuccess: boostConfirmationModal.dismiss }
                  );
                }}
              >
                <MdBoldLabel>
                  Boost (
                  {formatAmount(toAmountInMajor((sessionInfo?.cost || 0) * 2))})
                </MdBoldLabel>
                <Image
                  source={require('~/assets/images/session/lightening.png')}
                  className="size-4"
                />
              </TouchableOpacity>
            </View>
            <View className="mt-2 h-12 justify-center rounded-md bg-bg-info-contrast px-4">
              <P>
                Wallet balance:{' '}
                {formatAmount(toAmountInMajor(user?.walletBalance || 0))}
              </P>
            </View>
          </View>
        </BottomSheetView>
      </Modal>

      <RNModal visible={isOpen} transparent={true} animationType="slide">
        {/* <View className="inset-0 absolute bg-black/50" /> */}
        <View className="relative flex-1 bg-bg-overlay-light dark:bg-bg-overlay-dark">
          <BlurredBackground
            isDark={isDark}
            blurStyle={styles.blurView}
            overlayStyle={styles.overlay}
          />
          <SongRecordingModal
            visible={isOpen}
            onClose={onClose}
            songId={songId || id || ''}
            sessionId={sessionId}
          />
        </View>
      </RNModal>
    </View>
  );
};

export const styling = (isDark: boolean) =>
  StyleSheet.create({
    overlay: {
      backgroundColor: colors.black,
      opacity: isDark ? 0.45 : 0.7,
      ...StyleSheet.absoluteFillObject,
    },
    blurView: {
      overflow: 'hidden',
      backgroundColor: 'transparent',
      ...StyleSheet.absoluteFillObject,
    },
  });
