import React from 'react';

import { Button, View } from '@/components/ui';
import { cn } from '@/lib';

interface ModalActionsProps {
  primaryLabel: string;
  secondaryLabel?: string;
  onPrimaryAction: () => void;
  onSecondaryAction?: () => void;
  primaryVariant?: 'default' | 'destructive' | 'ghost';
  secondaryVariant?: 'default' | 'destructive' | 'ghost';
  isLoading?: boolean;
  className?: string;
  isBtnGroupRow?: boolean;
}

export const ModalActions: React.FC<ModalActionsProps> = ({
  primaryLabel,
  secondaryLabel,
  onPrimaryAction,
  onSecondaryAction,
  primaryVariant = 'default',
  secondaryVariant = 'ghost',
  isLoading,
  className,
  isBtnGroupRow,
}) => {
  return (
    <View className={cn('gap-2', className)}>
      <Button
        label={primaryLabel}
        variant={primaryVariant}
        onPress={onPrimaryAction}
        loading={isLoading}
        disabled={isLoading}
        className={cn(isBtnGroupRow && 'flex-1')}
      />
      {secondaryLabel && (
        <Button
          className={cn(isBtnGroupRow && 'flex-1')}
          label={secondaryLabel}
          variant={secondaryVariant}
          onPress={onSecondaryAction}
          disabled={isLoading}
        />
      )}
    </View>
  );
};
