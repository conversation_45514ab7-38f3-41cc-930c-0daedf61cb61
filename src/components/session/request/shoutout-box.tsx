import React from 'react';

import { MdRegularLabel, View } from '@/components/ui';

interface ShoutoutMessageBoxProps {
  message: string;
}

export const ShoutoutMessageBox: React.FC<ShoutoutMessageBoxProps> = ({
  message,
}) => {
  return (
    <View className="w-full items-center rounded-md bg-bg-canvas-light px-2 py-4 dark:bg-bg-canvas-dark">
      <MdRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
        {message}
      </MdRegularLabel>
    </View>
  );
};
