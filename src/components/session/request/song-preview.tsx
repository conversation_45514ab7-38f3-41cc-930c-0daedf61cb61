import { useColorScheme } from 'nativewind';
import React from 'react';

import { Image, MdRegularLabel, SmRegularLabel, View } from '@/components/ui';
import { colors, Music } from '@/components/ui';

interface SongPreviewProps {
  title: string;
  subtitle?: string;
  iconSource?: string;
}

export const SongPreview: React.FC<SongPreviewProps> = ({
  title,
  subtitle,
  iconSource,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="flex-row items-center gap-2 px-4">
      <View className="flex-1 flex-row items-center gap-4 py-3">
        <Image
          source={
            iconSource || require('~/assets/gradient-bg/event-gradient.png')
          }
          className="size-10 rounded-md"
        />
        <View className="flex-1 gap-2">
          <MdRegularLabel numberOfLines={1}>{title}</MdRegularLabel>
          <SmRegularLabel
            numberOfLines={1}
            className="text-fg-muted-light dark:text-fg-muted-dark"
          >
            {subtitle}
          </SmRegularLabel>
        </View>
      </View>
      <View className="items-center justify-center pl-2">
        <Music color={isDark ? colors.white : colors.grey[100]} />
      </View>
    </View>
  );
};
