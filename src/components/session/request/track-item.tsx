import { BottomSheetView, useBottomSheetModal } from '@gorhom/bottom-sheet';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { toast } from 'sonner-native';

import {
  type SongRequestPayload,
  type TrackItems,
  useSendSongRequest,
} from '@/api/session';
import {
  Button,
  colors,
  Image,
  MdRegularLabel,
  Modal,
  Pressable,
  SmRegularLabel,
  useModal,
  View,
  XsBoldLabel,
} from '@/components/ui';
import {
  formatAmount,
  toAmountInMajor,
  useGetLiveSessionQuery,
  useLoggedInUser,
} from '@/lib';

import { RequestDetails } from './details';
import { RequestIcon } from './icon';
import { useRouter } from 'expo-router';

export const TrackItem: React.FC<
  TrackItems & {
    closeSongRequestModal: () => void;
    onSelectSong?: (song: SongRequestPayload) => void;
    sessionId: string;
  }
> = ({
  album,
  name,
  artists,
  id,
  closeSongRequestModal,
  onSelectSong,
  sessionId,
}) => {
  const { push } = useRouter();
  const { data: user } = useLoggedInUser();
  const { data: sessionData } = useGetLiveSessionQuery(sessionId);
  const sessionInfo = sessionData?.djSession;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  const modal = useModal();
  const { dismissAll: dismissAllModals } = useBottomSheetModal();

  const { mutate, isPending } = useSendSongRequest();

  const onSendSongRequest = () => {
    mutate(
      {
        sessionId,
        payload: {
          artist: artists[0]?.name,
          coverUrl: album?.images[2]?.url,
          isBoosted: false,
          songId: id,
          title: name,
          userCurrency: 'NGN',
        },
      },
      {
        onSuccess: () => {
          closeSongRequestModal();
          modal.dismiss();
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return (
    <>
      <Pressable
        className="h-16 flex-row items-center"
        onPress={() => {
          if (
            !sessionInfo?.isFree &&
            user?.walletBalance &&
            user?.walletBalance < (sessionInfo?.cost || 0)
          ) {
            toast.info(
              'Insufficient wallet balance to make this request, please top up',
              {
                action: (
                  <Button
                    label="Top up"
                    onPress={() => {
                      dismissAllModals();
                      push({ pathname: '/profile/top-up' });
                      toast.dismiss();
                    }}
                    size="sm"
                    className="w-40"
                  />
                ),
              }
            );
          } else {
            if (onSelectSong) {
              onSelectSong({
                artist: artists[0]?.name,
                coverUrl: album?.images[2]?.url,
                isBoosted: false,
                songId: id,
                title: name,
                userCurrency: 'NGN',
              });
              closeSongRequestModal();
            } else {
              modal.present();
            }
          }
        }}
      >
        <View className="flex-1 flex-row items-center gap-4">
          <RequestIcon iconSource={album?.images[2]?.url} />
          <RequestDetails title={name} subtitle={artists[0]?.name} />
        </View>
        <View className="rounded-full bg-accent-subtle-light px-4 py-2 dark:bg-accent-subtle-dark">
          <XsBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
            Request {formatAmount(toAmountInMajor(sessionInfo?.cost || 0))}
          </XsBoldLabel>
        </View>
      </Pressable>
      <Modal
        ref={modal.ref}
        enableDynamicSizing
        index={1}
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
      >
        <BottomSheetView
          className="min-h-[300px] items-center gap-4 px-4 pt-6"
          style={{
            paddingBottom: insets.bottom,
          }}
        >
          <Image
            source={album?.images[2]?.url}
            className="size-20 rounded-full"
          />
          <View className="items-center gap-2.5">
            <MdRegularLabel>{name}</MdRegularLabel>
            <SmRegularLabel className="text-grey-60 dark:text-grey-50">
              {artists[0]?.name}
            </SmRegularLabel>
          </View>
          <View className="w-full gap-2">
            <Button
              label="Send request"
              onPress={onSendSongRequest}
              loading={isPending}
              disabled={isPending}
            />
            <Button
              label="Cancel"
              onPress={modal.dismiss}
              variant="ghost"
              disabled={isPending}
            />
          </View>
        </BottomSheetView>
      </Modal>
    </>
  );
};
