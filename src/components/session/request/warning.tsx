import { Feather } from '@expo/vector-icons';
import React from 'react';

import { colors, P, View } from '@/components/ui';

interface WarningMessageProps {
  message: string;
  iconColor?: string;
}

export const WarningMessage: React.FC<WarningMessageProps> = ({
  message,
  iconColor = colors.brand[60],
}) => {
  return (
    <View className="flex-row items-center gap-4">
      <Feather name="alert-triangle" size={24} color={iconColor} />
      <P className="flex-1">{message}</P>
    </View>
  );
};
