import React from 'react';
import { ActivityIndicator, View } from 'react-native';
import { RtcSurfaceView, VideoSourceType } from 'react-native-agora';

import { colors, Image, Large, TouchableOpacity } from '@/components/ui';
import { type JoinState } from '@/lib';

interface VideoStreamViewProps {
  joinState: JoinState;
  isOwner: boolean;
  enableVideo: boolean;
  onRetry: () => void;
  isCameraHidden: boolean;
}

export const VideoStreamView: React.FC<VideoStreamViewProps> = ({
  joinState,
  isOwner,
  enableVideo,
  onRetry,
  isCameraHidden,
}) => {
  if (!enableVideo) {
    return (
      <Image
        source={require('~/assets/icon.png')}
        className="size-[127px] rounded-full"
      />
    );
  }

  const renderState = () => {
    switch (joinState) {
      case 'initial':
        return (
          <View className="flex-1 items-center justify-center bg-bg-canvas-light dark:bg-bg-canvas-dark">
            <Large>Preparing to join session...</Large>
          </View>
        );

      case 'connecting':
        return (
          <View className="flex-1 items-center justify-center bg-bg-canvas-light dark:bg-bg-canvas-dark">
            <ActivityIndicator size="large" color={colors.brand[60]} />
            <Large className="mt-4">Connecting to session...</Large>
          </View>
        );

      case 'connected':
        if (isCameraHidden) {
          return <View className="size-full flex-1 bg-black" />;
        }
        return (
          <RtcSurfaceView
            canvas={{
              uid: 1234,
              sourceType: isOwner
                ? VideoSourceType.VideoSourceCamera
                : VideoSourceType.VideoSourceRemote,
            }}
            style={{
              width: '100%',
              height: '100%',
              position: 'relative',
              bottom: 0,
              right: 0,
            }}
          />
        );

      case 'failed':
        return (
          <View className="flex-1 items-center justify-center bg-bg-canvas-light dark:bg-bg-canvas-dark">
            <Large>Failed to join session</Large>
            <TouchableOpacity
              className="mt-4 rounded-lg bg-brand-60 px-4 py-2"
              onPress={onRetry}
            >
              <Large>Retry</Large>
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  return <View className="size-full">{renderState()}</View>;
};
