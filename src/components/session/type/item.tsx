import React, { type ReactNode } from 'react';

import { MdRegularLabel, TouchableOpacity, View } from '@/components/ui';

interface LiveTypeOptionProps {
  icon: ReactNode;
  label: string;
  onPress: () => void;
}

export const LiveTypeOption: React.FC<LiveTypeOptionProps> = ({
  icon,
  label,
  onPress,
}) => {
  return (
    <TouchableOpacity
      className="flex-row items-center gap-4 py-3"
      onPress={onPress}
    >
      <View className="size-10 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark">
        {icon}
      </View>
      <MdRegularLabel>{label}</MdRegularLabel>
    </TouchableOpacity>
  );
};
