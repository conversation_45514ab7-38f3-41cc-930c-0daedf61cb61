import { useQueryClient } from '@tanstack/react-query';
import * as React from 'react';
import { RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { type UserObjectData } from '@/api/auth';
import { type IEvent } from '@/api/events';
import { TAB_HEIGHT } from '@/app/(app)/_layout';
import { List, ScrollView, View } from '@/components/ui';

import { FavouriteAccountCard } from '../cards/favourite-account';
import { EmptyState } from '../ui/empty';

type Props = {
  accounts?: IEvent[] | UserObjectData[];
  index: number;
};
export const FavouriteCreatorsTab = ({ accounts, index }: Props) => {
  const [refreshing, setRefreshing] = React.useState(false);
  const queryClient = useQueryClient();

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);

    Promise.all([
      queryClient.invalidateQueries({
        queryKey: ['getUserFavourites'],
        exact: true,
      }),
    ])
      .then(() => new Promise((resolve) => setTimeout(resolve, 1000)))
      .finally(() => {
        setRefreshing(false);
      });
  }, [queryClient]);
  const insets = useSafeAreaInsets();

  return (
    <ScrollView
      className="flex-1 bg-white dark:bg-grey-100"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View
        className="flex-1 p-4"
        style={{
          marginBottom: TAB_HEIGHT + insets.bottom,
        }}
      >
        <List
          key={index}
          data={(Array.isArray(accounts) ? accounts : []) as UserObjectData[]}
          estimatedItemSize={64}
          ListEmptyComponent={<EmptyState />}
          renderItem={({ item }) => (
            <FavouriteAccountCard
              id={item.id}
              image={item.profileImageUrl}
              fullname={item.fullName}
              username={item.username}
            />
          )}
          keyExtractor={(item) =>
            item?.id?.toString?.() ?? Math.random().toString()
          }
          ItemSeparatorComponent={() => <View className="size-2 " />}
        />
      </View>
    </ScrollView>
  );
};
