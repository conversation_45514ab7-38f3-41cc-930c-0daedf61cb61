import * as React from 'react';
import { RefreshControl } from 'react-native';

import { type IEvent } from '@/api/events';
import { List, ScrollView, View } from '@/components/ui';

import { EventFavoriteCard } from '../cards/event-favorite';
import { MyEventsLoading } from '../loading/my-events';
import { EmptyState } from '../ui/empty';

type Props = {
  events?: IEvent[];
  index: number;
  isPending: boolean;
  refreshing: boolean;
  onRefresh: () => void;
};
export const CreatorEventsTab = ({
  events,
  index,
  isPending,
  refreshing,
  onRefresh,
}: Props) => (
  <ScrollView
    className="flex-1 bg-white dark:bg-grey-100"
    showsVerticalScrollIndicator={false}
    refreshControl={
      <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
    }
  >
    <View className="flex-1 p-4">
      {isPending ? (
        <MyEventsLoading />
      ) : (
        <List
          key={index}
          data={events}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={152}
          ListEmptyComponent={<EmptyState />}
          renderItem={({ item }) => (
            <EventFavoriteCard {...item} attendees={item.ticketsSold} />
          )}
          keyExtractor={(item) => String(item.id)}
          ItemSeparatorComponent={() => <View className="h-4" />}
        />
      )}
    </View>
  </ScrollView>
);
