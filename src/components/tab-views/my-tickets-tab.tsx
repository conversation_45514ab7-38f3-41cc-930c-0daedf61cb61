import * as React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { type ITicketExtension } from '@/api/events';
import { TAB_HEIGHT } from '@/app/(app)/_layout';
import { List, ScrollView, View } from '@/components/ui';

import { TicketItem } from '../tickets/item';
import { EmptyState } from '../ui/empty';

type Props = {
  tickets?: ITicketExtension[];
  index: number;
};
export const UserTicketsTab = ({ tickets, index }: Props) => {
  const insets = useSafeAreaInsets();

  return (
    <ScrollView
      className="flex-1 bg-white dark:bg-grey-100"
      showsVerticalScrollIndicator={false}
    >
      <View
        className="flex-1 p-4"
        style={{
          marginBottom: TAB_HEIGHT + insets.bottom,
        }}
      >
        <List
          key={index}
          data={tickets}
          estimatedItemSize={103}
          ListEmptyComponent={<EmptyState />}
          renderItem={({ item }) => (
            <TicketItem
              {...item}
              title={`${item.title} - ${Object.keys(item.meta.breakdown || {})[0]}`}
              id={item.id}
              hasPresale={false}
              isPast={index === 1}
            />
          )}
          keyExtractor={(item) => String(item.id)}
          ItemSeparatorComponent={() => <View className="size-4" />}
        />
      </View>
    </ScrollView>
  );
};
