import * as React from 'react';
import { RefreshControl } from 'react-native';

import { Image, List, MdRegularLabel, View } from '@/components/ui';

import { Queue } from '@/api/session';
import { SongsLoading } from '../loading/songs';

type Props = {
  queue: Queue[];
  renderItem: ({ item }: { item: Queue }) => React.JSX.Element;
  index: number;
  isPending?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
};
export const SessionRequestTabView = ({
  queue,
  renderItem,
  index,
  isPending,
  refreshing = false,
  onRefresh,
}: Props) => (
  <View className="flex-1 gap-2 p-4">
    {isPending ? (
      <SongsLoading />
    ) : (
      <List
        key={index}
        data={queue}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={74}
        ListEmptyComponent={
          <View className="mt-16 flex-1 items-center gap-4">
            <Image
              className="size-[186px]"
              source={require('~/assets/images/session/empty.png')}
            />
            <MdRegularLabel>It's empty here</MdRegularLabel>
          </View>
        }
        renderItem={renderItem}
        keyExtractor={(item) => String(item.songId || item.id)}
        ItemSeparatorComponent={() => <View className="size-2" />}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    )}
  </View>
);
