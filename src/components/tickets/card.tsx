import { LinearGradient } from 'expo-linear-gradient';
import moment from 'moment';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { StyleSheet } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import ViewShot from 'react-native-view-shot';

import { EventFormat, type ITicketExtension } from '@/api/events';
import {
  colors,
  H2,
  Image,
  MdBoldLabel,
  P,
  View,
  WIDTH,
} from '@/components/ui';

interface TicketCardProps {
  ticket: ITicketExtension;
}

export const TicketCard = React.forwardRef<ViewShot, TicketCardProps>(
  ({ ticket }, ref) => {
    const { colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';

    const isPhysicalLocation = ticket.event.eventFormat !== EventFormat.ONLINE;
    const locationText =
      isPhysicalLocation && ticket?.location
        ? ticket.location.landmark ||
          ticket.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
        : ticket?.event.onlineEventUrl || 'Online';

    return (
      <ViewShot
        ref={ref}
        style={{
          backgroundColor: isDark ? colors.grey[100] : colors.white,
          width: WIDTH - 64,
        }}
      >
        <LinearGradient
          colors={['#131214', '#53575A']}
          locations={[0.82, 1]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={{ borderRadius: 16 }}
        >
          <View className="relative h-[270px]">
            <Image
              className="size-full rounded-t-lg"
              source={ticket.bannerUrl}
              priority="high"
            />
            <LinearGradient
              colors={['rgba(0,0,0,0)', 'rgba(0,0,0,1)']}
              locations={[0, 1]}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              style={{ ...StyleSheet.absoluteFillObject }}
            />
            <View className="absolute bottom-3 mx-3 gap-3">
              <H2>{ticket.title}</H2>
              <View className="w-full flex-row justify-between">
                <View className="flex-1 gap-2">
                  <P className="text-grey-60 dark:text-grey-50">DATE</P>
                  <MdBoldLabel>
                    {moment.utc(ticket.startTime).format('MMM D · LT')}
                  </MdBoldLabel>
                </View>
                <View className="flex-1 gap-2">
                  <P className="text-grey-60 dark:text-grey-50">LOCATION</P>
                  <MdBoldLabel>{locationText}</MdBoldLabel>
                  {ticket.event.eventFormat === EventFormat.HYBRID &&
                    ticket?.event.onlineEventUrl && (
                      <MdBoldLabel numberOfLines={1}>
                        {ticket?.event.onlineEventUrl}
                      </MdBoldLabel>
                    )}
                </View>
              </View>
            </View>
          </View>

          <View className="gap-3 p-3 pb-6">
            {ticket.isUsed &&
              moment().isAfter(moment.utc(ticket.event.endTime)) && (
                <View className="h-8 w-[102px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                  <MdBoldLabel className="text-green-60 dark:text-green-40">
                    Attended
                  </MdBoldLabel>
                </View>
              )}

            <View className="gap-5">
              <P className="text-grey-60 dark:text-grey-50">BARCODE</P>
              <View className="mx-auto">
                <QRCode
                  size={215}
                  value={ticket.id}
                  enableLinearGradient
                  backgroundColor="transparent"
                  color={colors.white}
                  linearGradient={[colors.white, colors.white]}
                />
              </View>
            </View>

            <View className="flex-row justify-between">
              <View className="flex-1 gap-1">
                <P className="text-grey-60 dark:text-grey-50">NAME</P>
                <MdBoldLabel>{ticket?.user?.fullName}</MdBoldLabel>
              </View>
              <View className="flex-1 gap-1">
                <P className="text-grey-60 dark:text-grey-50">TICKET GRADE</P>
                <MdBoldLabel>
                  {Object.keys(ticket?.meta.breakdown || {})[0]}
                </MdBoldLabel>
              </View>
            </View>
            <View className="flex-row justify-between">
              <View className="flex-1 gap-1">
                <P className="text-grey-60 dark:text-grey-50">TICKET ID</P>
                <MdBoldLabel>{ticket?.ticketId}</MdBoldLabel>
              </View>
              <View className="flex-1 gap-1">
                <P className="text-grey-60 dark:text-grey-50">QUANTITY</P>
                <MdBoldLabel>
                  {Object.values(ticket?.meta.breakdown || {})[0]}
                </MdBoldLabel>
              </View>
            </View>
          </View>
        </LinearGradient>
      </ViewShot>
    );
  }
);
