import { useRouter } from 'expo-router';
import React from 'react';
import { Alert } from 'react-native';

import { Small, View } from '@/components/ui';
import { useRecordingTimer } from '@/lib/hooks/use-recording-timer';

interface CheckoutTimerProps {
  initialTime?: number; // Time in seconds (default: 10 minutes 59 seconds)
  onTimeExpired?: () => void;
}

export const CheckoutTimer: React.FC<CheckoutTimerProps> = ({
  initialTime = 659, // 10:59 in seconds
  onTimeExpired,
}) => {
  const router = useRouter();

  const handleTimeExpired = () => {
    Alert.alert(
      'Time Expired',
      'Your ticket reservation has expired. Please try again.',
      [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]
    );
  };

  const { formattedTime, resetTimer } = useRecordingTimer(
    true, // Always active when component is mounted
    initialTime,
    () => {
      // Handle timer completion
      if (onTimeExpired) {
        onTimeExpired();
      } else {
        // Default behavior: redirect back to tickets page or show alert
        handleTimeExpired();
      }
      resetTimer();
    }
  );

  return (
    <View className="w-full items-center rounded-md bg-red-10 py-1 dark:bg-red-80">
      <Small className="max-w-[331px] text-red-60 dark:text-red-40">
        We've reserved your ticket. Please complete checkout within{' '}
        <Small className="text-brand-80 dark:text-brand-30">
          {formattedTime}
        </Small>{' '}
        to secure your tickets.
      </Small>
    </View>
  );
};
