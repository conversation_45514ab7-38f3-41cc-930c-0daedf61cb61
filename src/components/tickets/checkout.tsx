import { Feather, Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import { useFormContext } from 'react-hook-form';
import { toast } from 'sonner-native';

import { DiscountType, useUpdateReserveEventTicket } from '@/api/events';
import {
  colors,
  H4,
  H5,
  Image,
  MdRegularLabel,
  P,
  Skeleton,
  Small,
  SmBoldLabel,
  SmRegularLabel,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '@/components/ui';
import { cn, formatAmount, toAmountInMajor } from '@/lib';
import { usePurchaseTicketContext } from '@/lib/contexts/purchase-ticket-context';
import {
  type AdditionalFees,
  type PurchaseTicketFormType,
} from '@/lib/hooks/use-purchase-ticket';

import Counter from '../common/counter';

const CheckoutSummary: React.FC<{
  openAddTicketModal: () => void;
}> = ({ openAddTicketModal }) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { watch } = useFormContext<PurchaseTicketFormType>();
  const code = watch('discountCode');
  const {
    event,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    calculateTotalCost,
    activeDiscount,
    eventId,
  } = usePurchaseTicketContext();
  const hasDiscount = !!activeDiscount;

  const ticketCategories = event?.ticketCategories || {};
  const selectedTickets = getSelectedTicketsArray();

  const additionalFees: AdditionalFees = {
    fee: 0.05,
    ...(activeDiscount && {
      discountType: activeDiscount.discountType,
      discountValue: activeDiscount.discountValue,
    }),
    // Add other fees as needed
  };
  const costBreakdown = calculateTotalCost(additionalFees);

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useUpdateReserveEventTicket();

  return (
    <View className="gap-4 p-4">
      <View className="gap-4">
        <H5>Order summary</H5>
        <View className="gap-3">
          {selectedTickets.map((ticket, index) =>
            reservingTicket ? (
              <Skeleton key={index} className="h-[42px] rounded-md" />
            ) : (
              <View
                key={index}
                className="flex-row items-center justify-between"
              >
                <View className="flex-row items-center gap-2">
                  <Image
                    source={require('~/assets/images/ticket.png')}
                    className="size-[37px]"
                  />
                  <View className="gap-2">
                    <MdRegularLabel>{ticket.category}</MdRegularLabel>
                    <Small className="text-grey-60 dark:text-grey-50">
                      {formatAmount(
                        toAmountInMajor(
                          ticket.cost ||
                            ticketCategories[ticket.category].convertedCost ||
                            ticketCategories[ticket.category].cost
                        )
                      )}
                    </Small>
                  </View>
                </View>
                <View className="flex-row items-center gap-4">
                  {ticket.isPresale && (
                    <View className="h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                      <XsBoldLabel className="text-green-60 dark:text-green-40">
                        Presale
                      </XsBoldLabel>
                    </View>
                  )}
                  <Counter
                    initialValue={ticket.quantity}
                    value={ticket.quantity}
                    minimum={1}
                    maximum={
                      ticketCategories[ticket.category].quantity > 10
                        ? 10
                        : ticketCategories[ticket.category].quantity
                    }
                    onValueChange={(quantity) => {
                      const previousTickets = [...selectedTickets]; // Snapshot previous state

                      const updatedTickets = [...selectedTickets];
                      const index = updatedTickets.findIndex(
                        (item) => item.category === ticket.category
                      );

                      if (index !== -1) {
                        updatedTickets[index].quantity = quantity;
                      } else {
                        updatedTickets.push({
                          category: ticket.category,
                          quantity,
                          cost: ticket.cost,
                        });
                      }
                      handleTicketQuantityChange(ticket.category, quantity);
                      reserveTicket(
                        {
                          id: eventId,
                          reservations: updatedTickets
                            .filter(({ quantity }) => quantity !== 0)
                            .map(({ category, quantity }) => ({
                              category,
                              quantity,
                            })),
                        },
                        {
                          onError: (error) => {
                            toast.error(error.message);
                            previousTickets.forEach((t) =>
                              handleTicketQuantityChange(t.category, t.quantity)
                            );
                          },
                        }
                      );
                    }}
                    className="border border-border-subtle-light dark:border-border-subtle-dark"
                  />
                </View>
              </View>
            )
          )}

          <TouchableOpacity
            className="h-8 w-[107px] flex-row items-center justify-center gap-1 rounded-[48px] bg-brand-20 dark:bg-brand-90"
            onPress={openAddTicketModal}
          >
            <SmBoldLabel className="text-brand-70 dark:text-brand-40">
              Add Ticket
            </SmBoldLabel>
            <Ionicons
              name="add"
              size={16}
              color={isDark ? colors.brand[40] : colors.brand[70]}
            />
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity
        className={cn(
          'flex-row justify-between items-center border-t-4 border-b-4 border-grey-30 dark:border-grey-70 py-4',
          hasDiscount && 'py-2'
        )}
        onPress={() => {
          router.push({
            pathname: '/events/[id]/tickets/discount',
            params: { id: event?.id || '', code },
          });
        }}
      >
        <View className={cn('flex-1', hasDiscount && 'gap-2.5')}>
          {activeDiscount ? (
            <>
              <MdRegularLabel>
                {activeDiscount.name || activeDiscount.code}
              </MdRegularLabel>
              <SmRegularLabel className="text-grey-60 dark:text-grey-50">
                {activeDiscount.discountType === DiscountType.AMOUNT
                  ? formatAmount(toAmountInMajor(activeDiscount.discountValue))
                  : `${activeDiscount.discountValue}%`}{' '}
                discount
              </SmRegularLabel>
            </>
          ) : (
            <MdRegularLabel>Use discount code</MdRegularLabel>
          )}
        </View>
        <View className="items-center justify-end">
          <Feather
            name="chevron-right"
            size={24}
            color={isDark ? colors.white : colors.grey[100]}
          />
        </View>
      </TouchableOpacity>

      <View className="gap-4">
        <H4>Payment Summary</H4>
        <View className="flex-row justify-between">
          <P>Ticket</P>
          <P>{formatAmount(costBreakdown.ticketSubtotal)}</P>
        </View>
        {!!costBreakdown.totalFees && (
          <View className="flex-row justify-between">
            <P>Service fee</P>
            <P>{formatAmount(costBreakdown.totalFees)}</P>
          </View>
        )}

        {!!costBreakdown.fees.discountAmount && (
          <View className="flex-row justify-between">
            <P>Discount</P>
            <P>-{formatAmount(costBreakdown.fees.discountAmount)}</P>
          </View>
        )}
        <View className="flex-row justify-between">
          <H4>Total</H4>
          <H4>{formatAmount(costBreakdown.total)}</H4>
        </View>
      </View>
    </View>
  );
};

export default CheckoutSummary;
