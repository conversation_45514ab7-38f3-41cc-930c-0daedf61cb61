import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React from 'react';
import QRCode from 'react-native-qrcode-svg';

import { type ITicketExtension } from '@/api/events';

import {
  colors,
  H5,
  Image,
  TicketQrBg,
  TouchableOpacity,
  View,
  XsBoldLabel,
} from '../ui';

interface TicketItemProps {
  isPast?: boolean;
  hasPresale?: boolean;
}

export const TicketItem: React.FC<ITicketExtension & TicketItemProps> = ({
  id,
  title,
  hasPresale,
  isPast,
  bannerUrl,
  transactionId,
}) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const qrColor = colors.black;

  return (
    <TouchableOpacity
      className="h-[103px] flex-1 flex-row rounded-lg"
      onPress={() =>
        router.push({
          pathname: '/events/tickets/[id]',
          params: { id, transactionId },
        })
      }
    >
      <View className="relative flex-1">
        <Image
          className="size-full rounded-l-lg"
          source={
            isDark
              ? require('~/assets/ticket/ticket-bg-subtle-dark.png')
              : require('~/assets/ticket/ticket-bg-subtle-light.png')
          }
        />
        <View className="absolute left-3.5 top-2.5 flex-1 flex-row items-center justify-center gap-2">
          <Image
            className="size-[68px] rounded-md"
            source={bannerUrl}
            priority="high"
          />

          <View className="gap-2">
            <H5>{title}</H5>
            {hasPresale && (
              <View className="h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                <XsBoldLabel className="text-green-60 dark:text-green-40">
                  Presale
                </XsBoldLabel>
              </View>
            )}

            <View className="h-7 w-[94px] items-center justify-center rounded-full border border-brand-70 dark:border-brand-40">
              <XsBoldLabel className="text-brand-70 dark:text-brand-40">
                See details
              </XsBoldLabel>
            </View>
          </View>
        </View>
      </View>
      <View className="relative w-[74px] justify-end">
        <TicketQrBg
          color={
            !isPast
              ? colors.brand[50]
              : isDark
                ? colors.grey[70]
                : colors.grey[30]
          }
        />
        <View className="absolute inset-0 items-center justify-center">
          <QRCode
            size={58}
            value={id}
            enableLinearGradient
            backgroundColor="transparent"
            color={qrColor}
            linearGradient={[qrColor, qrColor]}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};
