import { Fontisto, Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import LoadingScreen from '@/components/loading';
import {
  colors,
  H5,
  MdRegularLabel,
  SmRegularLabel,
  TouchableOpacity,
  View,
} from '@/components/ui';
import { RadioGroup, RadioGroupItem } from '@/components/ui';
import { formatAmount, toAmountInMajor, useLoggedInUser } from '@/lib';
import { type PurchaseTicketFormType } from '@/lib/hooks/use-purchase-ticket';

export default function PaymentMethod() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { data: user, isLoading: isUserFetching } = useLoggedInUser();

  const { watch, setValue } = useFormContext<PurchaseTicketFormType>();
  const paymentMethod = watch('paymentMethod');

  const onValueChange = (value: string) =>
    setValue('paymentMethod', value as PurchaseTicketFormType['paymentMethod']);

  if (isUserFetching) return <LoadingScreen />;

  return (
    <View className="gap-4 p-4">
      <RadioGroup
        value={paymentMethod}
        onValueChange={onValueChange}
        className="gap-2"
      >
        <H5>Payment method</H5>

        <TouchableOpacity
          className="flex-row items-center justify-between"
          onPress={() => {
            setValue('paymentMethod', 'wallet');
          }}
        >
          <View className="flex-1 flex-row items-center gap-4 py-3">
            <View className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
              <Ionicons
                name="wallet-outline"
                size={16}
                color={isDark ? colors.brand[40] : colors.brand[70]}
              />
            </View>

            <View className="flex-1 gap-2.5">
              <MdRegularLabel>Wallet</MdRegularLabel>
              <SmRegularLabel className="text-grey-60 dark:text-grey-50">
                Available balance -{' '}
                {formatAmount(toAmountInMajor(user?.walletBalance || 0))}
              </SmRegularLabel>
            </View>
          </View>
          <View className="items-center justify-end">
            <RadioGroupItem
              value="wallet"
              aria-selected={paymentMethod === 'wallet'}
              aria-labelledby={`label-for-wallet`}
            />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          className="flex-row items-center justify-between"
          onPress={() => {
            setValue('paymentMethod', 'online');
          }}
        >
          <View className="flex-1 flex-row items-center gap-4 py-3">
            <View className="size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
              <Fontisto
                name="world-o"
                size={16}
                color={isDark ? colors.brand[40] : colors.brand[70]}
              />
            </View>

            <View className="flex-1">
              <MdRegularLabel>Pay Online</MdRegularLabel>
            </View>
          </View>
          <View className="items-center justify-end">
            <RadioGroupItem
              value="online"
              aria-selected={paymentMethod === 'online'}
              aria-labelledby={`label-for-online`}
            />
          </View>
        </TouchableOpacity>
      </RadioGroup>
    </View>
  );
}
