import {
  Blur,
  Canvas,
  Group,
  RadialGradient,
  RoundedRect,
  vec,
} from '@shopify/react-native-skia';
import { useColorScheme } from 'nativewind';
import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import {
  cancelAnimation,
  Easing,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

import { colors as themeColors } from '@/components/ui';

interface AnimatedBorderProps {
  width: number;
  height: number;
  isSelected: boolean;
  borderRadius?: number;
  borderWidth?: number;
  blurRadius?: number;
}

export const AnimatedBorder: React.FC<AnimatedBorderProps> = ({
  width,
  height,
  isSelected,
  borderRadius = 8,
  borderWidth = 2,
  blurRadius = 8,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const rotation = useSharedValue(0);

  const centerX = width / 2;
  const centerY = height / 2;
  const centerVec = vec(centerX, centerY);

  // Calculate radius for the radial gradient - should extend beyond the card
  const radius = Math.sqrt(width * width + height * height) / 2;

  useEffect(() => {
    if (isSelected) {
      rotation.value = withRepeat(
        withTiming(2, {
          duration: 3000,
          easing: Easing.ease,
        }),
        -1, // Infinite repeat
        true
      );
    } else {
      rotation.value = withTiming(0, {
        duration: 300,
        easing: Easing.out(Easing.cubic),
      });
    }

    // Cleanup animation on unmount
    return () => cancelAnimation(rotation);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSelected]);

  const animatedRotation = useDerivedValue(() => {
    return [{ rotate: Math.PI * rotation.value }];
  }, [rotation]);

  if (!isSelected) {
    return null;
  }

  // Purple gradient layer (#5336E2)
  const PurpleGradient = () => (
    <RoundedRect r={borderRadius} x={0} y={0} width={width} height={height}>
      <RadialGradient
        c={centerVec}
        r={radius}
        colors={['#5336E2FF', '#5336E200']} // 100% to 0% opacity
        positions={[0, 1]} // 0% to 100% stops
        transform={animatedRotation}
      />
    </RoundedRect>
  );

  // Magenta gradient layer (#E733E0)
  const MagentaGradient = () => (
    <RoundedRect r={borderRadius} x={0} y={0} width={width} height={height}>
      <RadialGradient
        c={centerVec}
        r={radius}
        colors={['#E733E0FF', '#E733E000']} // 100% to 0% opacity
        positions={[0, 1]} // 0% to 100% stops
        transform={animatedRotation}
      />
    </RoundedRect>
  );

  // White gradient layer (#FFFFFF)
  const WhiteGradient = () => (
    <RoundedRect r={borderRadius} x={0} y={0} width={width} height={height}>
      <RadialGradient
        c={centerVec}
        r={radius}
        colors={['#FFFFFFFF', '#FFFFFF00']} // 100% to 0% opacity
        positions={[0, 1]} // 0% to 100% stops
        transform={animatedRotation}
      />
    </RoundedRect>
  );

  return (
    <View style={StyleSheet.absoluteFill}>
      <Canvas style={{ width, height }}>
        <Group>
          {/* Blurred background glow */}
          <Group>
            <PurpleGradient />
            <MagentaGradient />
            <WhiteGradient />
            <Blur blur={blurRadius} />
          </Group>

          {/* Sharp outline layers */}
          <PurpleGradient />
          <MagentaGradient />
          <WhiteGradient />

          {/* Inner content area - creates the border effect with exact 2px border */}
          <RoundedRect
            r={borderRadius}
            x={borderWidth}
            y={borderWidth}
            width={width - borderWidth * 2}
            height={height - borderWidth * 2}
            color={isDark ? themeColors.grey[100] : themeColors.white}
          />
        </Group>
      </Canvas>
    </View>
  );
};
