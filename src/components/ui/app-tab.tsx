import React, { useEffect, useState } from 'react';
import { Keyboard, useWindowDimensions, View } from 'react-native';
import {
  type NavigationState,
  SceneMap,
  type SceneRendererProps,
  type TabDescriptor,
  TabView,
} from 'react-native-tab-view';

import { TouchableOpacity, XsBoldLabel, XsRegularLabel } from '@/components/ui';
import { cn } from '@/lib';
import { type AppTabProps, type TabScreenItem } from '@/types';

export const AppTab = (props: AppTabProps) => {
  const {
    items,
    containerClassName,
    contentContainerClassName,
    countClassName,
    countContainerClassName,
    tabBarClassName,
    tabBarFocusedClassName,
    tabBarLabelClassName,
    tabBarLabelActiveClassName,
    tabBarLabelInactiveClassName,
    tabIndex = 1,
    tabSetIndex,
  } = props;
  const layout = useWindowDimensions();

  const [index, setIndex] = useState(tabIndex);

  const [routes] = useState(
    items.map((item: TabScreenItem) => ({
      key: item.key,
      title: item.title,
      notificationCount: item.notificationCount,
    }))
  );

  const getSceneObjects = () => {
    const scene: Record<string, React.ComponentType> = {};
    items.forEach((item: TabScreenItem) => {
      scene[item.key] = item.component;
    });
    return scene;
  };

  const renderScene = SceneMap(getSceneObjects());

  useEffect(() => {
    if (tabIndex) {
      setIndex(tabIndex);
    }
  }, [tabIndex]);

  function sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async function executeWithDelay(ms: number) {
    await sleep(ms); // Pauses execution for ms milliseconds (ms/1000 seconds)
  }

  const renderTabBar = (
    tabBarProps: SceneRendererProps & {
      navigationState: NavigationState<Omit<TabScreenItem, 'component'>>;
      options:
        | Record<string, TabDescriptor<Omit<TabScreenItem, 'component'>>>
        | undefined;
    }
  ) => {
    return (
      <View className={cn('px-4 py-2', containerClassName)}>
        <View
          className={cn(
            'h-11 w-full flex-row items-center rounded-full bg-grey-10 p-0.5 dark:bg-grey-90',
            contentContainerClassName
          )}
        >
          {tabBarProps.navigationState.routes.map((route, i: number) => {
            const isFocused = tabBarProps.navigationState.index === i;

            return (
              <TouchableOpacity
                key={route.key}
                className={cn(
                  'relative flex-1 items-center justify-center h-full rounded-full',
                  tabBarClassName,
                  isFocused
                    ? cn('bg-white dark:bg-grey-80', tabBarFocusedClassName)
                    : 'bg-transparent'
                )}
                onPress={async (event) => {
                  event.preventDefault();
                  Keyboard.dismiss();
                  await executeWithDelay(100);
                  setIndex(i);
                  tabSetIndex?.(i);
                }}
              >
                <XsBoldLabel
                  className={cn(
                    tabBarLabelClassName,
                    isFocused
                      ? cn(
                          'text-brand-60 dark:text-brand-50',
                          tabBarLabelActiveClassName
                        )
                      : cn(
                          'text-grey-50 dark:text-grey-60',
                          tabBarLabelInactiveClassName
                        )
                  )}
                >
                  {route.title}
                </XsBoldLabel>
                {!!route.notificationCount && (
                  <View
                    className={cn(
                      'absolute -top-[9px] right-[7px] size-5 items-center justify-center rounded-full bg-red-60 dark:bg-red-60',
                      countContainerClassName
                    )}
                  >
                    <XsRegularLabel
                      className={cn('text-white', countClassName)}
                    >
                      {route.notificationCount}
                    </XsRegularLabel>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <TabView
      renderTabBar={renderTabBar}
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={(i) => {
        setIndex(i);
        if (tabSetIndex) {
          tabSetIndex(i);
        }
      }}
      initialLayout={{
        width: layout.width,
      }}
    />
  );
};

export default AppTab;
