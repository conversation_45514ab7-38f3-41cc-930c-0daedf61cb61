import Feather from '@expo/vector-icons/Feather';
import { BottomSheetTextInput as GorhomBottomSheetTextInput } from '@gorhom/bottom-sheet';
import * as React from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import type {
  NativeSyntheticEvent,
  TextInput,
  TextInputFocusEventData,
  TextInputProps,
  ViewProps,
} from 'react-native';
import { I18nManager, StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { tv } from 'tailwind-variants';

import colors from './colors';
import { IconWrapper } from './icon-wrapper';
import { Text } from './text';

// Reuse the same styles as the regular input component
const inputTv = tv({
  slots: {
    container: 'relative mb-2',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark',
    input:
      'h-[52px] rounded-md border border-border-subtle-light bg-transparent p-3 pl-12 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    iconContainer: 'absolute left-3 top-3.5 text-accent-moderate',
  },

  variants: {
    focused: {
      true: {
        input:
          'border-2 border-brand-60 pb-2 pt-5 shadow-sm shadow-accent-moderate',
        iconContainer: '',
      },
    },
    filled: {
      true: {
        input: 'pb-2 pt-5',
        iconContainer: '',
      },
    },
    error: {
      true: {
        input: 'border-red-60 dark:border-red-80',
        label: 'dark:text-danger-80 text-red-80',
        iconContainer: '',
      },
    },
    disabled: {
      true: {
        input: 'bg-neutral-200',
        iconContainer: '',
      },
    },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
  },
});

export interface BottomSheetInputProps extends TextInputProps {
  label?: string;
  disabled?: boolean;
  error?: string;
  isPassword?: boolean;
  icon?: React.DetailedReactHTMLElement<
    {
      color: string;
      style: any;
    },
    HTMLElement
  >;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type BottomSheetInputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  rules?: RuleType<T>;
};

interface ControlledBottomSheetInputProps<T extends FieldValues>
  extends BottomSheetInputProps,
    BottomSheetInputControllerType<T> {}

const ForwardedView = React.forwardRef(
  (props: ViewProps, ref: React.LegacyRef<View>) => (
    <View ref={ref} {...props} />
  )
);
// Create animated components
const AnimatedView = Animated.createAnimatedComponent(ForwardedView);

export const BottomSheetInput = React.forwardRef<
  TextInput,
  BottomSheetInputProps
>((props, ref) => {
  const {
    label,
    error,
    testID,
    isPassword,
    icon,
    iconClassName = '',
    containerClassName = '',
    inputClassName = '',
    value,
    onBlur,
    handleFieldBlur,
    handleFieldUnBlur,
    hideErrorMessage,
    ...inputProps
  } = props;
  const [isFocused, setIsFocused] = React.useState(false);
  const [secureTextEntry, setSecureTextEntry] = React.useState(isPassword);
  const inputRef = React.useRef(null);

  // Animation values
  const labelPosition = useSharedValue(0);
  const labelColor = useSharedValue(0);

  // Update shared values based on component state
  React.useEffect(() => {
    if (value) {
      labelPosition.value = 1;
      labelColor.value = 1;
    } else if (isFocused) {
      labelColor.value = 1;
      labelPosition.value = 1;
    } else {
      labelPosition.value = 0;
      labelColor.value = 0;
    }
  }, [value, isFocused, labelPosition, labelColor]);

  // Handle imperative methods
  React.useImperativeHandle(ref as any, () => ({
    focus: () => {
      (inputRef.current as any)?.focus();
    },
    blur: () => {
      (inputRef.current as any)?.blur();
    },
  }));

  // Event handlers
  const onInputBlur = React.useCallback(
    (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      setIsFocused(false);
      handleFieldBlur?.();
      onBlur?.(e);
    },
    [handleFieldBlur, onBlur]
  );

  const onFocus = React.useCallback(() => {
    setIsFocused(true);
    handleFieldUnBlur?.();
  }, [handleFieldUnBlur]);

  const styles = React.useMemo(
    () =>
      inputTv({
        error: Boolean(error),
        focused: isFocused,
        filled: !!value && !isFocused,
        disabled: Boolean(props.disabled),
      }),
    [error, isFocused, props.disabled, value]
  );

  const togglePassword = () => {
    setSecureTextEntry(!secureTextEntry);
    // Maintain focus after toggle
    setTimeout(() => (inputRef.current as any)?.focus(), 100);
  };

  // Animation styles
  const animatedLabelStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: withTiming(labelPosition.value ? -10 : 0, {
            duration: 200,
          }),
        },
        {
          scale: withTiming(labelPosition.value ? 0.9 : 1, { duration: 200 }),
        },
      ],
      color: interpolateColor(
        labelColor.value,
        [0, 1],
        [colors.red['50'], colors.brand['50']]
      ),
    };
  });

  return (
    <View className={styles.container({ className: containerClassName })}>
      {icon ? (
        <IconWrapper
          className={styles.iconContainer({ className: iconClassName })}
          icon={icon}
          isFocused={isFocused}
          isFilled={!!value}
          hasError={Boolean(error)}
        />
      ) : null}
      {label && (
        <AnimatedView
          style={[
            {
              position: 'absolute',
              left: icon ? 48 : 16,
              top: 14,
            },
            animatedLabelStyle,
          ]}
        >
          <Text
            testID={testID ? `${testID}-label` : undefined}
            className={styles.label()}
          >
            {label}
          </Text>
        </AnimatedView>
      )}
      {/* Use GorhomBottomSheetTextInput instead of NTextInput */}
      <GorhomBottomSheetTextInput
        testID={testID}
        ref={inputRef}
        placeholderTextColor={colors.grey[40]}
        className={styles.input({
          className: !icon ? `pl-4 ${inputClassName}` : inputClassName,
        })}
        onBlur={onInputBlur}
        onFocus={onFocus}
        secureTextEntry={isPassword && secureTextEntry}
        {...inputProps}
        value={value}
        style={StyleSheet.flatten([
          { writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr' },
          { textAlign: I18nManager.isRTL ? 'right' : 'left' },
          inputProps.style,
        ])}
      />
      {isPassword && (
        <TouchableOpacity
          className="absolute right-3 top-3.5"
          onPress={togglePassword}
        >
          <IconWrapper
            // @ts-ignore
            icon={
              <Feather name={secureTextEntry ? 'eye-off' : 'eye'} size={24} />
            }
            isFocused={isFocused}
            isFilled={!!value}
            hasError={Boolean(error)}
          />
        </TouchableOpacity>
      )}
      {error && !hideErrorMessage && (
        <Text
          testID={testID ? `${testID}-error` : undefined}
          className="text-sm text-red-60 dark:text-red-80"
        >
          {error}
        </Text>
      )}
    </View>
  );
});

// only used with react-hook-form
export function ControlledBottomSheetInput<T extends FieldValues>(
  props: ControlledBottomSheetInputProps<T>
) {
  const { name, rules, ...inputProps } = props;
  const { control } = useFormContext();

  const { field, fieldState } = useController({
    control,
    name,
    rules,
  });
  return (
    <BottomSheetInput
      ref={field.ref}
      autoCapitalize="none"
      onChangeText={field.onChange}
      onBlur={field.onBlur}
      value={(field.value as string) || ''}
      {...inputProps}
      error={fieldState.error?.message}
    />
  );
}

export function ControlledBottomSheetInputWithoutContext<T extends FieldValues>(
  props: ControlledBottomSheetInputProps<T>
) {
  const { name, control, rules, ...inputProps } = props;

  const { field, fieldState } = useController({ control, name, rules });
  return (
    <BottomSheetInput
      ref={field.ref}
      autoCapitalize="none"
      onChangeText={field.onChange}
      value={(field.value as string) || ''}
      {...inputProps}
      error={fieldState.error?.message}
    />
  );
}
