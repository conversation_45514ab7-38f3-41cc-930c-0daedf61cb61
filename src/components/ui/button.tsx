import React from 'react';
import type { PressableProps } from 'react-native';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

const button = tv({
  slots: {
    container:
      'relative flex h-12 flex-row items-center justify-center rounded-full px-4',
    label: 'font-aeonik-black text-base/100 font-bold',
    indicator: 'h-6 text-white',
    iconContainer: 'absolute flex items-center justify-center',
  },

  variants: {
    variant: {
      default: {
        container: 'bg-accent-moderate dark:bg-accent-moderate',
        label: 'text-accent-on-accent dark:text-accent-on-accent',
        indicator: 'text-accent-on-accent dark:text-accent-on-accent',
        iconContainer: 'text-accent-on-accent dark:text-accent-on-accent',
      },
      secondary: {
        container: 'bg-accent-subtle-light dark:bg-accent-subtle-dark',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-white dark:text-white',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
      outline: {
        container: 'border border-brand-70 dark:border-brand-40',
        label: 'text-brand-70 dark:text-brand-40',
        indicator: 'text-brand-70 dark:text-brand-40',
        iconContainer: 'text-brand-70 dark:text-brand-40',
      },
      destructive: {
        container: 'bg-red-50 dark:bg-red-50',
        label: 'text-white',
        indicator: 'text-white',
        iconContainer: 'text-white',
      },
      ghost: {
        container: 'bg-transparent',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-accent-bold-light dark:text-accent-bold-dark',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
      link: {
        container: 'bg-transparent',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-accent-bold-light dark:text-accent-bold-dark',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
    },
    size: {
      default: {
        container: 'h-12 px-4',
        label: 'text-base',
        iconContainer: 'size-7',
      },
      lg: {
        container: 'h-14 px-8',
        label: 'text-xl',
        iconContainer: 'size-6',
      },
      sm: {
        container: 'h-10 px-3',
        label: 'text-sm',
        indicator: 'h-2',
        iconContainer: 'size-4',
      },
      xs: {
        container: 'h-7 items-center justify-between px-2.5',
        label: 'text-xs',
        indicator: 'h-2',
        iconContainer: 'size-4',
      },
      modal: {
        container: 'h-8 px-4',
        label: 'text-base',
        indicator: 'h-4',
        iconContainer: 'size-4',
      },
      icon: { container: 'size-9', iconContainer: 'size-6' },
    },
    disabled: {
      true: {
        container: 'bg-bg-disabled-light dark:bg-bg-disabled-dark',
        label: 'text-fg-disabled-light dark:text-fg-disabled-dark',
        indicator: 'text-neutral-400 dark:text-neutral-400',
        iconContainer: 'text-fg-disabled-light dark:text-fg-disabled-light',
      },
    },
    fullWidth: {
      true: {
        container: '',
      },
      false: {
        container: 'self-center',
      },
    },
    iconPosition: {
      left: {
        iconContainer: 'left-4',
      },
      right: {
        iconContainer: 'right-4',
      },
    },
  },
  defaultVariants: {
    variant: 'default',
    disabled: false,
    fullWidth: true,
    size: 'default',
    iconPosition: 'left',
  },
});

type ButtonVariants = VariantProps<typeof button>;
interface Props extends ButtonVariants, Omit<PressableProps, 'disabled'> {
  label?: string;
  loading?: boolean;
  className?: string;
  textClassName?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  iconClassName?: string;
}

export const Button = React.forwardRef<View, Props>(
  (
    {
      label: text,
      loading = false,
      variant = 'default',
      disabled = false,
      size = 'default',
      className = '',
      testID,
      textClassName = '',
      icon,
      iconPosition = 'left',
      iconClassName = '',
      ...props
    },
    ref
  ) => {
    const styles = React.useMemo(
      () => button({ variant, disabled, size, iconPosition }),
      [variant, disabled, size, iconPosition]
    );

    const renderContent = () => {
      if (props.children) {
        return props.children;
      }

      if (loading) {
        return (
          <ActivityIndicator
            size="small"
            className={styles.indicator()}
            testID={testID ? `${testID}-activity-indicator` : undefined}
          />
        );
      }

      const textElement = text ? (
        <Text
          testID={testID ? `${testID}-label` : undefined}
          className={styles.label({ className: textClassName })}
        >
          {text}
        </Text>
      ) : null;

      const iconElement = icon ? (
        <View className={styles.iconContainer({ className: iconClassName })}>
          {icon}
        </View>
      ) : null;

      if (iconPosition === 'left') {
        return (
          <>
            {iconElement}
            {textElement}
          </>
        );
      } else {
        return (
          <>
            {textElement}
            {iconElement}
          </>
        );
      }
    };

    return (
      <Pressable
        disabled={disabled || loading}
        className={styles.container({ className })}
        {...props}
        ref={ref}
        testID={testID}
      >
        {renderContent()}
      </Pressable>
    );
  }
);
