import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Pressable, View } from 'react-native';

interface CloseButtonProps {
  onPress?: () => void;
  className?: string;
}

export const CloseButton: React.FC<CloseButtonProps> = ({
  onPress,
  className = 'absolute -top-1 -right-1 z-10',
}) => {
  return (
    <Pressable onPress={onPress} className={className}>
      <View className="size-6 items-center justify-center rounded-full bg-bg-danger-primary dark:bg-bg-danger-primary">
        <View className="size-4 items-center justify-center rounded-full border-2 border-accent-on-accent dark:border-accent-on-accent">
          <Ionicons name="close" size={12} color="#fff" />
        </View>
      </View>
    </Pressable>
  );
};
