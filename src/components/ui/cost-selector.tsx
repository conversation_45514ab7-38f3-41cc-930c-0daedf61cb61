import React from 'react';
import {
  type Control,
  type FieldValues,
  type UseFormSetValue,
} from 'react-hook-form';

import { ControlledInput, ScrollView, View } from '@/components/ui';
import { CostOption } from '@/components/ui/cost-option';
import {
  type CreateEventFormType,
  formatAmount,
  formatNumberWithCommas,
} from '@/lib';

interface CostSelectorProps<T extends FieldValues> {
  /**
   * The control from react-hook-form
   */
  control: Control<T>;

  /**
   * The setValue function from react-hook-form
   */
  setValue: UseFormSetValue<T>;

  /**
   * The name of the field in the form
   */
  name?: string;

  /**
   * Custom label for the cost input
   */
  label?: string;

  /**
   * Predefined cost options to display
   */
  costOptions?: number[];

  /**
   * Optional test ID for the input component
   */
  testID?: string;

  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
}

/**
 * A component for inputing and selecting cost values with predefined options
 */
export const CostSelector: React.FC<CostSelectorProps<CreateEventFormType>> = ({
  control,
  setValue,
  name = 'cost',
  handleFieldBlur,
  handleFieldUnBlur,
  label = 'Cost per request',
  costOptions = [0, 10000, 20000, 50000],
  testID = 'cost-input',
}) => {
  return (
    <View>
      <ControlledInput
        testID={testID}
        control={control}
        name={name as any}
        label={label}
        handleFieldBlur={handleFieldBlur}
        handleFieldUnBlur={handleFieldUnBlur}
        // onChangeText={(cost) => setValue('tickets.0.price', Number(cost))}
        onChangeText={(cost) => {
          const formatted = formatNumberWithCommas(cost);
          setValue('tickets.0.price', formatted); // store as string with commas
        }}
      />
      <ScrollView
        horizontal
        contentContainerStyle={{
          gap: 8,
        }}
      >
        {costOptions.map((cost, index) => (
          <CostOption
            key={index}
            option={cost === 0 ? 'Free' : formatAmount(cost)}
            onPress={() => setValue(name as any, Number(cost))}
          />
        ))}
      </ScrollView>
    </View>
  );
};
