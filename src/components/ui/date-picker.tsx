import { Ionicons } from '@expo/vector-icons';
import {
  addDays,
  format,
  isSameYear,
  isToday,
  isTomorrow,
  startOfDay,
  startOfWeek,
  subDays,
} from 'date-fns';
import { useColorScheme } from 'nativewind';
import React, { useCallback, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

import { cn } from '@/lib';

import { Button } from './button';
import colors from './colors';
import semanticColors from './semantic-colors';
import { MdBoldLabel, SmBoldLabel, XsBoldLabel } from './typography';

interface DatePickerProps {
  onSelected?: (v: Date) => void;
}

const initDays = (date: Date) => {
  const weekStart = startOfWeek(date);
  const newDays: Date[] = [];
  for (let i = 0; i < 7; i++) {
    newDays.push(addDays(weekStart, i));
  }
  return newDays;
};

export const DatePicker: React.FC<DatePickerProps> = ({ onSelected }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [days, setDays] = useState<Date[]>(initDays(new Date()));
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const addNewDays = useCallback((date: Date) => {
    setDays(initDays(date));
  }, []);

  const handlePrev = (): void => {
    const dayStart = startOfDay(currentDate);
    const newDate = subDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    if (onSelected) {
      onSelected(newDate);
    }
  };

  const handleNext = (): void => {
    const dayStart = startOfDay(currentDate);
    const newDate = addDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    if (onSelected) {
      onSelected(newDate);
    }
  };

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: any) => {
    console.warn('A date has been picked: ', date);
    const newDate = date || new Date();
    setCurrentDate(newDate);
    const weekStart = startOfWeek(newDate);
    addNewDays(weekStart);
    if (onSelected) {
      onSelected(newDate);
    }
    hideDatePicker();
  };

  return (
    <View>
      {/* Top */}
      <View className="flex-row items-center justify-between gap-4 py-4">
        <View className="size-8" />
        <View className="flex-row items-center gap-4">
          <TouchableOpacity onPress={handlePrev}>
            <Ionicons
              name="chevron-back"
              size={24}
              color={isDark ? 'white' : semanticColors.accent.moderate}
            />
          </TouchableOpacity>
          <View>
            <MdBoldLabel className="text-grey-100 dark:text-white">
              {isToday(currentDate)
                ? 'Today'
                : isTomorrow(currentDate)
                  ? 'Tomorrow'
                  : format(
                      currentDate,
                      `EEE, dd MMMM${isSameYear(currentDate, new Date()) ? '' : ' yyyy'}`
                    )}
            </MdBoldLabel>
          </View>
          <TouchableOpacity onPress={handleNext}>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={isDark ? 'white' : semanticColors.accent.moderate}
            />
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={showDatePicker}>
          <Ionicons
            name="calendar-clear-outline"
            size={28}
            color={colors.brand[60]}
          />
        </TouchableOpacity>
      </View>

      {/* Horizontal Date */}
      <View className="gap-2 px-1 py-2">
        <View className="flex-row justify-between">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <View key={index} className="size-10 items-center justify-center">
              <XsBoldLabel className=" text-grey-100 dark:text-white">
                {day}
              </XsBoldLabel>
            </View>
          ))}
        </View>
        {/* Date numbers row */}
        <View className="flex-row justify-between">
          {days.map((day, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setCurrentDate(day);
                if (onSelected) {
                  onSelected(day);
                }
              }}
              className={`size-10 items-center justify-center rounded-full ${
                format(day, 'd') === format(currentDate, 'd')
                  ? 'bg-brand-60'
                  : 'bg-transparent'
              }`}
            >
              <SmBoldLabel
                className={cn(
                  format(day, 'd') === format(currentDate, 'd')
                    ? 'text-white dark:text-white'
                    : 'text-grey-50 dark:text-grey-60'
                )}
              >
                {format(day, 'd')}
              </SmBoldLabel>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        mode="date"
        display="inline"
        date={currentDate}
        // minimumDate={new Date()}
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
        pickerStyleIOS={{ alignSelf: 'center' }}
        accentColor={colors.brand[60]}
        buttonTextColorIOS={colors.brand[60]}
        confirmTextIOS="Next"
        customConfirmButtonIOS={({ onPress, label }) => (
          <Button label={label} className="m-4 mt-5" onPress={onPress} />
        )}
        customCancelButtonIOS={() => <></>}
      />
    </View>
  );
};
