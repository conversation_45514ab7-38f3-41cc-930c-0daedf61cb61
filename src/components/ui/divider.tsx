import React from 'react';
import { View } from 'react-native';

import { cn } from '@/lib';

interface DividerProps {
  className?: string;
  color?: string;
  thickness?: number;
}

export function Divider({
  className,
  color = 'bg-grey-20 dark:bg-grey-80',
  thickness = 1,
}: DividerProps) {
  return (
    <View
      className={cn('w-full mb-2', color, className)}
      style={{ height: thickness }}
    />
  );
}
