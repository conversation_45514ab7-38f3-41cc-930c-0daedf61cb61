import { useColorScheme } from 'nativewind';

import {
  DarkEmptyIcon,
  DarkSearchIcon,
  LightEmptyIcon,
  LightSearchIcon,
  Tiny,
  View,
} from '@/components/ui';

interface EmptyStateProps {
  text?: string;
  type?: 'app' | 'search';
}

export const EmptyState = ({ text, type = 'app' }: EmptyStateProps) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className="flex-1 items-center justify-center gap-3.5">
      {isDark ? (
        <>{type === 'search' ? <DarkSearchIcon /> : <DarkEmptyIcon />}</>
      ) : (
        <>{type === 'search' ? <LightSearchIcon /> : <LightEmptyIcon />}</>
      )}
      <Tiny className="text-grey-50 dark:text-grey-60">
        {text || 'There is nothing here'}
      </Tiny>
    </View>
  );
};
