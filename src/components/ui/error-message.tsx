import { Text } from '@/components/ui';

type FieldStates = {
  [key: string]: {
    isBlurred?: boolean;
    isValid?: boolean;
  };
};

interface ErrorMessageProps {
  name: string;
  fieldStates: FieldStates;
  message: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  name,
  fieldStates,
  message,
}) => {
  if (
    fieldStates?.[name]?.isBlurred &&
    fieldStates?.[name]?.isValid === false
  ) {
    return (
      <Text className="pt-0 text-xs text-fg-danger-light dark:text-fg-danger-dark">
        {message}
      </Text>
    );
  }

  return null;
};
