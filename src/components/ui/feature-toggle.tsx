import { TouchableOpacity } from 'react-native';

import { MdRegularLabel, SmRegularLabel, Switch, View } from '@/components/ui';

type FeatureToggleProps = {
  title: string;
  subtitle?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  accessibilityLabel: string;
  editText?: string;
  onEditPress?: () => void;
};

export const FeatureToggle: React.FC<FeatureToggleProps> = ({
  title,
  subtitle,
  checked,
  onChange,
  accessibilityLabel,
  editText,
  onEditPress,
}) => (
  <View className="flex-row items-center justify-center gap-x-4 rounded-md bg-bg-subtle-light px-4 py-3 dark:bg-bg-subtle-dark">
    <View className="flex-1 gap-2.5">
      <MdRegularLabel>{title}</MdRegularLabel>
      {subtitle && !editText ? (
        <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
          {subtitle}
        </SmRegularLabel>
      ) : null}
      {editText ? (
        <TouchableOpacity onPress={onEditPress}>
          <SmRegularLabel className="text-fg-muted-light underline dark:text-fg-muted-dark">
            {editText}
          </SmRegularLabel>
        </TouchableOpacity>
      ) : null}
    </View>
    <Switch
      checked={checked}
      onChange={onChange}
      nativeID={accessibilityLabel.toLowerCase().replace(/\s+/g, '-')}
      accessibilityLabel={accessibilityLabel}
    />
  </View>
);
