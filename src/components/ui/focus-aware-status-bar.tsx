import { useIsFocused } from '@react-navigation/native';
import { useColorScheme } from 'nativewind';
import * as React from 'react';
import { Platform, StatusBar, type StatusBarStyle } from 'react-native';
import { SystemBars, type SystemBarStyle } from 'react-native-edge-to-edge';

export const STATUSBAR_HEIGHT = StatusBar.currentHeight;

type Props = {
  hidden?: boolean;
  colorSchemeProp?: SystemBarStyle;
  barStyle?: StatusBarStyle;
  defaultStatusBar?: boolean;
  backgroundColor?: string;
  translucent?: boolean;
};
export const FocusAwareStatusBar = ({
  hidden = false,
  colorSchemeProp,
  defaultStatusBar = false,
  backgroundColor,
  translucent = true,
  barStyle,
}: Props) => {
  const isFocused = useIsFocused();
  const { colorScheme } = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  const invertedColorScheme = isDarkMode ? 'light' : 'dark';

  const finalColorScheme = colorSchemeProp || invertedColorScheme;

  if (Platform.OS === 'web') return null;

  return defaultStatusBar || Platform.OS === 'android' ? (
    <StatusBar
      translucent={translucent}
      barStyle={barStyle || (isDarkMode ? 'light-content' : 'dark-content')}
      backgroundColor={backgroundColor}
    />
  ) : isFocused ? (
    <SystemBars style={finalColorScheme} hidden={hidden} />
  ) : null;
};
