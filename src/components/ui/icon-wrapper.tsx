import React from 'react';
import {
  type StyleProp,
  View,
  type ViewProps,
  type ViewStyle,
} from 'react-native';
import Animated from 'react-native-reanimated';

import colors from './colors';

const ForwardedView = React.forwardRef(
  (props: ViewProps, ref: React.LegacyRef<View>) => (
    <View ref={ref} {...props} />
  )
);
// Create an animated view to wrap the icon
const AnimatedView = Animated.createAnimatedComponent(ForwardedView);

interface IconWrapperProps {
  icon: React.DetailedReactHTMLElement<
    {
      color: string;
      style: any;
    },
    HTMLElement
  >;
  isFocused: boolean;
  isFilled: boolean;
  hasError: boolean;
  className?: string;
  style?: StyleProp<ViewStyle>;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
  className,
  icon,
  isFocused,
  isFilled,
  hasError,
  style,
}) => {
  const getIconColor = () => {
    if (hasError) return colors.red[80];
    if (isFilled) return colors.brand[60];
    if (isFocused) return colors.brand[60];
    return colors.grey[50];
  };

  const iconColor = React.useMemo(
    () => getIconColor(),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isFocused, isFilled, hasError]
  );

  const iconWithState = React.cloneElement(icon, {
    color: iconColor,
    style: icon.props?.style || {},
  });

  return (
    <AnimatedView className={className} style={[style]}>
      {iconWithState}
    </AnimatedView>
  );
};
