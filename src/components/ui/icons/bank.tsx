import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const BankIcon = ({ color = '#7257FF', style, ...props }: SvgProps) => (
  <Svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M16.6654 10.0003C16.6654 10.3685 16.3669 10.667 15.9987 10.667C15.6305 10.667 15.332 10.3685 15.332 10.0003C15.332 9.6321 15.6305 9.33362 15.9987 9.33362C16.3669 9.33362 16.6654 9.6321 16.6654 10.0003Z"
      fill="white"
    />
    <Path
      d="M23.9987 14.667H7.9987M23.9987 14.667C25.4715 14.667 26.6654 13.473 26.6654 12.0003V11.1816C26.6654 10.07 25.9758 9.07505 24.935 8.68474L17.8714 6.03587C16.664 5.5831 15.3334 5.5831 14.126 6.03587L7.06237 8.68474C6.02156 9.07505 5.33203 10.07 5.33203 11.1816V12.0003C5.33203 13.473 6.52594 14.667 7.9987 14.667M23.9987 14.667H18.6654V22.667H23.9987V14.667ZM7.9987 14.667H13.332V22.667H7.9987V14.667ZM7.18193 26.667H24.8155C25.7255 26.667 26.3682 25.7754 26.0804 24.912L25.6359 23.5787C25.4544 23.0342 24.9449 22.667 24.371 22.667H7.62638C7.05247 22.667 6.54295 23.0342 6.36147 23.5787L5.91702 24.912C5.62923 25.7754 6.27186 26.667 7.18193 26.667ZM16.6654 10.0003C16.6654 10.3685 16.3669 10.667 15.9987 10.667C15.6305 10.667 15.332 10.3685 15.332 10.0003C15.332 9.6321 15.6305 9.33362 15.9987 9.33362C16.3669 9.33362 16.6654 9.6321 16.6654 10.0003Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linejoin="round"
    />
  </Svg>
);

export default BankIcon;
