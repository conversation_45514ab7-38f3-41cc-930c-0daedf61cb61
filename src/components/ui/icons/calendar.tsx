import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const CalendarIcon = ({ color = '#6E7375', style, ...props }: SvgProps) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M8.60156 6L8.60156 4M16.6016 6V4M12.6016 6V4M8.60156 19H16.6016C18.2584 19 19.6016 17.6569 19.6016 16V8C19.6016 6.34315 18.2584 5 16.6016 5H8.60156C6.94471 5 5.60156 6.34315 5.60156 8V16C5.60156 17.6569 6.94471 19 8.60156 19ZM11.1659 10.9198L9.79992 11.1182C9.38981 11.1778 9.22605 11.6818 9.52281 11.9711L10.5112 12.9346C10.6291 13.0494 10.6828 13.2149 10.655 13.3771L10.4217 14.7375C10.3516 15.146 10.7803 15.4575 11.1472 15.2646L12.3689 14.6223C12.5146 14.5457 12.6886 14.5457 12.8342 14.6223L14.056 15.2646C14.4228 15.4575 14.8515 15.146 14.7814 14.7375L14.5481 13.3771C14.5203 13.2149 14.5741 13.0494 14.6919 12.9346L15.6803 11.9711C15.9771 11.6818 15.8133 11.1778 15.4032 11.1182L14.0373 10.9198C13.8744 10.8961 13.7336 10.7938 13.6608 10.6462L13.0499 9.40849C12.8665 9.03687 12.3366 9.03687 12.1532 9.40849L11.5423 10.6462C11.4695 10.7938 11.3287 10.8961 11.1659 10.9198Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <Path
      d="M8.60156 6L8.60156 4M16.6016 6V4M12.6016 6V4M11.1659 10.9198L9.79992 11.1182C9.38981 11.1778 9.22605 11.6818 9.52281 11.9711L10.5112 12.9346C10.6291 13.0494 10.6828 13.2149 10.655 13.3771L10.4217 14.7375C10.3516 15.146 10.7803 15.4575 11.1472 15.2646L12.3689 14.6223C12.5146 14.5457 12.6886 14.5457 12.8342 14.6223L14.056 15.2646C14.4228 15.4575 14.8515 15.146 14.7814 14.7375L14.5481 13.3771C14.5203 13.2149 14.5741 13.0494 14.6919 12.9346L15.6803 11.9711C15.9771 11.6818 15.8133 11.1778 15.4032 11.1182L14.0373 10.9198C13.8744 10.8961 13.7336 10.7938 13.6608 10.6462L13.0499 9.40849C12.8665 9.03687 12.3366 9.03687 12.1532 9.40849L11.5423 10.6462C11.4695 10.7938 11.3287 10.8961 11.1659 10.9198Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default CalendarIcon;
