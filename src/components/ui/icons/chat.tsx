import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const ChatIcon = ({
  strokeColor = '#6E7375',
  style,
  ...props
}: SvgProps & { strokeColor?: string }) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M12.8806 16.8042C12.8264 16.5405 12.7991 16.2711 12.7998 16V15.7647C12.8539 14.7838 13.2679 13.8574 13.9626 13.1628C14.6572 12.4681 15.5836 12.0541 16.5645 12H16.7998C17.0715 11.9993 17.3416 12.0267 17.6059 12.0811M12.8806 16.8042C12.9505 17.1448 13.0655 17.4758 13.2233 17.7882C13.5554 18.4526 14.0658 19.0114 14.6975 19.402C15.3292 19.7926 16.0571 19.9997 16.7998 20C17.311 20.0013 17.8166 19.9032 18.2886 19.7123C18.4867 19.6322 18.7017 19.5957 18.9123 19.6314L19.5041 19.6944C20.0743 19.7552 20.5552 19.2742 20.4943 18.704L20.4312 18.1125C20.3955 17.9019 20.432 17.6869 20.5121 17.4888C20.703 17.0168 20.8011 16.5112 20.7998 16C20.7995 15.2573 20.5924 14.5293 20.2018 13.8977C19.8112 13.266 19.2524 12.7556 18.588 12.4235C18.2762 12.266 17.9458 12.1512 17.6059 12.0811M12.8806 16.8042C12.3661 16.9334 11.8353 16.9998 11.2998 17C10.469 17.0021 9.64754 16.8428 8.88051 16.5326C8.55866 16.4024 8.20926 16.343 7.86696 16.401L6.45885 16.551C5.76187 16.6252 5.17417 16.0374 5.24856 15.3404L5.39879 13.9328C5.45678 13.5905 5.39741 13.2411 5.26725 12.9193C4.95704 12.1523 4.79766 11.3308 4.79983 10.5C4.80029 9.29311 5.13676 8.11019 5.77155 7.08373C6.40634 6.05726 7.31437 5.2278 8.39393 4.68825C9.29484 4.2332 10.2905 3.99739 11.2998 4.00002H11.6822C13.2761 4.08796 14.7815 4.76072 15.9103 5.8895C17.0391 7.01827 17.7118 8.52374 17.7998 10.1176V10.5C17.8012 11.0349 17.7356 11.566 17.6059 12.0811"
      stroke={strokeColor}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default ChatIcon;
