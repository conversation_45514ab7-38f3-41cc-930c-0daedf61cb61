import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const ChevronLeft = ({ color = '#7257FF', style, ...props }: SvgProps) => (
  <Svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M20.5 24L12.9195 16.9428C12.3602 16.4221 12.3602 15.5779 12.9195 15.0572L20.5 8"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
    />
  </Svg>
);

export default ChevronLeft;
