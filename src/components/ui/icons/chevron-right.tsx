import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const ChevronRight = ({ color = '#7257FF', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M9 18L14.6854 12.7071C15.1049 12.3166 15.1049 11.6834 14.6854 11.2929L9 6"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
    />
  </Svg>
);

export default ChevronRight;
