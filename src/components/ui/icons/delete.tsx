import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const DeleteIcon = ({ color = '#DB340B', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M10.0016 11.5L10.446 16.1667M14.0016 11.5L13.5571 16.1667M4.35156 7.92453H6.16283M6.16283 7.92453C10.8786 7.92453 19.6353 7.92453 19.6353 7.92453M6.16283 7.92453L6.80765 17.2079C6.91686 18.7803 8.22426 20 9.80044 20H14.2249C15.7919 20 17.095 18.7939 17.2159 17.2316L17.9365 7.92453C17.9365 7.92453 10.2891 7.92453 6.16283 7.92453ZM8.87986 7.92453V6C8.87986 4.89543 9.77529 4 10.8799 4H13.2195C14.3241 4 15.2195 4.89543 15.2195 6V7.92453"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default DeleteIcon;
