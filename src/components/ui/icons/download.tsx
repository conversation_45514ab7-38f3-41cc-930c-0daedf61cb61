import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const DownloadIcon = ({ color = '#7257FF', style, ...props }: SvgProps) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M13.3332 9.7781V11.3337C13.3332 12.4382 12.4377 13.3337 11.3332 13.3337H4.66651C3.56194 13.3337 2.66651 12.4382 2.66651 11.3337L2.6665 9.7781M5.03687 6.81514L7.99984 9.7781M7.99984 9.7781L10.9628 6.81514M7.99984 9.7781V2.66699"
      stroke={color}
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default DownloadIcon;
