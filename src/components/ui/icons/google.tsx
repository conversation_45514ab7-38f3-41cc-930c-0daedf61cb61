import * as React from 'react';
import { StyleSheet } from 'react-native';
import Svg, { <PERSON>lipP<PERSON>, Defs, G, Path, type SvgProps } from 'react-native-svg';

import { isRTL } from '@/lib';

const GoogleIcon = ({ style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <G clipPath="url(#clip0_24257_2694)">
      <Path
        d="M23.713 12.276c0-.816-.066-1.636-.207-2.438H12.187v4.62h6.482a5.554 5.554 0 01-2.399 3.647v2.999h3.867c2.271-2.09 3.576-5.177 3.576-8.828z"
        fill="#4285F4"
      />
      <Path
        d="M12.187 24.001c3.237 0 5.966-1.062 7.955-2.897l-3.867-2.998c-1.076.732-2.465 1.146-4.083 1.146-3.13 0-5.785-2.112-6.738-4.951h-3.99v3.09a12.002 12.002 0 0010.723 6.61z"
        fill="#34A853"
      />
      <Path
        d="M5.45 14.3a7.188 7.188 0 010-4.594v-3.09H1.464a12.01 12.01 0 000 10.776L5.45 14.3z"
        fill="#FBBC04"
      />
      <Path
        d="M12.187 4.75a6.52 6.52 0 014.604 1.799l3.426-3.426A11.533 11.533 0 0012.187 0 11.998 11.998 0 001.464 6.615l3.986 3.09c.948-2.843 3.607-4.955 6.737-4.955z"
        fill="#EA4335"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_24257_2694">
        <Path fill="#fff" d="M0 0H24V24H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default GoogleIcon;
