import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const GraphChart = ({ color = '#B4A6FF', style, ...props }: SvgProps) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M2.66797 2.66602V10.666C2.66797 12.1388 3.86188 13.3327 5.33464 13.3327H13.3346M5.33464 10.666V8.66602M10.668 10.666V6.66602M8.0013 10.666V4.66602"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default GraphChart;
