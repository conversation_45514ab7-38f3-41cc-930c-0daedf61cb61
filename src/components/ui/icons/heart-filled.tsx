import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const HeartFilledIcon = ({ color = '#7257FF', style, ...props }: SvgProps) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M19.1662 7.23375C18.7752 6.84262 18.311 6.53234 17.8001 6.32065C17.2892 6.10896 16.7416 6 16.1886 6C15.6356 6 15.088 6.10896 14.5771 6.32065C14.0662 6.53234 13.602 6.84262 13.2111 7.23375L12.7081 7.73674C12.5378 7.90705 12.2616 7.90705 12.0913 7.73674L11.5883 7.23375C10.7986 6.44406 9.72759 6.00041 8.61079 6.00041C7.49399 6.00041 6.42294 6.44406 5.63324 7.23375C4.84355 8.02344 4.3999 9.0945 4.3999 10.2113C4.3999 11.3281 4.84355 12.3991 5.63324 13.1888L10.5495 18.1051C11.5713 19.1269 13.2281 19.1269 14.2499 18.1051L19.1662 13.1888C19.5573 12.7979 19.8676 12.3337 20.0793 11.8228C20.2909 11.3119 20.3999 10.7643 20.3999 10.2113C20.3999 9.65828 20.2909 9.11068 20.0793 8.59978C19.8676 8.08888 19.5573 7.6247 19.1662 7.23375Z"
      fill={color}
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default HeartFilledIcon;
