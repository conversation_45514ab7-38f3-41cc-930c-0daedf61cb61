import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const HomeIcon = ({ color = '#6E7375', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M10.8008 4.64844C11.4597 4.15258 12.3727 4.12196 13.0635 4.55566L13.1992 4.64844L13.2002 4.64941L19.1982 9.14746V9.14844C19.7011 9.52742 19.9999 10.1259 20 10.749V16.749C20 18.4048 18.6558 19.749 17 19.749H7C5.34421 19.749 4 18.4048 4 16.749V10.749C4.00014 10.1259 4.29888 9.52742 4.80176 9.14844L4.80078 9.14746L10.7998 4.64941L10.8008 4.64844Z"
      stroke={color}
      stroke-width="1.5"
    />
  </Svg>
);

export default HomeIcon;
