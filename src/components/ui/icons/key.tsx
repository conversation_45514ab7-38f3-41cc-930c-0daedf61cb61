import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const KeyIcon = ({ color = 'white', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M14.8701 3.75C16.3081 3.75003 17.661 4.30751 18.6768 5.32324C20.7715 7.41798 20.7715 10.832 18.6768 12.9268C17.6739 13.9296 16.2922 14.4805 14.8799 14.4805C14.5479 14.4805 14.2202 14.4528 13.8955 14.3896L13.6338 14.3379L13.4453 14.5273L11.4355 16.5479C11.3639 16.6158 11.2687 16.6391 11.1719 16.6104H11.1709L10.3506 16.3701L10.0664 16.2871L9.85645 16.4961L9.5166 16.8369L9.29102 17.0615L9.40039 17.3613L9.66992 18.1016L9.6709 18.1035C9.701 18.1848 9.68242 18.2929 9.61035 18.3711L7.79785 20.1758L7.79688 20.1768C7.7496 20.224 7.68596 20.25 7.62012 20.25H4C3.86614 20.25 3.75 20.1339 3.75 20V16.3799C3.75002 16.3324 3.76411 16.2808 3.79688 16.2305L3.83594 16.1816L9.47363 10.5537L9.65918 10.3682L9.61133 10.1094C9.2903 8.36645 9.83293 6.56355 11.0732 5.32324C12.0896 4.30685 13.433 3.75 14.8701 3.75ZM16.8965 7.09082C16.4058 6.59677 15.6245 6.60888 15.1367 7.09668L14.4365 7.79688C13.9449 8.28873 13.9359 9.07958 14.4424 9.56934V9.56836C14.6879 9.82076 15.0093 9.92969 15.3203 9.92969C15.6356 9.92961 15.9545 9.81198 16.2031 9.56348L16.9033 8.86328C17.3914 8.37514 17.4032 7.59313 16.9082 7.10254H16.9092C16.9071 7.10039 16.9044 7.09881 16.9023 7.09668C16.9005 7.09491 16.8993 7.09258 16.8975 7.09082H16.8965Z"
      fill="#131214"
      stroke={color}
    />
  </Svg>
);

export default KeyIcon;
