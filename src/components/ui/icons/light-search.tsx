import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';

import { isRTL } from '@/lib';

const LightSearchIcon = ({ style, ...props }: SvgProps) => (
  <Svg
    width="165"
    height="86"
    viewBox="0 0 165 86"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <G clip-path="url(#clip0_28356_35447)">
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M157.626 3.2168C161.421 3.2168 164.5 6.31969 164.5 10.1451C164.5 13.9705 161.421 17.0734 157.626 17.0734H118.344C122.14 17.0734 125.219 20.1763 125.219 24.0017C125.219 27.8271 122.14 30.93 118.344 30.93H139.949C143.745 30.93 146.823 34.0329 146.823 37.8583C146.823 41.6837 143.745 44.7866 139.949 44.7866H129.957C125.169 44.7866 121.29 47.8895 121.29 51.7149C121.29 54.2635 123.254 56.5746 127.183 58.6432C130.978 58.6432 134.057 61.7461 134.057 65.5715C134.057 69.3969 130.978 72.4998 127.183 72.4998H45.6737C41.8781 72.4998 38.7994 69.3969 38.7994 65.5715C38.7994 61.7461 41.8781 58.6432 45.6737 58.6432H7.37425C3.57868 58.6432 0.5 55.5403 0.5 51.7149C0.5 47.8895 3.57868 44.7866 7.37425 44.7866H46.6557C50.4513 44.7866 53.5299 41.6837 53.5299 37.8583C53.5299 34.0329 50.4513 30.93 46.6557 30.93H22.1048C18.3092 30.93 15.2305 27.8271 15.2305 24.0017C15.2305 20.1763 18.3092 17.0734 22.1048 17.0734H61.3862C57.5907 17.0734 54.512 13.9705 54.512 10.1451C54.512 6.31969 57.5907 3.2168 61.3862 3.2168H157.626ZM157.626 30.93C161.421 30.93 164.5 34.0329 164.5 37.8583C164.5 41.6837 161.421 44.7866 157.626 44.7866C153.83 44.7866 150.751 41.6837 150.751 37.8583C150.751 34.0329 153.83 30.93 157.626 30.93Z"
        fill="#F4F6F7"
      />
      <Path
        d="M72.6797 67.5501C90.8474 67.5501 105.578 52.7037 105.578 34.3932C105.578 16.0827 90.8474 1.23633 72.6797 1.23633C54.512 1.23633 39.7815 16.0827 39.7815 34.3932C39.7815 52.7037 54.512 67.5501 72.6797 67.5501Z"
        fill="#6E7375"
        stroke="#DADDDE"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M67.4062 60.1233C69.1346 60.445 70.8924 60.6132 72.6797 60.6231C87.0518 60.6231 98.7037 48.8797 98.7037 34.3946C98.7037 19.9095 87.0518 8.16602 72.6797 8.16602C68.9873 8.16602 65.4765 8.94297 62.2947 10.3385C56.7658 12.7684 52.2386 17.0738 49.4988 22.4581C47.682 26.0361 46.6558 30.0941 46.6558 34.3946C46.6558 38.2942 47.5003 41.9959 49.0126 45.3215C50.0929 47.6969 51.5168 49.8793 53.2207 51.8094"
        fill="#DADDDE"
      />
      <Path
        d="M67.4062 60.1233C69.1346 60.445 70.8924 60.6132 72.6797 60.6231C87.0518 60.6231 98.7037 48.8797 98.7037 34.3946C98.7037 19.9095 87.0518 8.16602 72.6797 8.16602C68.9873 8.16602 65.4765 8.94297 62.2947 10.3385C56.7658 12.7684 52.2386 17.0738 49.4988 22.4581C47.682 26.0361 46.6558 30.0941 46.6558 34.3946C46.6558 38.2942 47.5003 41.9959 49.0126 45.3215C50.0929 47.6969 51.5168 49.8793 53.2207 51.8094"
        stroke="#DADDDE"
        stroke-linecap="round"
      />
      <Path
        d="M56.2795 54.7578C58.3909 56.4849 60.7724 57.8904 63.3551 58.8851"
        stroke="#E6E9EB"
        stroke-linecap="round"
      />
      <Path d="M99.6855 60.623L105.578 66.5616" stroke="#DADDDE" />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M104.625 65.602C102.764 67.4776 102.764 70.5112 104.625 72.3868L115.511 83.3583C117.372 85.2338 120.382 85.2338 122.243 83.3583C124.104 81.4827 124.104 78.4491 122.243 76.5735L111.357 65.602C109.496 63.7264 106.486 63.7264 104.625 65.602Z"
        fill="#6E7375"
        stroke="#DADDDE"
      />
      <Path
        d="M109.506 67.5508L120.308 78.4381"
        stroke="#DADDDE"
        stroke-width="5"
        stroke-linecap="round"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M66.2965 23.011C66.2965 34.4922 75.5276 43.7959 86.9192 43.7959C89.1583 43.7959 91.3089 43.4346 93.327 42.7715C90.0323 51.0063 82.0287 56.8211 72.6797 56.8211C60.3895 56.8211 50.4268 46.78 50.4268 34.3932C50.4268 23.1694 58.6071 13.8706 69.2868 12.2227C67.3914 15.3701 66.2965 19.0619 66.2965 23.011Z"
        fill="#6E7375"
      />
      <Path
        d="M73.1706 16.084C71.9185 16.084 70.6959 16.2077 69.5175 16.4452M65.9478 17.5439C59.2306 20.3894 54.512 27.0851 54.512 34.8894"
        stroke="#E6E9EB"
        stroke-linecap="round"
      />
      <Path
        d="M125.39 34.6661H117.362M131.602 26.9707H114.735M138.476 26.9707H136.296"
        stroke="#E6E9EB"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M37.0071 56.441H28.979M32.4161 47.7559H15.5496M9.82925 47.7559H5.68506"
        stroke="#E6E9EB"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_28356_35447">
        <Rect width="164" height="86" fill="white" transform="translate(0.5)" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default LightSearchIcon;
