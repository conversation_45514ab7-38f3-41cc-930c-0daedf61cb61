import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const LiveIcon = ({ color = 'black', style, ...props }: SvgProps) => (
  <Svg
    width={28}
    height={28}
    viewBox="0 0 28 28"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M6.98869 5.75115C7.15255 5.91521 7.24459 6.13761 7.24459 6.36948C7.24459 6.60136 7.15255 6.82375 6.98869 6.98782C6.06781 7.90867 5.33733 9.00189 4.83895 10.2051C4.34057 11.4082 4.08406 12.6978 4.08406 14.0001C4.08406 15.3024 4.34057 16.5919 4.83895 17.7951C5.33733 18.9982 6.06781 20.0915 6.98869 21.0123C7.148 21.1774 7.2361 21.3985 7.234 21.6279C7.23189 21.8573 7.13977 22.0767 6.97746 22.2389C6.81515 22.4011 6.59564 22.493 6.36622 22.4949C6.1368 22.4968 5.91581 22.4084 5.75086 22.249C1.19503 17.6931 1.19503 10.307 5.75086 5.75115C5.91492 5.58729 6.13732 5.49525 6.36919 5.49525C6.60107 5.49525 6.82463 5.58729 6.98869 5.75115ZM22.2487 5.75115C26.8045 10.307 26.8045 17.6943 22.2487 22.2501C22.0828 22.4047 21.8634 22.4889 21.6367 22.4849C21.4101 22.4809 21.1938 22.389 21.0335 22.2287C20.8732 22.0684 20.7813 21.8521 20.7773 21.6254C20.7733 21.3987 20.8575 21.1794 21.012 21.0135C21.9331 20.0926 22.6637 18.9993 23.1622 17.796C23.6607 16.5928 23.9172 15.3031 23.9172 14.0006C23.9172 12.6982 23.6607 11.4085 23.1622 10.2053C22.6637 9.00198 21.9331 7.90869 21.012 6.98782C20.9261 6.90771 20.8571 6.81111 20.8093 6.70378C20.7615 6.59644 20.7357 6.48058 20.7337 6.36309C20.7316 6.24561 20.7532 6.12891 20.7972 6.01995C20.8412 5.911 20.9067 5.81203 20.9898 5.72894C21.0729 5.64585 21.1719 5.58035 21.2808 5.53634C21.3898 5.49233 21.5065 5.47072 21.624 5.47279C21.7415 5.47487 21.8573 5.50058 21.9647 5.5484C22.072 5.59623 22.1686 5.66518 22.2487 5.75115ZM10.288 9.05048C10.4519 9.21455 10.5439 9.43694 10.5439 9.66882C10.5439 9.90069 10.4519 10.1231 10.288 10.2871C9.80034 10.7747 9.41347 11.3535 9.14953 11.9906C8.88558 12.6277 8.74973 13.3105 8.74973 14.0001C8.74973 14.6897 8.88558 15.3725 9.14953 16.0096C9.41347 16.6466 9.80034 17.2255 10.288 17.713C10.4426 17.8789 10.5267 18.0982 10.5227 18.3249C10.5187 18.5516 10.4269 18.7679 10.2666 18.9282C10.1063 19.0885 9.88999 19.1804 9.6633 19.1844C9.43662 19.1884 9.21723 19.1042 9.05136 18.9496C7.73871 17.6369 7.00127 15.8565 7.00127 14.0001C7.00127 12.1436 7.73871 10.3632 9.05136 9.05048C9.21542 8.88662 9.43782 8.79459 9.66969 8.79459C9.90157 8.79459 10.124 8.88662 10.288 9.05048ZM18.9505 9.05048C20.2632 10.3632 21.0006 12.1436 21.0006 14.0001C21.0006 15.8565 20.2632 17.6369 18.9505 18.9496C18.8698 19.0333 18.7733 19.1 18.6666 19.1459C18.5599 19.1918 18.4451 19.216 18.3289 19.217C18.2127 19.2181 18.0975 19.196 17.9899 19.1521C17.8824 19.1081 17.7846 19.0432 17.7024 18.9611C17.6202 18.879 17.5552 18.7813 17.5112 18.6738C17.4671 18.5663 17.4449 18.4511 17.4459 18.3349C17.4468 18.2187 17.4709 18.1039 17.5167 17.9971C17.5625 17.8903 17.6292 17.7937 17.7127 17.713C18.2002 17.2255 18.5869 16.6467 18.8508 16.0098C19.1146 15.3728 19.2504 14.6901 19.2504 14.0006C19.2504 13.3112 19.1146 12.6285 18.8508 11.9915C18.5869 11.3546 18.2002 10.7758 17.7127 10.2883C17.5581 10.1224 17.474 9.90306 17.478 9.67637C17.482 9.44969 17.5738 9.23341 17.7341 9.07309C17.8944 8.91278 18.1107 8.82095 18.3374 8.81695C18.5641 8.81295 18.7835 8.89709 18.9494 9.05165M14.0004 12.2506C14.4645 12.2506 14.9096 12.435 15.2378 12.7632C15.566 13.0914 15.7504 13.5365 15.7504 14.0006C15.7504 14.4648 15.566 14.9099 15.2378 15.2381C14.9096 15.5663 14.4645 15.7506 14.0004 15.7506C13.5362 15.7506 13.0911 15.5663 12.7629 15.2381C12.4347 14.9099 12.2504 14.4648 12.2504 14.0006C12.2504 13.5365 12.4347 13.0914 12.7629 12.7632C13.0911 12.435 13.5362 12.2506 14.0004 12.2506Z"
      fill={color}
    />
  </Svg>
);

export default LiveIcon;
