import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const MessageFilledIcon = ({
  color = '#7257FF',
  style,
  ...props
}: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M3.83105 8.99316L3.83496 8.99609L4.45215 9.45215L4.46094 9.45801C5.27643 10.0369 6.64446 10.9732 8.0459 11.7666C8.74636 12.1631 9.46439 12.5295 10.1318 12.7979C10.7885 13.0618 11.4426 13.25 12 13.25C12.5572 13.25 13.2099 13.0621 13.8652 12.7988C14.5311 12.5313 15.2472 12.1664 15.9463 11.7705C17.345 10.9785 18.7132 10.0424 19.5381 9.45898C19.8083 9.2688 20.0332 9.10361 20.1719 8.99707L20.1729 8.99805L20.248 8.94043C20.2484 8.96017 20.25 8.98001 20.25 9V15C20.25 16.7939 18.7939 18.25 17 18.25H7C5.20614 18.25 3.75 16.7939 3.75 15V9C3.75 8.97684 3.75141 8.95375 3.75195 8.93066L3.83105 8.99316ZM7 5.75H17C17.8481 5.75 18.617 6.07212 19.1904 6.60352L18.667 7.00195C18.5951 7.0536 18.5153 7.11253 18.4414 7.16797C17.4221 7.90496 16.0645 8.81325 14.8125 9.53516C14.1849 9.89704 13.5925 10.2077 13.0879 10.4258C12.5637 10.6522 12.1987 10.75 12 10.75C11.8013 10.75 11.4343 10.6513 10.9072 10.4238C10.4002 10.205 9.80508 9.89367 9.17578 9.53125C7.91814 8.80695 6.55823 7.89766 5.55469 7.16602H5.55371L5.34082 7.00293L4.81738 6.59277C5.39503 6.06582 6.15875 5.75 7 5.75Z"
      fill={color}
      stroke={color}
    />
  </Svg>
);

export default MessageFilledIcon;
