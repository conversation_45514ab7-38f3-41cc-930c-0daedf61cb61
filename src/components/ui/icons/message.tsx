import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const MessageIcon = ({ color = '#898D8F', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M4.5827 8C4.5827 8 9.99999 12 12 12C14 12 19.4173 8 19.4173 8M4 15C4 16.6569 5.34314 18 7 18H17C18.6569 18 20 16.6569 20 15V9C20 7.34315 18.6569 6 17 6H7.00001C5.34315 6 4.00001 7.34315 4.00001 9L4 15Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
    />
  </Svg>
);

export default MessageIcon;
