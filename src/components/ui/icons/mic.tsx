import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const MicIcon = ({ color = 'white', style, ...props }: SvgProps) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M9.798 9.92242C10.1237 9.66651 10.1803 9.19502 9.92437 8.86932C9.66846 8.54361 9.19697 8.48703 8.87127 8.74294L9.798 9.92242ZM5.59465 12.2712L5.13129 11.6815L5.13129 11.6815L5.59465 12.2712ZM3.72941 10.406L4.31915 10.8694L4.31915 10.8694L3.72941 10.406ZM7.25771 7.12938C7.51362 6.80368 7.45704 6.33219 7.13133 6.07628C6.80563 5.82037 6.33414 5.87695 6.07823 6.20265L7.25771 7.12938ZM2.13764 12.8024C1.84475 13.0952 1.84475 13.5701 2.13764 13.863C2.43053 14.1559 2.90541 14.1559 3.1983 13.863L2.13764 12.8024ZM4.1983 12.863C4.49119 12.5701 4.49119 12.0952 4.1983 11.8024C3.90541 11.5095 3.43053 11.5095 3.13764 11.8024L4.1983 12.863ZM6.53163 5.46902C6.23874 5.17613 5.76387 5.17613 5.47097 5.46902C5.17808 5.76191 5.17808 6.23679 5.47097 6.52968L6.53163 5.46902ZM9.47097 10.5297C9.76386 10.8226 10.2387 10.8226 10.5316 10.5297C10.8245 10.2368 10.8245 9.76191 10.5316 9.46902L9.47097 10.5297ZM13.3346 5.99935H12.5846C12.5846 7.42608 11.428 8.58268 10.0013 8.58268V9.33268V10.0827C12.2565 10.0827 14.0846 8.25451 14.0846 5.99935H13.3346ZM6.66797 5.99935H7.41797C7.41797 4.57261 8.57457 3.41602 10.0013 3.41602V2.66602V1.91602C7.74614 1.91602 5.91797 3.74419 5.91797 5.99935H6.66797ZM10.0013 2.66602V3.41602C11.428 3.41602 12.5846 4.57261 12.5846 5.99935H13.3346H14.0846C14.0846 3.74419 12.2565 1.91602 10.0013 1.91602V2.66602ZM10.0013 9.33268V8.58268C9.73001 8.58268 9.46984 8.54112 9.22604 8.46454L9.0013 9.18008L8.77656 9.89562C9.16413 10.0173 9.57579 10.0827 10.0013 10.0827V9.33268ZM6.79736 6.92217L7.51816 6.71494C7.45308 6.48858 7.41797 6.2487 7.41797 5.99935H6.66797H5.91797C5.91797 6.39031 5.97313 6.76965 6.07656 7.1294L6.79736 6.92217ZM9.33464 9.33268L8.87127 8.74294L5.13129 11.6815L5.59465 12.2712L6.05802 12.861L9.798 9.92242L9.33464 9.33268ZM3.72941 10.406L4.31915 10.8694L7.25771 7.12938L6.66797 6.66602L6.07823 6.20265L3.13967 9.94263L3.72941 10.406ZM3.83464 12.166L4.36497 11.6357C4.15794 11.4287 4.13826 11.0996 4.31915 10.8694L3.72941 10.406L3.13967 9.94263C2.48967 10.7699 2.56037 11.9524 3.30431 12.6963L3.83464 12.166ZM5.59465 12.2712L5.13129 11.6815C4.90107 11.8624 4.57199 11.8427 4.36497 11.6357L3.83464 12.166L3.30431 12.6963C4.04824 13.4403 5.23075 13.511 6.05802 12.861L5.59465 12.2712ZM2.66797 13.3327L3.1983 13.863L4.1983 12.863L3.66797 12.3327L3.13764 11.8024L2.13764 12.8024L2.66797 13.3327ZM6.0013 5.99935L5.47097 6.52968L9.47097 10.5297L10.0013 9.99935L10.5316 9.46902L6.53163 5.46902L6.0013 5.99935Z"
      fill={color}
    />
  </Svg>
);

export default MicIcon;
