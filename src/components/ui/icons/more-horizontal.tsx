import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const MoreHorizontal = ({
  color = '#B4A6FF',
  style,
  fill = 'white',
  ...props
}: SvgProps) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M7.66667 8.33268C7.48257 8.33268 7.33333 8.18344 7.33333 7.99935C7.33333 7.81525 7.48257 7.66602 7.66667 7.66602C7.85076 7.66602 8 7.81525 8 7.99935C8 8.18344 7.85076 8.33268 7.66667 8.33268Z"
      fill={fill}
    />
    <Path
      d="M3.66667 8.33268C3.48257 8.33268 3.33333 8.18344 3.33333 7.99935C3.33333 7.81525 3.48257 7.66602 3.66667 7.66602C3.85076 7.66602 4 7.81525 4 7.99935C4 8.18344 3.85076 8.33268 3.66667 8.33268Z"
      fill={fill}
    />
    <Path
      d="M11.6667 8.33268C11.4826 8.33268 11.3333 8.18344 11.3333 7.99935C11.3333 7.81525 11.4826 7.66602 11.6667 7.66602C11.8508 7.66602 12 7.81525 12 7.99935C12 8.18344 11.8508 8.33268 11.6667 8.33268Z"
      fill={fill}
    />
    <Path
      d="M7.66667 8.33268C7.48257 8.33268 7.33333 8.18344 7.33333 7.99935C7.33333 7.81525 7.48257 7.66602 7.66667 7.66602C7.85076 7.66602 8 7.81525 8 7.99935C8 8.18344 7.85076 8.33268 7.66667 8.33268Z"
      stroke={color}
      stroke-width="1.5"
    />
    <Path
      d="M3.66667 8.33268C3.48257 8.33268 3.33333 8.18344 3.33333 7.99935C3.33333 7.81525 3.48257 7.66602 3.66667 7.66602C3.85076 7.66602 4 7.81525 4 7.99935C4 8.18344 3.85076 8.33268 3.66667 8.33268Z"
      stroke={color}
      stroke-width="1.5"
    />
    <Path
      d="M11.6667 8.33268C11.4826 8.33268 11.3333 8.18344 11.3333 7.99935C11.3333 7.81525 11.4826 7.66602 11.6667 7.66602C11.8508 7.66602 12 7.81525 12 7.99935C12 8.18344 11.8508 8.33268 11.6667 8.33268Z"
      stroke={color}
      stroke-width="1.5"
    />
  </Svg>
);

export default MoreHorizontal;
