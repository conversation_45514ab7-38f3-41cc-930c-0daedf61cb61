import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const Music = ({ color = '#FFF', style, ...props }: SvgProps) => (
  <Svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M9.33333 17.3332L8.57236 8.53973C8.43741 6.98032 9.52405 5.57923 11.068 5.32191L15.7979 4.53359C17.524 4.2459 19.129 5.49067 19.2799 7.23413L20 15.5555M9.33333 17.3332C9.33333 18.806 8.13943 19.9999 6.66667 19.9999C5.19391 19.9999 4 18.806 4 17.3332C4 15.8605 5.19391 14.6666 6.66667 14.6666C8.13943 14.6666 9.33333 15.8605 9.33333 17.3332ZM20 15.5555C20 17.0282 18.8061 18.2221 17.3333 18.2221C15.8606 18.2221 14.6667 17.0282 14.6667 15.5555C14.6667 14.0827 15.8606 12.8888 17.3333 12.8888C18.8061 12.8888 20 14.0827 20 15.5555Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default Music;
