import * as React from 'react';
import { type ColorValue, StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Circle } from 'react-native-svg';

import { isRTL } from '@/lib';

const OnlineIndicator = ({
  color = '#23A15D',
  strokeColor = '#131214',
  size = '20',
  style,
  ...props
}: SvgProps & { strokeColor?: ColorValue; size?: string }) => (
  <Svg
    width={size}
    height={size}
    viewBox={`0 0 ${size} ${size}`}
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Circle
      cx="10"
      cy="10"
      r="8"
      fill={color}
      stroke={strokeColor}
      stroke-width="4"
    />
  </Svg>
);

export default OnlineIndicator;
