import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const PasswordIcon = ({ color = '#898D8F', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M8.20002 11.2V8C8.20002 6.93913 8.62145 5.92172 9.37159 5.17157C10.1217 4.42143 11.1392 4 12.2 4C13.2609 4 14.2783 4.42143 15.0284 5.17157C15.7786 5.92172 16.2 6.93913 16.2 8V11.2M8.00001 20H16.4C18.0569 20 19.4 18.6569 19.4 17V14.2C19.4 12.5431 18.0569 11.2 16.4 11.2H8.00002C6.34317 11.2 5.00002 12.5431 5.00002 14.2L5.00001 17C5 18.6568 6.34315 20 8.00001 20Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default PasswordIcon;
