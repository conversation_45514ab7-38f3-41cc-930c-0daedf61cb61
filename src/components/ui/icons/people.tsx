import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const PeopleIcon = ({ color = '#FFF', style, ...props }: SvgProps) => (
  <Svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M16.3828 16.25C16.3828 16.25 16.6659 15.6234 16.6659 15C16.6659 14.0336 16.2492 13.3333 15.4159 12.9167M13.3325 9.07443C14.3136 8.64579 14.9992 7.66685 14.9992 6.52778C14.9992 5.3887 14.3136 4.40977 13.3325 3.98113M11.4159 6.52778C11.4159 8.0619 10.1722 9.30556 8.63809 9.30556C7.10397 9.30556 5.86031 8.0619 5.86031 6.52778C5.86031 4.99365 7.10397 3.75 8.63809 3.75C10.1722 3.75 11.4159 4.99365 11.4159 6.52778ZM8.55128 12.3437C11.2058 12.3437 12.8582 13.5063 13.6446 14.2513C14.0101 14.5976 14.0371 15.1394 13.7903 15.5782C13.5569 15.9932 13.1177 16.25 12.6416 16.25H4.65623C4.16672 16.25 3.71527 15.986 3.47528 15.5593C3.23303 15.1287 3.24617 14.5987 3.59355 14.2473C4.33026 13.5021 5.90155 12.3437 8.55128 12.3437Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default PeopleIcon;
