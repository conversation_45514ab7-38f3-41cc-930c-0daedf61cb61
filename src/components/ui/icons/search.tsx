import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const SearchIcon = ({ color = '#6E7375', style, ...props }: SvgProps) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M20.1992 20L16.3325 16.1333M18.4214 11.1111C18.4214 15.0385 15.2377 18.2222 11.3103 18.2222C7.38297 18.2222 4.19922 15.0385 4.19922 11.1111C4.19922 7.18375 7.38297 4 11.3103 4C15.2377 4 18.4214 7.18375 18.4214 11.1111Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default SearchIcon;
