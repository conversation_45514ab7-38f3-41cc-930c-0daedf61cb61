import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const ShareIcon = ({ color = '#070707', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M5.60156 12L5.60156 17C5.60156 18.6569 6.94471 20 8.60156 20H15.4016C17.0584 20 18.4016 18.6569 18.4016 17V12M15.2016 7.2L12.0016 4M12.0016 4L8.80156 7.2M12.0016 4V14.4"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default ShareIcon;
