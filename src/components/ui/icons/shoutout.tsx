import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const ShoutoutIcon = ({ color = '#B4A6FF', style, ...props }: SvgProps) => (
  <Svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M10 14H8C5.79 14 4 12.21 4 10C4 7.79 5.79 6 8 6H10M10 14C9.1 12.31 9 10.76 9 10C9 8.09 9.64 6.67 10 6M10 14C10.61 13.95 11.28 13.94 12 14C14.65 14.21 16.71 15.2 18 16C18.46 14.65 19 12.58 19 10C19 8.58 18.84 6.44 18 4C16.71 4.8 14.65 5.79 12 6C11.28 6.06 10.61 6.05 10 6M10 14.07L11.34 17.19C11.72 18.08 11.47 19.15 10.76 19.7C10.4 19.9 9.88 20.09 9.31 19.95C8.17 19.63 7.6 18.11 7 16.41C6.75 15.71 6.47 14.76 6.26 13.59M13 9H15M19 9H20V11H19V9Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default ShoutoutIcon;
