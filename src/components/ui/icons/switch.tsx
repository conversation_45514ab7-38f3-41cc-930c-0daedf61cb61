import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const SwitchToggleIcon = ({ color = '#FFF', style, ...props }: SvgProps) => (
  <Svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M15.1818 4L18.0909 6.90909M18.0909 6.90909L15.1818 9.81818M18.0909 6.90909H8C6.34315 6.90909 5 8.25224 5 9.90909V11.2727M7.90909 20L5 17.0909M5 17.0909L7.90909 14.1818M5 17.0909H15C16.6569 17.0909 18 15.7478 18 14.0909V12.5"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default SwitchToggleIcon;
