import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const Tick = ({ color = '#B4A6FF', style, ...props }: SvgProps) => (
  <Svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M13.8332 4.6333L7.44265 11.0238C6.92195 11.5445 6.07773 11.5445 5.55703 11.0238L3.1665 8.6333"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default Tick;
