import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const TicketQrBg = ({ color = '#53575A', style, ...props }: SvgProps) => (
  <Svg
    width="74"
    height="103"
    viewBox="0 0 74 103"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M70.7738 0H3.22616C3.22616 1.94739 1.78245 3.52468 0 3.52468V6.4766C0.669428 6.4766 1.20981 7.06698 1.20981 7.79836C1.20981 8.52973 0.669428 9.12011 0 9.12011V11.0763C0.669428 11.0763 1.20981 11.6667 1.20981 12.3981C1.20981 13.1294 0.669428 13.7198 0 13.7198V15.817C0.596839 15.817 1.07271 16.3457 1.07271 16.989C1.07271 17.6322 0.588774 18.1609 0 18.1609V20.2581C0.669428 20.2581 1.20981 20.8485 1.20981 21.5799C1.20981 22.3112 0.669428 22.9016 0 22.9016V24.9988C0.596839 24.9988 1.07271 25.5275 1.07271 26.1708C1.07271 26.814 0.588774 27.3427 0 27.3427V29.5545C0.61297 29.5545 1.10496 30.1008 1.10496 30.7705C1.10496 31.4402 0.604905 31.9865 0 31.9865V34.1718C0.604905 34.1718 1.09691 34.7093 1.09691 35.3702C1.09691 36.0311 0.604905 36.5686 0 36.5686V38.7627C0.61297 38.7627 1.10496 39.3002 1.10496 39.9699C1.10496 40.6396 0.61297 41.1771 0 41.1771V43.3712C0.604905 43.3712 1.10496 43.9087 1.10496 44.5784C1.10496 45.2481 0.61297 45.7856 0 45.7856V47.8564C0.669428 47.8564 1.20981 48.4467 1.20981 49.1781C1.20981 49.9095 0.669428 50.4999 0 50.4999V52.5971C0.596839 52.5971 1.07271 53.1258 1.07271 53.769C1.07271 54.4123 0.588774 54.941 0 54.941V57.1527C0.61297 57.1527 1.10496 57.699 1.10496 58.3687C1.10496 59.0384 0.604905 59.5847 0 59.5847V61.77C0.604905 61.77 1.09691 62.3076 1.09691 62.9684C1.09691 63.6293 0.604905 64.1668 0 64.1668V66.3609C0.61297 66.3609 1.10496 66.8985 1.10496 67.5681C1.10496 68.2378 0.61297 68.7753 0 68.7753V70.9695C0.604905 70.9695 1.10496 71.507 1.10496 72.1767C1.10496 72.8464 0.61297 73.3839 0 73.3839V75.5692C0.604905 75.5692 1.10496 76.1067 1.10496 76.7764C1.10496 77.4461 0.61297 77.9836 0 77.9836V80.1777C0.604905 80.1777 1.10496 80.7152 1.10496 81.3849C1.10496 82.0546 0.61297 82.5921 0 82.5921V84.7774C0.604905 84.7774 1.10496 85.3149 1.10496 85.9846C1.10496 86.6543 0.61297 87.1918 0 87.1918V89.3859C0.604905 89.3859 1.10496 89.9234 1.10496 90.5931C1.10496 91.2628 0.61297 91.8003 0 91.8003V93.9856C0.604905 93.9856 1.10496 94.5231 1.10496 95.1928C1.10496 95.8625 0.61297 96.4 0 96.4V99.4753C1.78245 99.4753 3.22616 101.053 3.22616 103H70.7738C72.5482 103 74 101.414 74 99.4753V3.52468C74 1.58611 72.5482 0 70.7738 0Z"
      fill={color}
    />
  </Svg>
);

export default TicketQrBg;
