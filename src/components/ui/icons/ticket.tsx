import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const TicketIcon = ({ color = '#B4A6FF', style, ...props }: SvgProps) => (
  <Svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M7.57041 5.93899C7.69268 5.69125 8.04596 5.69125 8.16824 5.939L8.57548 6.76416C8.62403 6.86254 8.71789 6.93073 8.82646 6.94651L9.73709 7.07883C10.0105 7.11856 10.1197 7.45455 9.92182 7.6474L9.26289 8.2897C9.18433 8.36628 9.14848 8.47661 9.16702 8.58474L9.32258 9.49169C9.36928 9.76399 9.08347 9.97165 8.83893 9.84308L8.02444 9.41488C7.92733 9.36383 7.81132 9.36383 7.71421 9.41488L6.89972 9.84308C6.65518 9.97165 6.36937 9.76399 6.41607 9.49169L6.57162 8.58474C6.59017 8.47661 6.55432 8.36628 6.47576 8.2897L5.81682 7.6474C5.61898 7.45455 5.72815 7.11856 6.00156 7.07883L6.91219 6.94651C7.02076 6.93073 7.11461 6.86254 7.16317 6.76416L7.57041 5.93899Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <Path
      d="M3.99984 4C3.26346 4 2.6665 4.59695 2.6665 5.33333V10.6667C2.6665 11.403 3.26346 12 3.99984 12H11.9998C12.7362 12 13.3332 11.403 13.3332 10.6667V9.33333C12.5968 9.33333 11.9998 8.73638 11.9998 8C11.9998 7.26362 12.5968 6.66667 13.3332 6.66667V5.33333C13.3332 4.59695 12.7362 4 11.9998 4H3.99984Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <Path
      d="M7.57044 5.93874C7.69271 5.69099 8.04599 5.69099 8.16826 5.93874L8.57551 6.76391C8.62406 6.86229 8.71791 6.93048 8.82648 6.94626L9.73711 7.07858C10.0105 7.11831 10.1197 7.4543 9.92185 7.64714L9.26291 8.28945C9.18435 8.36603 9.1485 8.47636 9.16705 8.58449L9.3226 9.49144C9.36931 9.76374 9.08349 9.97139 8.83895 9.84283L8.02446 9.41463C7.92735 9.36357 7.81134 9.36357 7.71424 9.41463L6.89975 9.84283C6.6552 9.97139 6.36939 9.76374 6.4161 9.49144L6.57165 8.58449C6.5902 8.47636 6.55435 8.36603 6.47578 8.28945L5.81685 7.64714C5.61901 7.4543 5.72818 7.11831 6.00159 7.07858L6.91221 6.94626C7.02078 6.93048 7.11464 6.86229 7.16319 6.76391L7.57044 5.93874Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
  </Svg>
);

export default TicketIcon;
