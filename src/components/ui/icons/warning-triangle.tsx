import * as React from 'react';
import { StyleSheet } from 'react-native';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import { isRTL } from '@/lib';

const WarningTriangle = ({ color = '#FFD84D', style, ...props }: SvgProps) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
    style={StyleSheet.flatten([
      style,
      { transform: [{ scaleX: isRTL ? -1 : 1 }] },
    ])}
  >
    <Path
      d="M11.9155 9.37017V13.0001M11.9155 16.7612H11.9235M17.7418 20H6.08931C4.54351 20 3.53434 18.3779 4.21746 16.9912L10.0437 5.16453C10.8086 3.61183 13.0225 3.61182 13.7874 5.16452L19.6136 16.9912C20.2967 18.3779 19.2876 20 17.7418 20Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </Svg>
);

export default WarningTriangle;
