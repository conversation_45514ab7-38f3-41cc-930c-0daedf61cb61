import type { ImageProps } from 'expo-image';
import { Image as NImage } from 'expo-image';
import { cssInterop } from 'nativewind';
import * as React from 'react';

const getRecyclingKey = (source: any) => {
  if (typeof source === 'string') return source;
  if (typeof source === 'object' && source?.uri) return source.uri;
  if (typeof source === 'number') return String(source);
  return undefined;
};

export type ImgProps = ImageProps & {
  className?: string;
};

cssInterop(NImage, { className: 'style' });

export const Image = ({
  style,
  className,
  placeholder = 'L6PZfSi_.AyE_3t7t7R**0o#DgR4',
  ...props
}: ImgProps) => {
  const recyclingKey = getRecyclingKey(props.source);

  return (
    <NImage
      className={className}
      placeholder={placeholder}
      style={style}
      cachePolicy="memory-disk"
      recyclingKey={recyclingKey}
      {...props}
    />
  );
};

export class ImagePreloader {
  private static preloadedUrls = new Set<string>();

  static async preloadImage(url: string): Promise<void> {
    if (this.preloadedUrls.has(url)) {
      return;
    }

    try {
      await NImage.prefetch(url, {
        cachePolicy: 'memory-disk',
      });
      this.preloadedUrls.add(url);
    } catch (error) {
      console.warn('Failed to preload image:', url, error);
    }
  }

  static async preloadImages(urls: string[]): Promise<void> {
    const uniqueUrls = [...new Set(urls)];
    const preloadPromises = uniqueUrls.map((url) => this.preloadImage(url));

    try {
      await Promise.allSettled(preloadPromises);
    } catch (error) {
      console.warn('Some images failed to preload:', error);
    }
  }

  static clearPreloadedCache(): void {
    this.preloadedUrls.clear();
  }
}

export const preloadImages = async (sources: string[]) => {
  await ImagePreloader.preloadImages(sources);
};
