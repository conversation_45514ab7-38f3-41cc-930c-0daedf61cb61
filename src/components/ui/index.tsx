import { cssInterop } from 'nativewind';
import Svg from 'react-native-svg';

export * from './animated-border';
export * from './app-tab';
export * from './bottom-sheet-input';
export * from './button';
export * from './checkbox';
export * from './close-button-tag';
export { default as colors } from './colors';
export * from './cost-option';
export * from './cost-selector';
export * from './cost-selector-with-bottom-sheet';
export * from './date-picker';
export * from './error-message';
export * from './feature-toggle';
export * from './focus-aware-status-bar';
export * from './icons';
export * from './image';
export * from './input';
export * from './list';
export * from './modal';
/* eslint-disable import/no-cycle */
export * from './dialog';
export * from './divider';
export * from './empty';
export * from './persistent-modal';
export * from './pill-option';
export * from './progress-bar';
export * from './radio-group';
export * from './radio-group-option';
export * from './search-input';
export * from './select';
export { default as semanticColors } from './semantic-colors';
export * from './skeleton';
export * from './text';
export * from './typography';
export * from './unordered-list';
export * from './utils';

// export base components from react-native
export {
  ActivityIndicator,
  Pressable,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
export { SafeAreaView } from 'react-native-safe-area-context';

//Apply cssInterop to Svg to resolve className string into style
cssInterop(Svg, {
  className: {
    target: 'style',
  },
});
