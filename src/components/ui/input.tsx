'use client';
import Feather from '@expo/vector-icons/Feather';
import { useColorScheme } from 'nativewind';
import * as React from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import type {
  NativeSyntheticEvent,
  TextInputFocusEventData,
  TextInputProps,
  ViewProps,
} from 'react-native';
import {
  I18nManager,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { TextInput as NTextInput } from 'react-native';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { tv } from 'tailwind-variants';

import { cn } from '@/lib';

import colors from './colors';
import { IconWrapper } from './icon-wrapper';
import { Text } from './text';

const inputTv = tv({
  slots: {
    container: 'relative mb-2',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark',
    input:
      'h-[52px] rounded-md border border-border-subtle-light bg-transparent p-3 pl-12 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    iconContainer:
      'absolute left-3 top-3.5 text-accent-moderate dark:text-accent-moderate',
  },

  variants: {
    focused: {
      true: {
        input:
          'border border-accent-moderate pb-2 pt-5 dark:border-accent-moderate',
      },
    },
    filled: {
      true: {
        input: 'pb-2 pt-5',
      },
    },
    error: {
      true: {
        input: 'border-2 border-red-60 dark:border-red-80',
        label: 'dark:text-danger-80 text-red-80',
      },
    },
    disabled: {
      true: {
        input: 'inherit',
      },
    },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
  },
});

export interface NInputProps extends TextInputProps {
  label?: string;
  disabled?: boolean;
  error?: string;
  isPassword?: boolean;
  icon?: any;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  focusedInputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type InputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  rules?: RuleType<T>;
};

interface ControlledInputProps<T extends FieldValues>
  extends NInputProps,
    InputControllerType<T> {}

const ForwardedView = React.forwardRef(
  (props: ViewProps, ref: React.Ref<View>) => <View ref={ref} {...props} />
);
const AnimatedView = Animated.createAnimatedComponent(ForwardedView);

export const Input = React.forwardRef<NTextInput, NInputProps>((props, ref) => {
  const {
    label,
    error,
    testID,
    isPassword,
    icon,
    iconClassName = '',
    containerClassName = '',
    inputClassName = '',
    focusedInputClassName = '',
    value,
    onBlur,
    handleFieldBlur,
    handleFieldUnBlur,
    hideErrorMessage,
    ...inputProps
  } = props;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [secureTextEntry, setSecureTextEntry] = React.useState(isPassword);
  const inputRef = React.useRef(null);

  // Single shared value for focus/fill state
  const focusState = useSharedValue(0);

  // Use a ref to track the last value to prevent useEffect from firing
  // when the value changes, which would cause an animation hiccup
  const previousValueRef = React.useRef(value);

  // Animate on initial load if there's a value
  React.useEffect(() => {
    if (value && !previousValueRef.current) {
      focusState.value = withTiming(1, { duration: 150 });
    }
    previousValueRef.current = value;
  }, [value, focusState]);

  // Handle focus with direct shared value update
  const onFocus = React.useCallback(() => {
    focusState.value = withTiming(1, { duration: 150 });
    handleFieldUnBlur?.();
  }, [focusState, handleFieldUnBlur]);

  // Handle blur with direct shared value update
  const onInputBlur = React.useCallback(
    (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      if (!value) {
        focusState.value = withTiming(0, { duration: 150 });
      }
      handleFieldBlur?.();
      onBlur?.(e);
    },
    [focusState, value, handleFieldBlur, onBlur]
  );

  const animatedLabelStyle = useAnimatedStyle(() => {
    const isActive = focusState.value === 1 || !!value;
    const translateY = withTiming(isActive ? -10 : 0, {
      duration: 150,
    });
    const scale = withTiming(isActive ? 0.9 : 1, { duration: 150 });
    const color = interpolateColor(
      focusState.value,
      [0, 1],
      [
        error ? colors.red['60'] : isDark ? colors.grey[60] : colors.grey[50],
        error ? colors.red['60'] : colors.brand['60'],
      ]
    );

    return {
      transform: [{ translateY }, { scale }],
      color,
    };
  }, [focusState, value, isDark, error]);

  React.useImperativeHandle(ref as any, () => ({
    focus: () => {
      (inputRef.current as any)?.focus();
    },
    blur: () => {
      (inputRef.current as any)?.blur();
    },
  }));

  const styles = React.useMemo(
    () =>
      inputTv({
        error: Boolean(error),
        focused: focusState.value === 1,
        filled: !!value && focusState.value === 0,
        disabled: Boolean(props.disabled),
      }),
    [error, focusState.value, props.disabled, value]
  );

  const togglePassword = () => {
    setSecureTextEntry(!secureTextEntry);
    setTimeout(() => (inputRef.current as any)?.focus(), 100);
  };

  return (
    <View className={styles.container({ className: containerClassName })}>
      {label && (
        <AnimatedView
          style={[
            {
              position: 'absolute',
              left: 16,
              top: 14,
            },
            animatedLabelStyle,
          ]}
        >
          <Text
            testID={testID ? `${testID}-label` : undefined}
            className={styles.label()}
          >
            {label}
          </Text>
        </AnimatedView>
      )}
      <NTextInput
        testID={testID}
        ref={inputRef}
        placeholderTextColor={isDark ? colors.grey[60] : colors.grey[50]}
        className={styles.input({
          className: cn(
            inputClassName,
            {
              'shadow-sm shadow-accent-moderate dark:shadow-accent-moderate':
                focusState.value === 1 && !error,
              'shadow-none': Platform.OS === 'android',
              'pl-4': !icon,
            },
            isPassword ? 'pr-12' : '',
            focusState.value === 1 && !error ? focusedInputClassName : ''
          ),
        })}
        onBlur={onInputBlur}
        onFocus={onFocus}
        secureTextEntry={isPassword && secureTextEntry}
        {...inputProps}
        value={value}
        style={StyleSheet.flatten([
          { writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr' },
          { textAlign: I18nManager.isRTL ? 'right' : 'left' },
          inputProps.style,
        ])}
      />
      {icon ? (
        <View className={styles.iconContainer({ className: iconClassName })}>
          <IconWrapper
            icon={icon}
            isFocused={focusState.value === 1}
            isFilled={!!value}
            hasError={Boolean(error)}
          />
        </View>
      ) : null}
      {isPassword && (
        <TouchableOpacity
          className="absolute right-3 top-3.5"
          onPress={togglePassword}
        >
          <IconWrapper
            // @ts-ignore
            icon={
              <Feather name={secureTextEntry ? 'eye-off' : 'eye'} size={24} />
            }
            isFocused={focusState.value === 1}
            isFilled={!!value}
            hasError={Boolean(error)}
          />
        </TouchableOpacity>
      )}
      {error && !hideErrorMessage && (
        <Text
          testID={testID ? `${testID}-error` : undefined}
          className="text-sm text-red-60 dark:text-red-80"
        >
          {error}
        </Text>
      )}
    </View>
  );
});

export function ControlledInput<T extends FieldValues>(
  props: ControlledInputProps<T>
) {
  const { name, rules, ...inputProps } = props;
  const { control } = useFormContext();

  const { field, fieldState } = useController({
    control,
    name,
    rules,
  });
  return (
    <Input
      ref={field.ref}
      autoCapitalize="none"
      onChangeText={field.onChange}
      onBlur={field.onBlur}
      value={field.value ? String(field.value) : ''}
      {...inputProps}
      error={fieldState.error?.message}
    />
  );
}

export function ControlledInputWithoutContext<T extends FieldValues>(
  props: ControlledInputProps<T>
) {
  const { name, control, rules, ...inputProps } = props;

  const { field, fieldState } = useController({ control, name, rules });
  return (
    <Input
      ref={field.ref}
      autoCapitalize="none"
      onChangeText={field.onChange}
      value={(field.value as string) || ''}
      {...inputProps}
      error={fieldState.error?.message}
    />
  );
}
