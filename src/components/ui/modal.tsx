/**
 * Modal
 * Dependencies:
 * - @gorhom/bottom-sheet.
 *
 * Props:
 * - All `BottomSheetModalProps` props.
 * - `title` (string | undefined): Optional title for the modal header.
 *
 * Usage Example:
 * import { Modal, useModal } from '@gorhom/bottom-sheet';
 *
 * function DisplayModal() {
 *   const { ref, present, dismiss } = useModal();
 *
 *   return (
 *     <View>
 *       <Modal
 *         snapPoints={['60%']} // optional
 *         title="Modal Title"
 *         ref={ref}
 *       >
 *         Modal Content
 *       </Modal>
 *     </View>
 *   );
 * }
 *
 */

import type {
  BottomSheetBackdropProps,
  BottomSheetModalProps,
} from '@gorhom/bottom-sheet';
import { BottomSheetModal, useBottomSheet } from '@gorhom/bottom-sheet';
import { useColorScheme } from 'nativewind';
import * as React from 'react';
import { Keyboard, Pressable, View } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Path, Svg } from 'react-native-svg';

import { cn } from '@/lib';

import semanticColors from './semantic-colors';
import { Text } from './text';

type ModalProps = BottomSheetModalProps & {
  title?: string;
  hasHandle?: boolean;
  handleClassName?: string;
  hasCloseButton?: boolean;
};

type ModalRef = React.ForwardedRef<BottomSheetModal>;

type ModalHeaderProps = {
  title?: string;
  hasCloseButton?: boolean;
  dismiss: () => void;
};

export const useModal = () => {
  const ref = React.useRef<BottomSheetModal>(null);

  const present = React.useCallback((data?: any) => {
    Keyboard.dismiss();
    ref.current?.present(data);
  }, []);

  const dismiss = React.useCallback(() => {
    ref.current?.dismiss();
  }, []);

  const snapToIndex = React.useCallback((index: number) => {
    ref.current?.snapToIndex(index);
  }, []);

  return React.useMemo(
    () => ({ ref, present, dismiss, snapToIndex }),
    [present, dismiss, snapToIndex]
  );
};

export const Modal = React.forwardRef(
  (
    {
      snapPoints: _snapPoints,
      title,
      detached = false,
      enableDynamicSizing,
      hasHandle = true,
      handleClassName,
      hasCloseButton = false,
      ...props
    }: ModalProps,
    ref: ModalRef
  ) => {
    const detachedProps = React.useMemo(
      () => getDetachedProps(detached),
      [detached]
    );
    const modal = useModal();
    const snapPoints = React.useMemo(() => _snapPoints, [_snapPoints]);

    React.useImperativeHandle(
      ref,
      () => (modal.ref.current as BottomSheetModal) || null
    );

    const renderHandleComponent = React.useCallback(
      () => (
        <>
          <View
            className={cn(
              'mb-4 mt-3 h-[5px] w-12 self-center rounded-full bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark',
              handleClassName
            )}
          />
          <ModalHeader title={title} dismiss={modal.dismiss} />
          {hasCloseButton && <CloseButton close={modal.dismiss} />}
        </>
      ),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [title, modal.dismiss]
    );

    const { colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';

    const backgroundStyle = React.useMemo(
      () => ({
        backgroundColor: isDark
          ? semanticColors.bg.canvas.dark
          : semanticColors.bg.canvas.light,
      }),
      [isDark]
    );

    return (
      <BottomSheetModal
        {...props}
        {...detachedProps}
        ref={modal.ref}
        index={props.index ?? 0}
        snapPoints={snapPoints}
        backdropComponent={props.backdropComponent || renderBackdrop}
        enableDynamicSizing={enableDynamicSizing ? true : false}
        handleComponent={hasHandle ? renderHandleComponent : null}
        backgroundStyle={props.backgroundStyle || backgroundStyle}
      />
    );
  }
);

/**
 * Custom Backdrop
 */

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const CustomBackdrop = ({ style }: BottomSheetBackdropProps) => {
  const { close } = useBottomSheet();
  return (
    <AnimatedPressable
      onPress={() => close()}
      entering={FadeIn.duration(50)}
      exiting={FadeOut.duration(20)}
      style={[style, { backgroundColor: 'rgba(0, 0, 0, 0.4)' }]}
    />
  );
};

export const renderBackdrop = (props: BottomSheetBackdropProps) => (
  <CustomBackdrop {...props} />
);

/**
 *
 * @param detached
 * @returns
 *
 * @description
 * In case the modal is detached, we need to add some extra props to the modal to make it look like a detached modal.
 */

const getDetachedProps = (detached: boolean) => {
  if (detached) {
    return {
      detached: true,
      bottomInset: 46,
      style: { marginHorizontal: 16, overflow: 'hidden' },
    } as Partial<BottomSheetModalProps>;
  }
  return {} as Partial<BottomSheetModalProps>;
};

/**
 * ModalHeader
 */

const ModalHeader = React.memo(
  ({ title, dismiss, hasCloseButton }: ModalHeaderProps) => {
    return (
      <>
        {title && (
          <View className="flex-row px-2 py-4">
            <View className="size-6" />
            <View className="flex-1">
              <Text className="text-center text-lg font-bold text-[#26313D] dark:text-white">
                {title}
              </Text>
            </View>
          </View>
        )}
        {hasCloseButton && <CloseButton close={dismiss} />}
      </>
    );
  }
);

const CloseButton = ({ close }: { close: () => void }) => {
  return (
    <Pressable
      onPress={close}
      className="absolute -top-12 right-0 size-8 items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark "
      hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
      accessibilityLabel="close modal"
      accessibilityRole="button"
      accessibilityHint="closes the modal"
    >
      <Svg width={16} height={16} fill="none" viewBox="0 0 16 16">
        <Path
          d="M4 4L8 8M8 8L12 12M8 8L12 4M8 8L4 12"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </Svg>
    </Pressable>
  );
};
