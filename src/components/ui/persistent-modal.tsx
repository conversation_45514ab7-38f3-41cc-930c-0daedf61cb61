/* eslint-disable import/no-cycle */
import { type BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { useColorScheme } from 'nativewind';
import React, { forwardRef, type ReactNode } from 'react';
import { type ColorValue } from 'react-native';

import { colors, Modal, Pressable } from '@/components/ui';

interface PersistentModalProps {
  children: ReactNode;
  enablePanDown?: boolean;
  backgroundColor?: ColorValue | undefined;
  index?: number;
  handleClassName?: string;
  onBackdropPress?: () => void;
}

export const PersistentModal = forwardRef<
  BottomSheetModalMethods,
  PersistentModalProps
>(
  (
    {
      children,
      enablePanDown = false,
      backgroundColor,
      index = 0,
      handleClassName,
      onBackdropPress,
    },
    ref
  ) => {
    const { colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';

    return (
      <Modal
        ref={ref}
        index={index}
        enableDynamicSizing
        backgroundStyle={{
          backgroundColor: backgroundColor
            ? backgroundColor
            : isDark
              ? colors.grey[100]
              : colors.white,
        }}
        enablePanDownToClose={enablePanDown}
        backdropComponent={() => <Pressable onPress={onBackdropPress} />}
        handleClassName={handleClassName}
      >
        {children}
      </Modal>
    );
  }
);

PersistentModal.displayName = 'PersistentModal';
