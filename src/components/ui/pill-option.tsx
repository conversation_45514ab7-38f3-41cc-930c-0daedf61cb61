import React from 'react';

import { SmRegularLabel, TouchableOpacity } from '@/components/ui';
import { cn } from '@/lib';

type PillOptionProps = {
  option: string;
  className?: string;
  textClassName?: string;
  selected?: boolean;
  onPress?: (option: string) => void;
};

export const PillOption: React.FC<PillOptionProps> = ({
  option,
  className,
  textClassName,
  selected,
  onPress,
}) => (
  <TouchableOpacity
    className={cn(
      'rounded-full border border-accent-muted-light dark:border-accent-muted-dark py-3 px-5',
      className,
      selected &&
        'bg-brand-60 dark:bg-brand-60 border-brand-60 dark:border-brand-60'
    )}
    onPress={() => onPress?.(option)}
    disabled={!onPress}
  >
    <SmRegularLabel className={textClassName}>{option}</SmRegularLabel>
  </TouchableOpacity>
);
