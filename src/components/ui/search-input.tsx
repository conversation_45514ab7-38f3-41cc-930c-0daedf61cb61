import Ionicons from '@expo/vector-icons/Ionicons';
import { useColorScheme } from 'nativewind';
import * as React from 'react';
import type { TextInputProps } from 'react-native';

import { cn } from '@/lib';

import colors from './colors';
import { Input } from './input';

export interface SearchInputProps extends Omit<TextInputProps, 'placeholder'> {
  /**
   * The placeholder text for the search input
   */
  placeholder?: string;
  /**
   * The current search query value
   */
  value: string;
  /**
   * Callback when search text changes
   */
  onChangeText: (text: string) => void;
  /**
   * Additional class name for the icon container
   */
  iconClassName?: string;
  /**
   * Additional class name for the input
   */
  inputClassName?: string;
  /**
   * Additional class name for the placeholder input
   */
  placeholderClassName?: string;
  /**
   * Additional class name for the focused input state
   */
  focusedInputClassName?: string;
  /**
   * Callback when search is submitted
   */
  onSubmit?: () => void;
  /**
   * Callback when clear button is pressed
   */
  onClear?: () => void;
  /**
   * Whether to show the clear button
   */
  showClearButton?: boolean;
}

/**
 * A reusable search input component
 */
export const SearchInput = React.forwardRef<
  React.ComponentRef<typeof Input>,
  SearchInputProps
>((props, ref) => {
  const {
    placeholder,
    value,
    onChangeText,
    iconClassName,
    inputClassName,
    placeholderClassName,
    focusedInputClassName,
    showClearButton = true,
    ...rest
  } = props;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Input
      ref={ref}
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      // @ts-ignore
      icon={
        <Ionicons
          name="search"
          size={18}
          color={isDark ? colors.white : colors.grey[100]}
        />
      }
      iconClassName={cn('top-4 left-4', iconClassName)}
      inputClassName={cn(
        'h-12 rounded-xl py-2 pl-[52px] bg-bg-subtle-light dark:bg-bg-subtle-dark text-fg-subtle-light dark:text-white text-base/100 border-0',
        inputClassName
      )}
      placeholderClassName={placeholderClassName}
      focusedInputClassName={cn('border', focusedInputClassName)}
      returnKeyType="search"
      clearButtonMode={showClearButton ? 'while-editing' : 'never'}
      {...rest}
    />
  );
});

SearchInput.displayName = 'SearchInput';
