import { Ionicons } from '@expo/vector-icons';
import { type BottomSheetModal } from '@gorhom/bottom-sheet';
import { FlashList } from '@shopify/flash-list';
import { useColorScheme } from 'nativewind';
import * as React from 'react';
import type { FieldValues } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { Platform } from 'react-native';
import { Pressable, type PressableProps } from 'react-native';
import { Dimensions } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { tv } from 'tailwind-variants';

import { List as CustomList, semanticColors, View } from '@/components/ui';
import colors from '@/components/ui/colors';

import { Button } from './button';
import { RadioIcon } from './checkbox';
import type { InputControllerType } from './input';
import { useModal } from './modal';
import { Modal } from './modal';
import { Text } from './text';

const selectTv = tv({
  slots: {
    container: 'mb-2',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark',
    input:
      'mt-0 h-[52px] flex-row items-center justify-center gap-2 rounded-md border border-border-subtle-light bg-transparent p-3 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    inputValue: 'dark:text-neutral-100',
  },

  variants: {
    focused: {
      true: {
        input:
          'border-2 border-accent-moderate pb-2 pt-5 dark:border-accent-moderate',
      },
    },
    error: {
      true: {
        input: 'border-danger-600',
        label: 'text-danger-600 dark:text-danger-600',
        inputValue: 'text-danger-600',
      },
    },
    disabled: {
      true: {
        input: 'bg-neutral-200',
      },
    },
  },
  defaultVariants: {
    error: false,
    disabled: false,
  },
});

const List = Platform.OS === 'web' ? FlashList : CustomList;

export type OptionType = { label: string; value: string | number };

type OptionsProps = {
  options: OptionType[];
  onSelect: (option: OptionType) => void;
  onDismiss?: () => void;
  value?: string | number;
  testID?: string;
  title?: string;
  loading?: boolean;
};

function keyExtractor(item: OptionType) {
  return `select-item-${item.value}`;
}

export const Options = React.forwardRef<BottomSheetModal, OptionsProps>(
  ({ options, onSelect, onDismiss, value, testID, title, loading }, ref) => {
    const { height: screenHeight } = Dimensions.get('window');
    const calculatedHeight = options.length * 70 + 160;
    const maxHeight = screenHeight * 0.9;
    const finalHeight = Math.min(calculatedHeight, maxHeight);
    const snapPoints = React.useMemo(() => [finalHeight], [finalHeight]);
    const { colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const iconColor = isDark
      ? semanticColors.fg.subtle.dark
      : semanticColors.fg.subtle.light;

    // Initialize with the current value (including default values)
    const getInitialSelectedOption = React.useCallback(() => {
      if (value !== undefined) {
        return options.find((opt) => opt.value === value) || null;
      }
      return null;
    }, [value, options]);

    // Track the selected option in local state
    const [selectedOption, setSelectedOption] =
      React.useState<OptionType | null>(getInitialSelectedOption);

    // Update selected option when value prop changes or when modal opens
    React.useEffect(() => {
      const initialOption = getInitialSelectedOption();
      setSelectedOption(initialOption);
    }, [getInitialSelectedOption]);

    const resetToCurrentValue = React.useCallback(() => {
      const currentOption = getInitialSelectedOption();
      setSelectedOption(currentOption);
    }, [getInitialSelectedOption]);

    // Update selected option when value prop changes
    React.useEffect(() => {
      if (value !== undefined) {
        const option = options.find((opt) => opt.value === value);
        if (option) setSelectedOption(option);
      }
    }, [value, options]);

    const handleOptionPress = React.useCallback((option: OptionType) => {
      // Force immediate state update
      setSelectedOption((prevState) => {
        // Only update if it's actually different
        if (prevState?.value !== option.value) {
          return option;
        }
        return prevState;
      });
    }, []);

    const handleConfirm = React.useCallback(() => {
      if (selectedOption) {
        onSelect(selectedOption);
      }
    }, [selectedOption, onSelect]);

    const renderSelectItem = React.useCallback(
      ({ item }: { item: OptionType }) => {
        const isSelected = selectedOption?.value === item.value;
        return (
          <Option
            key={`select-item-${item.value}`}
            label={item.label}
            selected={isSelected}
            onPress={() => handleOptionPress(item)}
            testID={testID ? `${testID}-item-${item.value}` : undefined}
          />
        );
      },
      [handleOptionPress, selectedOption?.value, testID]
    );

    return (
      <Modal
        ref={ref}
        index={0}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: isDark ? colors.grey[80] : colors.white,
        }}
        onAnimate={(_, toIndex) => {
          // Reset selection to current value when modal opens (index 0)
          if (toIndex === 0) {
            resetToCurrentValue();
          }
        }}
      >
        <View className="flex-1 gap-4 p-4">
          <View className="flex-row items-center justify-start gap-2">
            <TouchableOpacity
              onPress={onDismiss}
              className=""
              accessibilityLabel="Close select modal"
              accessibilityRole="button"
            >
              <Ionicons name="close" size={32} color={iconColor} />
            </TouchableOpacity>
            <Text className="text-lg font-bold dark:text-neutral-100">
              {title}
            </Text>
          </View>
          <List
            data={options}
            renderScrollComponent={ScrollView}
            showsVerticalScrollIndicator={false}
            keyExtractor={keyExtractor}
            renderItem={renderSelectItem}
            testID={testID ? `${testID}-modal` : undefined}
            estimatedItemSize={400}
            // Add extraData to force re-render when selection changes
            extraData={selectedOption?.value}
          />
          <View className="mt-auto pb-4">
            <Button
              label="Select"
              disabled={!selectedOption || loading}
              loading={loading}
              onPress={handleConfirm}
              testID={testID ? `${testID}-confirm` : undefined}
            />
          </View>
        </View>
      </Modal>
    );
  }
);

const Option = React.memo(
  ({
    label,
    selected = false,
    ...props
  }: PressableProps & {
    selected?: boolean;
    label: string;
  }) => {
    return (
      <Pressable
        className="flex-row items-center justify-between py-6"
        {...props}
      >
        <Text className="flex-1 dark:text-neutral-100">{label}</Text>
        {selected ? (
          <RadioIcon checked={true} />
        ) : (
          <RadioIcon checked={false} />
        )}
      </Pressable>
    );
  }
);

export interface SelectProps {
  value?: string | number;
  label?: string;
  disabled?: boolean;
  error?: string;
  options?: OptionType[];
  onSelect?: (value: string | number) => void;
  placeholder?: string;
  testID?: string;
  title?: string; // Add this new prop
}
interface ControlledSelectProps<T extends FieldValues>
  extends SelectProps,
    InputControllerType<T> {}

export const Select = (props: SelectProps) => {
  const {
    label,
    value,
    error,
    options = [],
    placeholder = 'select...',
    disabled = false,
    onSelect,
    testID,
    title = 'Choose an option',
  } = props;
  const modal = useModal();

  const onSelectOption = React.useCallback(
    (option: OptionType) => {
      onSelect?.(option.value);
      modal.dismiss();
    },
    [modal, onSelect]
  );

  // const onConfirmSelectOption = React.useCallback(
  //   (option: OptionType) => {
  //     onSelect?.(option.value);
  //     modal.dismiss();
  //   },
  //   [modal, onSelect]
  // );

  const styles = React.useMemo(
    () =>
      selectTv({
        error: Boolean(error),
        disabled,
      }),
    [error, disabled]
  );

  const textValue = React.useMemo(
    () =>
      value !== undefined
        ? (options?.filter((t) => t.value === value)?.[0]?.label ?? placeholder)
        : placeholder,
    [value, options, placeholder]
  );

  return (
    <>
      <View className={styles.container()}>
        {label && (
          <Text
            testID={testID ? `${testID}-label` : undefined}
            className={styles.label()}
          >
            {label}
          </Text>
        )}
        <Pressable
          className={styles.input()}
          disabled={disabled}
          onPress={modal.present}
          testID={testID ? `${testID}-trigger` : undefined}
        >
          <View className="flex-1">
            <Text className={styles.inputValue()} numberOfLines={1}>
              {textValue}
            </Text>
          </View>
          <Ionicons name="chevron-down" size={24} color={colors.grey[70]} />
        </Pressable>
        {error && (
          <Text
            testID={`${testID}-error`}
            className="text-danger-300 dark:text-danger-600 text-sm"
          >
            {error}
          </Text>
        )}
      </View>
      <Options
        testID={testID}
        ref={modal.ref}
        options={options}
        onSelect={onSelectOption}
        onDismiss={modal.dismiss}
        title={title} // Pass the title prop
        value={value}
      />
    </>
  );
};

// only used with react-hook-form
export function ControlledSelect<T extends FieldValues>(
  props: ControlledSelectProps<T>
) {
  const { name, control, rules, onSelect: onNSelect, ...selectProps } = props;

  const { field, fieldState } = useController({ control, name, rules });
  const onSelect = React.useCallback(
    (value: string | number) => {
      field.onChange(value);
      onNSelect?.(value);
    },
    [field, onNSelect]
  );
  return (
    <Select
      onSelect={onSelect}
      value={field.value}
      error={fieldState.error?.message}
      {...selectProps}
    />
  );
}
