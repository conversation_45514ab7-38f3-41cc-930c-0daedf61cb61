// src/components/ui/semanticColors.js
const colors = require('./colors');

module.exports = {
  fg: {
    base: { light: colors.grey[100], dark: colors.white },
    muted: { light: colors.grey[60], dark: colors.grey[50] },
    subtle: { light: colors.grey[50], dark: colors.grey[60] },
    link: colors.brand[60],
    disabled: { light: colors.grey[50], dark: colors.grey[60] },
    'on-contrast': { light: colors.white, dark: colors.grey[100] },
    'static-dark': colors.grey[100],
    'static-light': colors.white,
    danger: { light: colors.red[60], dark: colors.red[40] },
    success: { light: colors.green[60], dark: colors.green[40] },
    warning: { light: colors.yellow[60], dark: colors.yellow[30] },
    error: { light: colors.red[60], dark: colors.red[40] },
    info: { light: colors.blue[60], dark: colors.blue[40] },
  },
  bg: {
    canvas: { light: colors.white, dark: colors.grey[100] },
    subtle: { light: colors.grey[10], dark: colors.grey[90] },
    muted: { light: colors.grey[20], dark: colors.grey[80] },
    contrast: { light: colors.grey[100], dark: colors.white },
    surface: { light: colors.white, dark: colors.grey[80] },
    'interactive-primary': { light: colors.grey[20], dark: colors.grey[80] },
    'interactive-secondary': { light: colors.grey[30], dark: colors.grey[70] },
    'interactive-tertiary': { light: colors.grey[40], dark: colors.grey[60] },
    success: { light: colors.green[10], dark: colors.green[80] },
    'success-contrast': { light: colors.green[60], dark: colors.green[50] },
    error: { light: colors.red[10], dark: colors.red[80] },
    'error-contrast': { light: colors.red[60], dark: colors.grey[50] },
    warning: { light: colors.yellow[10], dark: colors.yellow[80] },
    'warning-contrast': colors.yellow[30],
    info: { light: colors.blue[10], dark: colors.blue[30] },
    'info-contrast': colors.blue[60],
    overlay: { light: 'rgba(0, 0, 0, 0.45)', dark: 'rgba(0, 0, 0, 0.70)' },
    notification: colors.red[60],
    disabled: { light: colors.grey[30], dark: colors.grey[80] },
    'danger-primary': colors.red[50],
    'danger-secondary': colors.red[60],
    'danger-tertiary': colors.red[70],
  },
  accent: {
    'on-accent': colors.white,
    subtle: { light: colors.brand[20], dark: colors.brand[90] },
    muted: { light: colors.brand[30], dark: colors.brand[80] },
    dim: { light: colors.brand[40], dark: colors.brand[70] },
    moderate: colors.brand[60],
    bold: { light: colors.brand[70], dark: colors.brand[40] },
    strong: { light: colors.brand[80], dark: colors.brand[30] },
    intense: { light: colors.brand[90], dark: colors.brand[20] },
  },
  border: {
    subtle: { light: colors.grey[20], dark: colors.grey[80] },
    muted: { light: colors.grey[30], dark: colors.grey[70] },
    'interactive-primary': { light: colors.grey[30], dark: colors.grey[70] },
    contrast: { light: colors.grey[100], dark: colors.white },
    disabled: { light: colors.grey[40], dark: colors.grey[60] },
    error: { light: colors.red[40], dark: colors.red[70] },
  },
  social: {
    google: {
      primary: '#F4F6F7',
      secondary: '#E8EBEB',
      tertiary: '#DADDDE',
    },
    twitter: {
      primary: '#1DA1F2',
      secondary: '#0C90E1',
      tertiary: '#0B84CF',
    },
    facebook: {
      primary: '#0078FF',
      secondary: '#0067DB',
      tertiary: '#0056B8',
    },
    apple: {
      primary: { light: colors.grey[100], dark: colors.white },
      secondary: { light: colors.grey[90], dark: colors.grey[10] },
      tertiary: { light: '#2F3133', dark: colors.grey[20] },
    },
  },
};
