import { LinearGradient } from 'expo-linear-gradient';
import * as React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

import { cn } from '@/lib';

const { width } = Dimensions.get('window');
const shimmerWidth = 0.7 * width;
const duration = 800;

function Skeleton({
  className,
  ...props
}: Omit<React.ComponentPropsWithoutRef<typeof View>, 'style'>) {
  const translateX = useSharedValue(-shimmerWidth);

  React.useEffect(() => {
    translateX.value = withRepeat(withTiming(width, { duration }), -1, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const shimmerStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  return (
    <View
      className={cn(
        'overflow-hidden rounded-md bg-grey-10 dark:bg-grey-90',
        className
      )}
      {...props}
    >
      <Animated.View style={[StyleSheet.absoluteFill, shimmerStyle]}>
        <LinearGradient
          colors={['transparent', 'rgba(255,255,255,0.05)', 'transparent']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={{ width: shimmerWidth, height: '100%' }}
        />
      </Animated.View>
    </View>
  );
}

export { Skeleton };
