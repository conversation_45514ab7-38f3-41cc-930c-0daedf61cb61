import * as React from 'react';

import { SmBoldLabel, View } from '@/components/ui';
import { cn, formatEnumLabel } from '@/lib';
import { type TransactionStatus } from '@/types';

type StatusPillProps = {
  status?: TransactionStatus; // ??EXTEND STATUS OPTIONS TO BE MORE ENCOMPASSING
};

export const StatusPill = ({ status }: StatusPillProps) => {
  return (
    <View
      className={cn(
        'h-[30px] rounded-full px-4 items-center justify-center',
        status === 'PENDING' &&
          'bg-bg-warning-contrast dark:bg-bg-warning-contrast',
        status === 'FAILED' &&
          'bg-bg-error-contrast-light dark:bg-bg-error-contrast-light',
        status === 'SUCCESS' &&
          'bg-bg-success-contrast-light dark:bg-bg-success-contrast-dark'
      )}
    >
      <SmBoldLabel className="text-white dark:text-white">
        {formatEnumLabel(status || '')}
      </SmBoldLabel>
    </View>
  );
};
