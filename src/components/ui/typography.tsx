// src/components/ui/typography.tsx
import React from 'react';
import { type TextProps } from 'react-native';

import { cn } from '@/lib';

import { Text } from './text';

// Base text variants
interface TypographyProps extends TextProps {
  children: React.ReactNode;
  className?: string;
}

const fontWeightMap = {
  light: 'font-aeonik-light font-light',
  regular: 'font-aeonik-regular',
  medium: 'font-aeonik-medium font-medium',
  semibold: 'font-aeonik-bold font-semibold',
  bold: 'font-aeonik-bold font-bold',
};

const fontSizeMap = {
  xs: 'text-xs', // 12px
  sm: 'text-sm', // 14px
  md: 'text-base', // 16px
  lg: 'text-lg', // 18px
  xl: 'text-xl', // 24px
  '2xl': 'text-2xl', // 32px
  '3xl': 'text-3xl', // 40px
  '4xl': 'text-4xl', // 64px
};

// Text component with size and weight props
export function Typography({
  children,
  className,
  size = 'md',
  weight = 'regular',
  ...props
}: TypographyProps & {
  size?: keyof typeof fontSizeMap;
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Text
      className={cn(
        fontSizeMap[size],
        fontWeightMap[weight],
        'text-grey-100 dark:text-white font-aeonik-regular',
        className
      )}
      {...props}
    >
      {children}
    </Text>
  );
}

// Display variants
export function D1({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-4xl/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function D2({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-3xl/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

// Heading variants
export function H1({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-2xl/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function H2({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-xl/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function H3({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-lg/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function H4({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-base/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function H5({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-sm/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

export function H6({
  children,
  className,
  weight = 'bold',
  ...props
}: TypographyProps & {
  weight?: keyof typeof fontWeightMap;
}) {
  return (
    <Typography
      className={cn('text-xs/120 font-aeonik-black', className)}
      weight={weight}
      {...props}
    >
      {children}
    </Typography>
  );
}

// Paragraph variants
export function P({ children, className, ...props }: TypographyProps) {
  return (
    <Typography className={cn('text-base/150', className)} {...props}>
      {children}
    </Typography>
  );
}

// Large text variant
export function Large({ children, className, ...props }: TypographyProps) {
  return (
    <Typography className={cn('text-lg/150', className)} {...props}>
      {children}
    </Typography>
  );
}

// Medium text variant
export function Medium({ children, className, ...props }: TypographyProps) {
  return (
    <Typography className={cn('text-base/150', className)} {...props}>
      {children}
    </Typography>
  );
}

// Small text variant
export function Small({ children, className, ...props }: TypographyProps) {
  return (
    <Typography className={cn('text-sm/150', className)} {...props}>
      {children}
    </Typography>
  );
}

// Tiny text variant
export function Tiny({ children, className, ...props }: TypographyProps) {
  return (
    <Typography className={cn('text-xs/150', className)} {...props}>
      {children}
    </Typography>
  );
}

// Large Bold Label
export function LgBoldLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-lg/150', className)}
      weight="bold"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Large Regular Label
export function LgRegularLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-lg/150 font-aeonik-regular', className)}
      weight="regular"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Medium Bold Label
export function MdBoldLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-base/150 font-aeonik-black', className)}
      weight="bold"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Medium Regular Label
export function MdRegularLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-base/150 font-aeonik-regular', className)}
      weight="regular"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Small Bold Label
export function SmBoldLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-sm/150 font-aeonik-black', className)}
      weight="bold"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Small Regular Label
export function SmRegularLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-sm/150 font-aeonik-regular', className)}
      weight="regular"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Extra Small Bold Label
export function XsBoldLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-xs/150 font-aeonik-black', className)}
      weight="bold"
      {...props}
    >
      {children}
    </Typography>
  );
}

// Extra Small Regular Label
export function XsRegularLabel({
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Typography
      className={cn('text-xs/150 font-aeonik-regular', className)}
      weight="regular"
      {...props}
    >
      {children}
    </Typography>
  );
}
