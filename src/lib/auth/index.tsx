import { jwtDecode } from 'jwt-decode';
import { create } from 'zustand';

import { createOrUpdateFirebaseUser } from '@/api';
import { type UserObjectData } from '@/api/auth/types';
import { type DecodedTokenType } from '@/types';

import { createSelectors } from '../utils';
import {
  getToken,
  getUser,
  removeToken,
  removeUser,
  setToken,
  setUser,
} from './utils';

interface AuthState {
  token: string | null;
  user: UserObjectData | null;
  status: 'idle' | 'signedOut' | 'signedIn';
  signIn: (token: string, user: UserObjectData) => void;
  setUser: (user: UserObjectData) => void;
  signOut: () => void;
  hydrate: () => void;
  fetchCurrentLoggedInUser: () => void;
}

const _useAuth = create<AuthState>((set, get) => ({
  status: 'idle',
  token: null,
  user: null,
  signIn: async (token, user) => {
    setToken(token);
    setUser(user);
    set({ status: 'signedIn', token, user });
    await createOrUpdateFirebaseUser(user, 'online');
  },
  setUser: (user) => {
    const authUser = get().user;
    const updatedUser = {
      ...authUser,
      ...user,
    };
    setUser(updatedUser);
    set({
      user: updatedUser,
    });
  },
  signOut: async () => {
    const authUser = get().user;
    if (authUser) await createOrUpdateFirebaseUser(authUser, 'offline');
    removeToken();
    removeUser();
    set({ status: 'signedOut', token: null, user: null });
  },
  hydrate: async () => {
    console.log('🚀 ~ hydrate: ~ call hydrate');
    try {
      const userToken = getToken();
      const authUser = getUser();
      if (userToken !== null && authUser !== null) {
        await createOrUpdateFirebaseUser(authUser, 'online');

        get().signIn(userToken, authUser);
      } else {
        get().signOut();
      }
    } catch (e) {
      // catch error here
      // Maybe sign_out user!
    }
  },
  fetchCurrentLoggedInUser: async () => {
    try {
      const userToken = getToken();
      if (userToken) {
        const decoded: DecodedTokenType = jwtDecode(userToken);
        get().signIn(userToken, decoded as unknown as UserObjectData);
      } else {
        get().signOut();
      }
    } catch (error) {}
  },
}));

export const useAuth = createSelectors(_useAuth);

export const signOut = () => _useAuth.getState().signOut();
export const signIn = (token: string, user: UserObjectData) =>
  _useAuth.getState().signIn(token, user);
export const hydrateAuth = () => _useAuth.getState().hydrate();
