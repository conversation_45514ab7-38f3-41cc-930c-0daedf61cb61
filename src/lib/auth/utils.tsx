import { type UserObjectData } from '@/api/auth/types';
import { getItem, removeItem, setItem } from '@/lib/storage';

const TOKEN = 'token';
const AUTH_USER = 'auth-user';
const PROFILE_IMAGE_KEY = 'profile-image';

export const getToken = () => getItem<string>(TOKEN);
export const removeToken = () => removeItem(TOKEN);
export const setToken = (value: string) => setItem<string>(TOKEN, value);

export const getUser = () => getItem<UserObjectData>(AUTH_USER);
export const removeUser = () => removeItem(AUTH_USER);
export const setUser = (value: UserObjectData) =>
  setItem<UserObjectData>(AUTH_USER, value);

export const getProfileImageKey = () => getItem<number>(PROFILE_IMAGE_KEY);
export const removeProfileImageKey = () => removeItem(PROFILE_IMAGE_KEY);
export const setProfileImageKey = (value: number) =>
  setItem<number>(PROFILE_IMAGE_KEY, value);
