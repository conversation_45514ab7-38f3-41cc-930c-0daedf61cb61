import { Env } from '@env';
import { Platform } from 'react-native';
import {
  type GooglePlacesAutocompleteProps,
  type SearchType,
} from 'react-native-google-places-autocomplete';

import { USER_ROLE_VALUES } from '@/api/auth';
import type { ProfileItem, ProfileSections, ReportType } from '@/types';

import type { ColorSchemeType } from './hooks';

export const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const SUPPORTED_COUNTRIES = [
  {
    label: 'Nigeria',
    value: 'NIGERIA',
    code: 'ng',
    coordinates: { latitude: 9.5132431, longitude: 8.0894561 },
  },
  {
    label: 'United Kingdom',
    value: 'UNITED KINGDOM',
    code: 'gb',
    coordinates: { latitude: 55.3781, longitude: -3.436 },
  },
];
export const defaultLocationObject = {
  coordinates: {
    lat: 0,
    lng: 0,
    latitude: 0,
    longitude: 0,
  },
  city: 'N/A',
  state: 'N/A',
  street: 'N/A',
  country: 'N/A',
  address: 'N/A',
  landmark: 'N/A',
};

export const DEFAULT_COORDINATES = SUPPORTED_COUNTRIES.reduce<
  Record<string, { latitude: number; longitude: number }>
>((acc, country) => {
  if (country.coordinates) {
    acc[country.value] = country.coordinates;
  }
  return acc;
}, {});

export const GENRES = [
  {
    id: 'afrobeats',
    name: 'Afrobeats',
    image: require('~/assets/images/genres/afrobeats.png'),
  },
  {
    id: 'hiphop',
    name: 'HipHop',
    image: require('~/assets/images/genres/hiphop.png'),
  },
  {
    id: 'rave',
    name: 'Rave',
    image: require('~/assets/images/genres/rave.png'),
  },
  {
    id: 'concerts',
    name: 'Concerts',
    image: require('~/assets/images/genres/concerts.png'),
  },
  {
    id: 'pop',
    name: 'Pop',
    image: require('~/assets/images/genres/pop.png'),
  },
  {
    id: 'gospel',
    name: 'Gospel',
    image: require('~/assets/images/genres/gospel.png'),
  },
  {
    id: 'festival',
    name: 'Festival',
    image: require('~/assets/images/genres/festival.png'),
  },
  {
    id: 'podcast',
    name: 'Podcast',
    image: require('~/assets/images/genres/podcast.png'),
  },
];

export const CREATOR_COMPLETION_CARDS = [
  // {
  //   id: 'preferences',
  //   imageSource: require('~/assets/icons/onboarding/add.png'),
  //   title: 'Tailor your vibe',
  //   description:
  //     'Tell us what interests you so we can customise your experience.',
  // },
  {
    id: 'category',
    imageSource: require('~/assets/icons/onboarding/type.png'),
    title: 'Select creator type',
    description: 'Let everyone know the type of creator you are.',
  },
  {
    id: 'bio',
    imageSource: require('~/assets/icons/onboarding/bio.png'),
    title: 'Add a bio',
    description: 'Write a short bio about yourself.',
  },
  {
    id: 'avatar',
    imageSource: require('~/assets/icons/onboarding/avatar.png'),
    title: 'Set a profile picture',
    description: 'Let everyone know it is you.',
  },
  {
    id: 'inspirations',
    imageSource: require('~/assets/icons/onboarding/add.png'),
    title: 'Favourite popular accounts',
    description: 'Get inspired by favouriting popular accounts.',
  },
];

export const QUESTER_COMPLETION_CARDS = [
  // {
  //   id: 'preferences',
  //   imageSource: require('~/assets/icons/onboarding/add.png'),
  //   title: 'Tailor your vibe',
  //   description:
  //     'Tell us what interests you so we can customise your experience.',
  // },
  {
    id: 'inspirations',
    imageSource: require('~/assets/icons/onboarding/add.png'),
    title: 'Favourite popular accounts',
    description: 'Get inspired by favouriting popular accounts.',
  },
  {
    id: 'bio',
    imageSource: require('~/assets/icons/onboarding/bio.png'),
    title: 'Add a bio',
    description: 'Write a short bio about yourself.',
  },
  {
    id: 'avatar',
    imageSource: require('~/assets/icons/onboarding/avatar.png'),
    title: 'Set a profile picture',
    description: 'Let everyone know it is you.',
  },
];

export const EVENT_TYPE_CARDS = [
  {
    id: 'PUBLIC' as const,
    icon: require('~/assets/icons/events/public.png'),
    title: 'Public event',
    description: 'Anyone can find and join your event.',
  },
  {
    id: 'PRIVATE' as const,
    icon: require('~/assets/icons/events/private.png'),
    title: 'Private event',
    description: 'Only people with a unique access code or link can join.',
  },
];

export const TICKET_TYPE_CARDS = [
  {
    id: 'PAID' as const,
    icon: require('~/assets/icons/events/paid.png'),
    title: 'Paid event',
    description: 'Set a price for tickets.',
  },
  {
    id: 'FREE' as const,
    icon: require('~/assets/icons/events/free.png'),
    title: 'Free event',
    description: 'No cost to attend.',
  },
];

export const EVENT_FORMAT_CARDS = [
  {
    id: 'IN_PERSON' as const,
    icon: require('~/assets/icons/events/physical.png'),
    title: 'In-Person',
    description: 'Hosted at a phyiscal location.',
  },
  {
    id: 'ONLINE' as const,
    icon: require('~/assets/icons/events/online.png'),
    title: 'Online',
    description: 'Hosted virtually.',
  },
  {
    id: 'HYBRID' as const,
    icon: require('~/assets/icons/events/hybrid.png'),
    title: 'Hybrid',
    description: 'Hosted in-person and virtually.',
  },
];

export const USER_ROLE_CARDS = [
  {
    id: USER_ROLE_VALUES[0],
    icon: require('~/assets/images/questers.png'),
    title: "I'm Here to Explore",
    description:
      'Just here for the vibes and to explore countless sounds and activities anywhere, anytime',
  },
  {
    id: USER_ROLE_VALUES[1],
    icon: require('~/assets/images/headphone.png'),
    title: "I'm a Creator",
    description:
      'Specially made for Disc Jockeys, Bands, Artistes and other creatives',
  },
];

export const CREATOR_CATEGORY_VALUES = [
  'DJ',
  'Event Organizer',
  'Hype-person/MC',
  'Organization/Business',
  'Others',
] as const;

export const CREATOR_CATEGORY_CARDS = [
  {
    id: CREATOR_CATEGORY_VALUES[0],
    icon: require('~/assets/images/categories/dj.png'),
    title: 'DJ',
    description:
      'I mix tracks and perform live sets at events, clubs, or festivals.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[1],
    icon: require('~/assets/images/categories/organizer.png'),
    title: 'Event Organizer',
    description: 'I organize live events and shows.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[2],
    icon: require('~/assets/images/categories/artiste.png'),
    title: 'Hype-person/MC',
    description: 'I anchor events, clubs or festivals.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[3],
    icon: require('~/assets/images/categories/org.png'),
    title: 'Organization/Business',
    description: 'I represent a corporate body.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[4],
    icon: require('~/assets/images/categories/others.png'),
    title: 'Others',
  },
];

export const PASSWORD_RESTRICTIONS = [
  'Include at least one uppercase letter (A-Z), a number (0–9), and a special character (!, @, #, $ etc).',
  'Use 8 characters or more.',
];

// TODO: Remove.
export const EVENTS = [
  {
    id: 1,
    date: '16 May 2025',
    location: 'Eko Hotel',
    title: 'Ultraviolence Tour',
    image: require('~/assets/images/events/event.png'),
  },
  {
    id: 2,
    date: '16 May 2025',
    location: 'Eko Hotel',
    title: 'Ultraviolence',
    image: require('~/assets/images/events/event-2.png'),
  },
  {
    id: 3,
    date: '16 May 2025',
    location: 'Eko Hotel',
    title: 'Rizz 404 Bloc Party',
    image: require('~/assets/images/events/event-3.png'),
  },
  {
    id: 4,
    date: '16 May 2025',
    location: 'Eko Hotel',
    title: 'Dj Mustard Live in Concert',
    image: require('~/assets/images/events/event-4.png'),
  },
  {
    id: 5,
    date: '16 May 2025',
    location: 'Eko Hotel',
    title: 'Fire and Ice - Book Reading Reading',
    image: require('~/assets/images/events/event-5.png'),
  },
];

// TODO: Remove.
export const FAVORITE_EVENTS = [
  {
    id: 1,
    date: '16 May',
    location: 'Eko Hotel',
    title: 'DJ Kay Live',
    image: require('~/assets/gradient-bg/event-gradient.png'),
    attendees: '201',
  },
  {
    id: 2,
    date: '16 May',
    location: 'Eko Hotel',
    title: 'Ultraviolence',
    image: require('~/assets/gradient-bg/event-gradient.png'),
    attendees: '201',
  },
  {
    id: 3,
    date: '16 May',
    location: 'Eko Hotel',
    title: 'Rizz 404 Bloc Party',
    image: require('~/assets/gradient-bg/event-gradient.png'),
    attendees: '201',
  },
  {
    id: 4,
    date: '16 May',
    location: 'Eko Hotel',
    title: 'Dj Mustard Live in Concert',
    image: require('~/assets/gradient-bg/event-gradient.png'),
    attendees: '201',
  },
  {
    id: 5,
    date: '16 May',
    location: 'Eko Hotel',
    title: 'Fire and Ice - Book Reading Reading',
    image: require('~/assets/gradient-bg/event-gradient.png'),
    attendees: '201',
  },
];

export const APP_URL =
  Env.APP_ENV !== 'production'
    ? 'https://dev.app.getpopla.com'
    : 'https://getpopla.com';
export const APP_STORE_LINK =
  'https://apps.apple.com/ng/app/popla/id6474488670';
export const PLAY_STORE_LINK =
  'https://play.google.com/store/apps/details?id=com.popla.app';
export const STORE_LINK = Platform.select({
  ios: APP_STORE_LINK,
  android: PLAY_STORE_LINK,
  default: APP_URL,
});

export const IG_SITE_URL = 'https://instagram.com/getpopla';
export const IG_DEEP_LINK_URL = 'instagram://user?username=getpopla';

export const X_SITE_URL = 'https://x.com/getpopla';
export const X_DEEP_LINK_URL = 'x://user?screen_name=getpopla';

export const TIKTOK_SITE_URL = 'https://www.tiktok.com/@getpopla';
export const TIKTOK_DEEP_LINK_URL = 'tiktok://user?username=getpopla';

export const YOUTUBE_SITE_URL = 'https://www.youtube.com/@getpopla';
export const YOUTUBE_DEEP_LINK_URL = 'youtube://user?username=getpopla';

export const CANCELLATION_URL =
  'https://getpopla.com/policy#cancellation_policy';
export const T_C_URL = 'https://getpopla.com/policy#terms_and_conditions';
export const PRIVACY_POLICY_URL = 'https://getpopla.com/policy#privacy_policy';
export const FAQ_URL = 'https://getpopla.com/help';

export const PROFILE_SECTIONS: ProfileSections = [
  {
    title: 'Account',
    items: [
      {
        icon: 'settings-outline',
        iconType: 'ionicons',
        label: 'Account settings',
        navigateTo: '/profile/account',
      },
      {
        icon: 'headset-outline',
        iconType: 'ionicons',
        label: 'Live session history',
        navigateTo: '/profile/session',
      },
    ],
  },
  {
    title: 'Finance',
    items: [
      {
        icon: 'wallet-outline',
        iconType: 'ionicons',
        label: 'Wallet',
        navigateTo: '/profile/wallet',
      },
    ],
  },
  {
    title: 'Preference',
    items: [
      {
        icon: 'notifications-outline',
        iconType: 'ionicons',
        label: 'Notification',
        navigateTo: '/profile/notification',
      },
      {
        icon: 'chatbubbles-outline',
        iconType: 'ionicons',
        label: 'Chat preferences',
        navigateTo: '/profile/chat',
      },
      // {
      //   icon: 'play-circle-outline',
      //   iconType: 'ionicons',
      //   label: 'Content preferences',
      //   navigateTo: '/profile/preferences',
      // },
      {
        icon: 'moon-outline',
        iconType: 'ionicons',
        label: 'Dark mode',
        navigateTo: '/profile',
        isToggle: true,
      },
    ],
  },
  {
    title: 'Support',
    items: [
      {
        icon: 'information-circle-outline',
        iconType: 'ionicons',
        label: 'About the app',
        navigateTo: '/profile/about',
      },
      {
        // use custom icon
        icon: 'Help',
        iconType: 'custom-svg',
        label: 'Help and support',
        navigateTo: '/profile',
        hasWebView: true,
        externalUrl: FAQ_URL,
      },
      {
        icon: 'document-text-outline',
        iconType: 'ionicons',
        label: 'Terms and conditions',
        navigateTo: '/profile/terms',
      },
      {
        icon: 'close-circle-outline',
        iconType: 'ionicons',
        label: 'Cancellation policy',
        navigateTo: '/profile/cancellation-policy',
      },
    ],
  },
  // {
  //   title: 'Stay in touch',
  //   items: [
  //     {
  //       // use custom icons
  //       icon: 'Instagram',
  //       iconType: 'custom-svg',
  //       label: 'Follow us on Instagram',
  //       navigateTo: '/profile',
  //       handleExternalLink: () =>
  //         openLinkInBrowser(IG_DEEP_LINK_URL, IG_SITE_URL),
  //     },
  //     {
  //       icon: 'X',
  //       iconType: 'custom-svg',
  //       label: 'Follow us on X',
  //       navigateTo: '/profile',
  //       handleExternalLink: () =>
  //         openLinkInBrowser(X_DEEP_LINK_URL, X_SITE_URL),
  //     },
  //     {
  //       icon: 'heart',
  //       iconType: 'ionicons',
  //       iconColor: '#FF5226',
  //       label: 'Rate this app',
  //       navigateTo: '/profile',
  //       handleExternalLink: () => openStoreLink(STORE_LINK),
  //     },
  //   ],
  // },
];

export const LOGOUT_ITEM = (selectedTheme: ColorSchemeType): ProfileItem => ({
  icon: 'logout',
  iconType: 'material',
  iconColor: selectedTheme === 'dark' ? '#FF9175' : '#DB340B',
  label: 'Log out',
  navigateTo: '/profile/confirm-logout',
  textColor: 'text-red-60 dark:text-red-40',
  hideChevronForward: true,
});

export const DELETE_ACCOUNT_ITEM = (
  selectedTheme: ColorSchemeType
): ProfileItem => ({
  icon: 'trash',
  iconType: 'feather',
  iconColor: selectedTheme === 'dark' ? '#FF9175' : '#DB340B',
  label: 'Delete account',
  navigateTo: '/profile/account/confirm-delete-account',
  textColor: 'text-red-60 dark:text-red-40',
  hideChevronForward: true,
});

export const NOTIFICATION_TYPES = ['Email', 'Push'] as const;
export type NotificationOption = (typeof NOTIFICATION_TYPES)[number];

export const CHAT_OPTIONS = ['Online status', 'Last seen', 'Near me'] as const;
export type ChatOption = (typeof CHAT_OPTIONS)[number];

export const RECENT_SEARCHES_KEY = '@recent_searches';
export const MAX_RECENT_SEARCHES = 10;

export const CAROUSEL_INTERVAL = 3000; // Auto-play interval in ms

export const REPORT_OPTIONS: { label: string; value: ReportType }[] = [
  { label: 'Fake profile / spam', value: 'spam' },
  { label: 'Inappropriate content', value: 'inappropriate' },
  { label: 'Copyright infringement', value: 'copyright' },
  { label: 'Underage user', value: 'underage' },
];

export const GIFTS = [
  {
    name: 'Vinyl - N100,000',
    image: require('~/assets/images/session/gifts/vinyl.png'),
  },
  {
    name: 'Mixer - N70,000',
    image: require('~/assets/images/session/gifts/mixer.png'),
  },
  {
    name: 'Mic - N50,000',
    image: require('~/assets/images/session/gifts/mic.png'),
  },
  {
    name: 'Note - N30,000',
    image: require('~/assets/images/session/gifts/note.png'),
  },
  {
    name: 'Ballon - N50,000',
    image: require('~/assets/images/session/gifts/ballon.png'),
  },
  {
    name: 'Unicorn - N30,000',
    image: require('~/assets/images/session/gifts/unicorn.png'),
  },
  {
    name: 'Pizza - N10,000',
    image: require('~/assets/images/session/gifts/pizza.png'),
  },
  {
    name: 'Coffee - N5,000',
    image: require('~/assets/images/session/gifts/coffee.png'),
  },
];

export const googlePlacesAutocompleteDefaultProps: GooglePlacesAutocompleteProps =
  {
    autoFillOnNotFound: false,
    currentLocation: false,
    currentLocationLabel: 'Current location',
    debounce: 0,
    disableScroll: false,
    enableHighAccuracyLocation: true,
    enablePoweredByContainer: true,
    fetchDetails: false,
    fields: '*',
    filterReverseGeocodingByTypes: [],
    GooglePlacesDetailsQuery: {},
    GooglePlacesSearchQuery: {
      rankby: 'distance',
      type: 'restaurant',
    },
    GoogleReverseGeocodingQuery: {},
    isNewPlacesAPI: false,
    isRowScrollable: true,
    keepResultsAfterBlur: false,
    keyboardShouldPersistTaps: 'always',
    listHoverColor: '#ececec',
    listUnderlayColor: '#c8c7cc',
    listViewDisplayed: 'auto',
    minLength: 0,
    nearbyPlacesAPI: 'GooglePlacesSearch',
    numberOfLines: 1,
    onFail: () => {},
    onNotFound: () => {},
    onPress: () => {},
    // eslint-disable-next-line no-console
    onTimeout: () =>
      console.warn('google places autocomplete: request timeout'),
    placeholder: '',
    predefinedPlaces: [],
    predefinedPlacesAlwaysVisible: false,
    query: {
      key: 'missing api key',
      language: 'en',
      type: 'geocode',
    },
    styles: {},
    suppressDefaultStyles: false,
    textInputHide: false,
    textInputProps: {},
    timeout: 20000,
  };

export const mapCustomStyle = [
  { elementType: 'geometry', stylers: [{ color: '#242f3e' }] },
  { elementType: 'labels.text.fill', stylers: [{ color: '#746855' }] },
  { elementType: 'labels.text.stroke', stylers: [{ color: '#242f3e' }] },
  {
    featureType: 'administrative.locality',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'poi',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'geometry',
    stylers: [{ color: '#263c3f' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#6b9a76' }],
  },
  {
    featureType: 'road',
    elementType: 'geometry',
    stylers: [{ color: '#38414e' }],
  },
  {
    featureType: 'road',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#212a37' }],
  },
  {
    featureType: 'road',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#9ca5b3' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry',
    stylers: [{ color: '#746855' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#1f2835' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#f3d19c' }],
  },
  {
    featureType: 'transit',
    elementType: 'geometry',
    stylers: [{ color: '#2f3948' }],
  },
  {
    featureType: 'transit.station',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'water',
    elementType: 'geometry',
    stylers: [{ color: '#17263c' }],
  },
  {
    featureType: 'water',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#515c6d' }],
  },
  {
    featureType: 'water',
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#17263c' }],
  },
];

export const CATEGORY_MAPPINGS = {
  'Adventure & Games': [
    'amusement_park' as SearchType,
    'tourist_attraction' as SearchType,
    'bowling_alley' as SearchType,
  ],
  'Arts & Culture': [
    'museum' as SearchType,
    'art_gallery' as SearchType,
    'library' as SearchType,
  ],
  'Club & Nightlife': [
    'night_club' as SearchType,
    'bar' as SearchType,
    'restaurant' as SearchType,
  ],
  'Club & Nightlifeee': [
    'night_club' as SearchType,
    'bar' as SearchType,
    'restaurant' as SearchType,
  ],
};

export type CategoryName = keyof typeof CATEGORY_MAPPINGS;
