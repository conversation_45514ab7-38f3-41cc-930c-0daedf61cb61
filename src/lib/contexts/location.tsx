import { useQueryClient } from '@tanstack/react-query';
import * as Location from 'expo-location';
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { AppState, Platform } from 'react-native';

import { type LocationObject, type UserObjectData } from '@/api/auth';
import { useUpdateProfile } from '@/api/user';
import { type LocationType } from '@/types';

import { useAuth } from '../auth';
import { DEFAULT_COORDINATES } from '../constants';
import { getLatLongFromCountry } from '../utils';

type LocationContextType = {
  location: LocationType | null;
  setLocation: (loc: LocationType | null) => void;
  getCurrentLocation: () => Promise<void>;
  locationAccessible: boolean;
};

const LocationContext = createContext<LocationContextType | undefined>(
  undefined
);

export const useLocation = () => {
  const ctx = useContext(LocationContext);
  if (!ctx) throw new Error('useLocation must be used within LocationProvider');
  return ctx;
};

type Props = {
  children: React.ReactNode;
  user?: UserObjectData;
};

export const LocationProvider: React.FC<Props> = ({ children, user }) => {
  const queryClient = useQueryClient();
  const status = useAuth.use.status();
  const isAuthenticated = status === 'signedIn';
  const [location, setLocation] = useState<LocationType | null>(null);
  const [locationAccessible, setLocationAccessible] = React.useState(false);
  const appState = useRef(AppState.currentState);

  const { mutate: updateProfile } = useUpdateProfile({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getUser'] });
    },
  });

  const getCurrentLocation = async () => {
    if (!user || !isAuthenticated) return;

    setLocationAccessible(false);

    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      console.warn('Location permission denied');
      if (user.location) {
        let countryCoords: { latitude: number; longitude: number } | null;
        if (Platform.OS === 'android') {
          countryCoords =
            DEFAULT_COORDINATES[user.location.country.toUpperCase()];
        } else {
          countryCoords = await getLatLongFromCountry(user.location.country);
        }
        setLocation({
          ...user.location,
          coordinates: {
            ...user.location.coordinates,
            lat: countryCoords?.latitude || user.location.coordinates.lat,
            lng: countryCoords?.longitude || user.location.coordinates.lng,
          },
        });
      }
      return;
    }

    const currentLocation = await Location.getCurrentPositionAsync({});
    if (!currentLocation) {
      if (user.location) setLocation(user.location);
      return;
    }

    const locationObj: LocationType = {
      coordinates: {
        lat: currentLocation.coords.latitude,
        lng: currentLocation.coords.longitude,
      },
      landmark: '',
      city: '',
      state: '',
      street: '',
      country: '',
      address: '',
    };

    try {
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      });

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        locationObj.city = address.city || 'N/A';
        locationObj.state = address.region || 'N/A';
        locationObj.country = address.country || 'N/A';
        locationObj.street = address.street || 'N/A';
        locationObj.landmark = address.name || address.street || 'N/A';
        locationObj.address =
          address.formattedAddress || address.name || address.street || 'N/A';

        updateProfile({
          userId: user.id,
          payload: {
            location: locationObj as LocationObject,
          },
        });
      }
    } catch (error) {
      console.error('Error getting reversed geocode address:', error);
    } finally {
      setLocation(locationObj);
      setLocationAccessible(true);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, [user]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextState) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextState === 'active'
      ) {
        getCurrentLocation();
      }
      appState.current = nextState;
    });
    return () => subscription.remove();
  }, [user]);

  return (
    <LocationContext.Provider
      value={{ location, setLocation, getCurrentLocation, locationAccessible }}
    >
      {children}
    </LocationContext.Provider>
  );
};
