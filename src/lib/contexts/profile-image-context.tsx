// contexts/ProfileImageContext.tsx
import React, {
  createContext,
  type ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';

import { getProfileImageKey, setProfileImageKey } from '../auth/utils';

interface ProfileImageContextType {
  imageKey: number | undefined;
  refreshImage: () => void;
  getProfileImageUrl: (baseUrl?: string) => string | undefined;
}

const ProfileImageContext = createContext<ProfileImageContextType | undefined>(
  undefined
);

interface ProfileImageProviderProps {
  children: ReactNode;
}

export function ProfileImageProvider({ children }: ProfileImageProviderProps) {
  const [imageKey, setImageKey] = useState<number | undefined>(undefined);

  // Load persisted imageKey on mount
  useEffect(() => {
    const persistedKey = getProfileImageKey();
    if (persistedKey) {
      setImageKey(persistedKey);
    }
  }, []);

  const refreshImage = () => {
    const newKey = Date.now();
    setImageKey(newKey);
    setProfileImageKey(newKey);
  };

  const getProfileImageUrl = (baseUrl?: string) => {
    if (!baseUrl) return undefined;

    if (imageKey) {
      const separator = baseUrl.includes('?') ? '&' : '?';
      return `${baseUrl}${separator}t=${imageKey}`;
    }

    return baseUrl;
  };

  return (
    <ProfileImageContext.Provider
      value={{ imageKey, refreshImage, getProfileImageUrl }}
    >
      {children}
    </ProfileImageContext.Provider>
  );
}

export function useProfileImage() {
  const context = useContext(ProfileImageContext);
  if (context === undefined) {
    throw new Error(
      'useProfileImage must be used within a ProfileImageProvider'
    );
  }
  return context;
}
