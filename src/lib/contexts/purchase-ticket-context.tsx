import { createContext, useContext } from 'react';

import {
  type EventDiscount,
  type IEventCategories,
  type ISingleEvent,
} from '@/api/events';

import {
  type AdditionalFees,
  type CostBreakdown,
  type SelectedTicket,
} from '../hooks/use-purchase-ticket';

interface PurchaseTicketContextType {
  event: ISingleEvent | undefined;
  categories: IEventCategories[] | undefined;
  isEventLoading: boolean;
  selectedTickets: Record<string, number>;
  handleTicketQuantityChange: (category: string, quantity: number) => void;
  getSelectedTicketsArray: () => SelectedTicket[];
  hasSelectedTickets: boolean;
  resetForm: () => void;
  calculateTotalCost: (additionalFees?: AdditionalFees) => CostBreakdown;
  getTotalQuantity: () => number;
  eventId: string;
  editType?: 'desc' | 'date' | 'location';
  setEditType: React.Dispatch<
    React.SetStateAction<'desc' | 'date' | 'location' | undefined>
  >;
  activeDiscount?: EventDiscount;
  setActiveDiscount: React.Dispatch<
    React.SetStateAction<EventDiscount | undefined>
  >;
}

const PurchaseTicketContext = createContext<
  PurchaseTicketContextType | undefined
>(undefined);
export const PurchaseTicketProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: PurchaseTicketContextType;
}) => {
  return (
    <PurchaseTicketContext.Provider value={value}>
      {children}
    </PurchaseTicketContext.Provider>
  );
};

export const usePurchaseTicketContext = () => {
  const context = useContext(PurchaseTicketContext);
  if (context === undefined) {
    throw new Error(
      'usePurchaseTicketContext must be used within a PurchaseTicketProvider'
    );
  }
  return context;
};
