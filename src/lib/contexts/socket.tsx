import React from 'react';
import { type Socket } from 'socket.io-client';

import connectSocket from '@/api/socket';

import { useLoggedInUser } from '../hooks';

interface SocketContextType {
  socket: Socket | null;
}

const SocketContext = React.createContext<SocketContextType | undefined>(
  undefined
);

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const { data: user } = useLoggedInUser();
  const [socket, setSocket] = React.useState<Socket | null>(null);

  React.useEffect(() => {
    if (!user?.id || socket) return;

    const newSocket = connectSocket();
    setSocket(newSocket);
    console.log('Socket connected via provider.');

    return () => {
      newSocket.disconnect();
      setSocket(null);
      console.log('Socket disconnected via provider.');
    };
  }, [user?.id]);

  const value = React.useMemo(() => ({ socket }), [socket]);

  return (
    <SocketContext.Provider value={value}>{children}</SocketContext.Provider>
  );
}

export function useSocket() {
  const context = React.useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}
