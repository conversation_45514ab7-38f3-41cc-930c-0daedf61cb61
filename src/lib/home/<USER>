import { create } from 'zustand';

import {
  type LiveSessionResponse,
  type SessionOrTrendingItem,
  type TrendingDjResponse,
} from '@/api/session/types';
import { type HomeDataResponse } from '@/api/user';

import { setUser } from '../auth/utils';
import { getLiveSession } from '../session/utils';
import { createSelectors } from '../utils';
import { getCombinedSessionAndDjSessionsList } from './utils';

// Define the store state type
interface HomeState {
  trendingDJs: TrendingDjResponse[];
  topLiveSessions: LiveSessionResponse[];
  combinedSessionAndTrendingList: SessionOrTrendingItem[];

  // Actions
  setTrendingDJs: (djs: TrendingDjResponse[]) => void;
  setTopLiveSessions: (params: {
    djSessions: LiveSessionResponse[];
    combinedSessionAndTrendingList: SessionOrTrendingItem[];
  }) => void;

  // Helper function to process data after fetching
  processHomeData: (data: HomeDataResponse) => void;
}

// Create the store
const _useHomeStore = create<HomeState>((set) => ({
  trendingDJs: [],
  topLiveSessions: [],
  combinedSessionAndTrendingList: [],

  setTrendingDJs: (djs) => set({ trendingDJs: djs }),

  setTopLiveSessions: (params) =>
    set({
      topLiveSessions: params.djSessions,
      combinedSessionAndTrendingList: params.combinedSessionAndTrendingList,
    }),

  processHomeData: async (data) => {
    const activeSession = getLiveSession();

    const { djSessions, trendingDJs, user } = data;

    setUser(user);

    // Process data
    const combinedSessionAndTrendingList = getCombinedSessionAndDjSessionsList({
      activeSession: activeSession?.id || '',
      activeSessionCreator: activeSession?.dj.username || '',
      djSessions: [...djSessions].reverse(),
      trendingDJs,
      user,
    });

    set({
      trendingDJs,
      topLiveSessions: djSessions,
      combinedSessionAndTrendingList,
    });
  },
}));

// Export with selectors
export const useHomeStore = createSelectors(_useHomeStore);
