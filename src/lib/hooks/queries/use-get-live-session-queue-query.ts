import { useGetLiveSessionQueues } from '@/api/session';

type UseGetLiveSessionQueueQueryOptions = { enabled: boolean };

export const useGetLiveSessionQueueQuery = (
  sessionId: string,
  options?: UseGetLiveSessionQueueQueryOptions
) =>
  useGetLiveSessionQueues({
    variables: {
      sessionId,
    },
    refetchOnReconnect: 'always',
    refetchInterval: 60 * 1000,
    ...(options && { options }),
  });
