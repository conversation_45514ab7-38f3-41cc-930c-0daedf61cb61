import { useMemo } from 'react';

import { useGetUserFavourites } from '@/api/user';

import { useFavoriteToggle } from './use-favourite-toggle';

type UseAccountFavouriteResult = {
  favourited: boolean;
  toggleFavourite: () => void;
};

export const useAccountFavourite = (
  accountId: string
): UseAccountFavouriteResult => {
  const { data: favouritesData } = useGetUserFavourites({
    variables: { type: 'ACCOUNT' },
  });

  const isFavourited = useMemo(() => {
    return favouritesData?.some((account) => account.id === accountId) ?? false;
  }, [favouritesData, accountId]);

  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isFavourited,
    payload: {
      type: 'ACCOUNT',
      accountId,
      eventId: null,
    },
  });

  return {
    favourited,
    toggleFavourite: handleFavToggle,
  };
};
