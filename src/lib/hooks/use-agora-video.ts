import { useCallback, useEffect, useRef, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import {
  ChannelProfileType,
  ClientRoleType,
  ConnectionStateType,
  createAgoraRtcEngine,
  type IRtcEngine,
  type IRtcEngineEventHandler,
  type RtcConnection,
} from 'react-native-agora';

import { useSocket } from '../contexts';

// TODO: USE ENV
const AGORA_APP_ID = '31ce1c253f2c499b81eec5106c370102';

export type JoinState = 'initial' | 'connecting' | 'connected' | 'failed';

interface UseAgoraVideoProps {
  channelName: string;
  token: string;
  isSessionOwner: boolean;
  enableVideo?: boolean;
  uid: number;
}

const getPermission = async (isSessionOwner: boolean, enableVideo: boolean) => {
  if (Platform.OS === 'android' && isSessionOwner && enableVideo) {
    await PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      PermissionsAndroid.PERMISSIONS.CAMERA,
    ]);
  }
};

export const useAgoraVideo = ({
  channelName,
  token,
  isSessionOwner,
  enableVideo,
  uid,
}: UseAgoraVideoProps) => {
  const { socket } = useSocket();

  const agoraEngineRef = useRef<IRtcEngine | null>(null);
  const eventHandlerRef = useRef<IRtcEngineEventHandler | null>(null);

  const [joinState, setJoinState] = useState<JoinState>('initial'); // Whether the local user has joined the channel
  const [remoteUid, setRemoteUid] = useState(0); // Uid of the remote user
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraHidden, setIsCameraHidden] = useState(false);

  const setupLocalVideo = useCallback(() => {
    if (!enableVideo) return;
    agoraEngineRef.current?.enableVideo();
    agoraEngineRef.current?.startPreview();
  }, [enableVideo]);

  const setupEventHandler = useCallback(() => {
    if (!enableVideo) return;

    eventHandlerRef.current = {
      onJoinChannelSuccess: () => {
        setupLocalVideo();
      },
      onConnectionStateChanged: (
        _connection: RtcConnection,
        state: ConnectionStateType
      ) => {
        switch (state) {
          case ConnectionStateType.ConnectionStateConnecting:
            setJoinState('connecting');
            break;
          case ConnectionStateType.ConnectionStateConnected:
            setJoinState('connected');
            break;
          case ConnectionStateType.ConnectionStateFailed:
          case ConnectionStateType.ConnectionStateDisconnected:
            setJoinState('failed');
            break;
        }
      },
      onUserJoined: (_connection: RtcConnection, uid: number) => {
        setRemoteUid(uid);
      },
      onUserOffline: (_connection: RtcConnection, uid: number) => {
        setRemoteUid(uid);
      },
    };
    agoraEngineRef.current?.registerEventHandler(eventHandlerRef.current);
  }, [enableVideo, setupLocalVideo]);

  const setupVideoSDKEngine = useCallback(async () => {
    try {
      if (!enableVideo || agoraEngineRef.current) return;

      await getPermission(isSessionOwner, enableVideo);
      agoraEngineRef.current = createAgoraRtcEngine();
      const agoraEngine = agoraEngineRef.current;
      agoraEngine.initialize({ appId: AGORA_APP_ID });
      setupEventHandler();
    } catch (e) {
      console.error(e);
    }
  }, [isSessionOwner, enableVideo]);

  const joinChannel = useCallback(async () => {
    if (
      joinState === 'connected' ||
      !agoraEngineRef.current ||
      !enableVideo ||
      !token
    ) {
      return;
    }

    console.log('joining...');
    setJoinState('connecting');

    try {
      const joinOptions = {
        channelProfile: ChannelProfileType.ChannelProfileLiveBroadcasting,
        autoSubscribeAudio: true,
        autoSubscribeVideo: true,
      };

      if (isSessionOwner) {
        agoraEngineRef.current?.joinChannel(token, channelName, uid, {
          ...joinOptions,
          clientRoleType: ClientRoleType.ClientRoleBroadcaster,
          publishMicrophoneTrack: true,
          publishCameraTrack: true,
        });
      } else {
        agoraEngineRef.current?.joinChannel(token, channelName, 0, {
          ...joinOptions,
          clientRoleType: ClientRoleType.ClientRoleAudience,
          publishMicrophoneTrack: false,
          publishCameraTrack: false,
        });
      }
    } catch (e) {
      console.log(e);
      setJoinState('failed');
    }
  }, [channelName, token, isSessionOwner, enableVideo, uid, joinState]);

  const leaveChannel = useCallback(() => {
    agoraEngineRef.current?.unregisterEventHandler(eventHandlerRef.current!);
    agoraEngineRef.current?.leaveChannel();
    agoraEngineRef.current?.release();
    setRemoteUid(0);
    setJoinState('initial');
  }, []);

  const toggleMute = useCallback(async () => {
    if (agoraEngineRef.current) {
      agoraEngineRef.current?.muteLocalAudioStream(!isMuted);
      setIsMuted(!isMuted);
    }
  }, [isMuted]);

  const switchCamera = useCallback(async () => {
    agoraEngineRef.current?.switchCamera();
  }, []);

  const toggleCamera = useCallback(async () => {
    if (!agoraEngineRef.current || !socket) return;

    const newCameraState = !isCameraHidden;

    if (newCameraState) {
      agoraEngineRef.current.disableVideo();
    } else {
      agoraEngineRef.current.enableVideo();
    }

    setIsCameraHidden(!isCameraHidden);

    socket.emit('CAMERA_CHANGE', {
      sessionId: channelName,
      isCameraHidden: !isCameraHidden,
    });
  }, [isCameraHidden, socket]);

  useEffect(() => {
    const init = async () => {
      await setupVideoSDKEngine();
    };
    init();
    return () => {
      leaveChannel();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enableVideo]);

  useEffect(() => {
    if (token && enableVideo) {
      joinChannel();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, joinChannel, agoraEngineRef.current]);

  return {
    joinState,
    remoteUid,
    isMuted,
    isCameraHidden,
    toggleMute,
    switchCamera,
    toggleCamera,
    leaveChannel,
    retryJoin: joinChannel,
  };
};
