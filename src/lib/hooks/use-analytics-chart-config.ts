import { useColorScheme } from 'nativewind';
import {
  type BarChartPropsType,
  type PieChartPropsType,
} from 'react-native-gifted-charts';

import { colors, semanticColors } from '@/components/ui';

export const useAnalyticsChartConfig = () => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const barChartConfig: BarChartPropsType = {
    barWidth: 32,
    noOfSections: 3,
    barBorderRadius: 32,
    initialSpacing: 0,
    frontColor: isDark
      ? semanticColors.accent.muted.dark
      : semanticColors.accent.muted.light,
    yAxisThickness: 0,
    xAxisThickness: 0,
    hideAxesAndRules: true,
    hideYAxisText: true,
  };

  const pieChartConfig: Omit<PieChartPropsType, 'data'> = {
    donut: true,
    showText: true,
    textColor: 'white',
    radius: 100,
    textSize: 12,
    textBackgroundRadius: 26,
    backgroundColor: isDark ? colors.grey[100] : colors.white,
    initialAngle: 370,
    strokeColor: isDark ? colors.grey[100] : colors.white,
    strokeWidth: 4,
  };

  const horizontalBarConfig: BarChartPropsType = {
    horizontal: true,
    barWidth: 32,
    barBorderRadius: 32,
    frontColor: isDark
      ? semanticColors.bg.muted.dark
      : semanticColors.bg.muted.light,
    yAxisThickness: 0,
    xAxisThickness: 0,
    spacing: 16,
    hideAxesAndRules: true,
  };

  return {
    barChartConfig,
    pieChartConfig,
    horizontalBarConfig,
  };
};
