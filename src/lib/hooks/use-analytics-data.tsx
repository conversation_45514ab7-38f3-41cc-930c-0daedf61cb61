import dayjs from 'dayjs';
import { useMemo } from 'react';
import { type barDataItem } from 'react-native-gifted-charts';

import {
  EventFormat,
  type ISingleEvent,
  type PresaleConfig,
  type TicketCategories,
} from '@/api/events';
import { colors, H6, XsRegularLabel } from '@/components/ui';
import {
  computeAnalyticsMetrics,
  hasTicketCategories,
  hasValidPresaleTicketCategories,
  isPresaleActive,
} from '@/lib';

interface Props {
  event?: ISingleEvent;
  ticketCategories?: TicketCategories | null;
  presaleConfig?: PresaleConfig[];
}

export const useAnalyticsData = ({
  event,
  presaleConfig,
  ticketCategories,
}: Props) => {
  const barData: barDataItem[] = useMemo(() => {
    if (!event || !hasTicketCategories(ticketCategories)) {
      return [];
    }

    // Since we don't have historical daily sales data in the current data structure,
    // we'll generate a date range and show 0 for all days except today
    // In a real implementation, this would come from a time-series API endpoint
    const today = dayjs();
    const dateRange = [];

    // Generate last 6 days
    for (let i = 5; i >= 0; i--) {
      const date = today.subtract(i, 'day');
      dateRange.push({
        date: date.format('MMM D'),
        dayName: date.format('ddd'),
        isToday: i === 0,
      });
    }

    // For the current implementation, we'll show total sales on the most recent day
    // and 0 for previous days since we don't have historical data
    const totalSold = Object.values(ticketCategories).reduce(
      (sum, category) => sum + (category.sold || 0),
      0
    );

    return dateRange.map(({ date, isToday }) => {
      const value = isToday ? totalSold : 0;
      const isHighlighted = value > 0;

      return {
        value,
        topLabelComponent: () => (
          <XsRegularLabel className="mb-2 text-fg-muted-light dark:text-fg-muted-dark">
            {value}
          </XsRegularLabel>
        ),
        labelComponent: () => (
          <XsRegularLabel className="self-center text-fg-muted-light dark:text-fg-muted-dark">
            {date}
          </XsRegularLabel>
        ),
        ...(isHighlighted && {
          frontColor: colors.brand[60],
        }),
      };
    });
  }, [event, ticketCategories]);

  const conversionData = useMemo(() => {
    if (!hasTicketCategories(ticketCategories)) {
      return [];
    }

    const categories = Object.entries(ticketCategories);
    const totalSold = categories.reduce(
      (sum, [, category]) => sum + (category.sold || 0),
      0
    );

    if (totalSold === 0) {
      return [];
    }

    const conversionRateData = categories
      .map(([key, category]) => {
        const sold = category.sold || 0;
        const percentage =
          totalSold > 0 ? Math.round((sold / totalSold) * 100) : 0;

        const formattedLabel = key
          .split(/[-_\s]/)
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(' ');

        return {
          label: formattedLabel,
          value: percentage,
          sold,
          category: key,
        };
      })
      .filter((item) => item.sold > 0)
      .sort((a, b) => b.sold - a.sold); // Sort by highest sales

    return conversionRateData;
  }, [ticketCategories]);

  const attendanceModeData: barDataItem[] = useMemo(() => {
    if (!event) {
      return [];
    }

    const data: barDataItem[] = [];

    switch (event.eventFormat) {
      case EventFormat.IN_PERSON:
        data.push({
          value: 100,
          // topLabelComponent: () => (
          //   <XsRegularLabel
          //     className=""
          //     style={{ transform: [{ rotate: '90deg' }] }}
          //     numberOfLines={1}
          //   >
          //     Physical
          //   </XsRegularLabel>
          // ),
          labelComponent: () => <H6 className="mr-2 self-center">100%</H6>,
          frontColor: colors.brand[60],
        });
        break;

      case EventFormat.ONLINE:
        data.push({
          value: 100,
          // topLabelComponent: () => (
          //   <XsRegularLabel className="absolute left-0 rotate-90">
          //     Online
          //   </XsRegularLabel>
          // ),
          labelComponent: () => <H6 className="mr-2 self-center">100%</H6>,
          frontColor: colors.brand[60],
        });
        break;

      case EventFormat.HYBRID:
        // This should be configurable or based on actual registrations
        data.push(
          {
            value: 70,
            // topLabelComponent: () => (
            //   <XsRegularLabel className="absolute left-0 rotate-90">
            //     Physical
            //   </XsRegularLabel>
            // ),
            labelComponent: () => <H6 className="mr-2 self-center">70%</H6>,
            frontColor: colors.brand[60],
          },
          {
            value: 30,
            // topLabelComponent: () => (
            //   <XsRegularLabel className="absolute left-0 rotate-90">
            //     Online
            //   </XsRegularLabel>
            // ),
            labelComponent: () => <H6 className="mr-2 self-center">30%</H6>,
            frontColor: colors.grey[40],
          }
        );
        break;

      default:
        // Default to physical if format is not specified
        data.push({
          value: 100,
          labelComponent: () => <H6 className="mr-2 self-center">100%</H6>,
          frontColor: colors.brand[60],
        });
    }

    return data;
  }, [event?.eventFormat]);

  const presaleAnalytics = useMemo(() => {
    if (!hasValidPresaleTicketCategories(presaleConfig)) {
      return null;
    }

    const activePresales = presaleConfig?.filter(isPresaleActive) || [];

    if (activePresales.length === 0) {
      return null;
    }

    const presaleMetrics = activePresales.reduce(
      (acc, presale) => {
        const soldTickets = presale.totalTickets - presale.quantity;
        return {
          totalSold: acc.totalSold + soldTickets,
          totalCapacity: acc.totalCapacity + presale.totalTickets,
          totalAvailable: acc.totalAvailable + presale.quantity,
        };
      },
      { totalSold: 0, totalCapacity: 0, totalAvailable: 0 }
    );

    const presaleConversionRate =
      presaleMetrics.totalCapacity > 0
        ? (presaleMetrics.totalSold / presaleMetrics.totalCapacity) * 100
        : 0;

    return {
      totalPresaleSold: presaleMetrics.totalSold,
      totalPresaleCapacity: presaleMetrics.totalCapacity,
      totalPresaleAvailable: presaleMetrics.totalAvailable,
      presaleConversionRate,
      activePresales: activePresales.length,
      presaleTickets: activePresales.map((presale) => ({
        name: presale.name,
        sold: presale.totalTickets - presale.quantity,
        total: presale.totalTickets,
        available: presale.quantity,
        price: presale.price,
        currency: presale.currency,
      })),
    };
  }, [presaleConfig]);

  const analyticsMetrics = useMemo(() => {
    return computeAnalyticsMetrics(ticketCategories, presaleConfig);
  }, [ticketCategories, presaleConfig]);

  const revenueMetrics = useMemo(() => {
    if (!hasTicketCategories(ticketCategories)) {
      return { totalRevenue: 0, projectedRevenue: 0, averageTicketPrice: 0 };
    }

    const categories = Object.values(ticketCategories);
    const totalRevenue = categories.reduce((sum, category) => {
      const sold = category.sold || 0;
      const price = category.cost || 0;
      return sum + sold * price;
    }, 0);

    const projectedRevenue = categories.reduce((sum, category) => {
      const totalCapacity = (category.sold || 0) + (category.quantity || 0);
      const price = category.cost || 0;
      return sum + totalCapacity * price;
    }, 0);

    const totalTicketsSold = categories.reduce(
      (sum, category) => sum + (category.sold || 0),
      0
    );
    const averageTicketPrice =
      totalTicketsSold > 0 ? totalRevenue / totalTicketsSold : 0;

    return {
      totalRevenue,
      projectedRevenue,
      averageTicketPrice,
    };
  }, [ticketCategories]);

  const ticketCategoryBreakdown = useMemo(() => {
    if (!hasTicketCategories(ticketCategories)) {
      return [];
    }

    return Object.entries(ticketCategories)
      .map(([key, category]) => {
        const sold = category.sold || 0;
        const available = category.quantity || 0;
        const total = sold + available;
        const soldPercentage = total > 0 ? (sold / total) * 100 : 0;

        return {
          name: key,
          sold,
          available,
          total,
          soldPercentage,
          price: category.cost || 0,
          revenue: sold * (category.cost || 0),
          description: category.description,
        };
      })
      .sort((a, b) => b.sold - a.sold);
  }, [ticketCategories]);

  return {
    barData,
    ticketCategories: conversionData,
    horizontalBarData: attendanceModeData,
    presaleAnalytics,
    analyticsMetrics,
    revenueMetrics,
    ticketCategoryBreakdown,
    event,
  };
};
