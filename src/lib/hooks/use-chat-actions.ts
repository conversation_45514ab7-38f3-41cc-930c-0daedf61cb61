import {
  collection,
  deleteField,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  onSnapshot,
  serverTimestamp,
  updateDoc,
  writeBatch,
} from '@react-native-firebase/firestore';
import { useRouter } from 'expo-router';
import { useLayoutEffect, useState } from 'react';
import { toast } from 'sonner-native';

import { useAccountFavourite, useLoggedInUser } from '@/lib';
import type { ChatListType, UserType } from '@/types';

export const useChatActions = (
  chat: ChatListType,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const { push } = useRouter();
  const { data: user } = useLoggedInUser();
  const { favourited, toggleFavourite } = useAccountFavourite(chat.userId);
  const db = getFirestore();

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [deleteVisible, setDeleteVisible] = useState(false);
  const [hasBlockedOtherUser, setOtherUserBlocked] = useState(false);

  const chatRoomId = [user?.id, chat.userId].sort().join('_');

  useLayoutEffect(() => {
    if (!user?.id) return;

    const unsubscribeCurrentUser = onSnapshot(
      doc(db, 'Users', user.id),
      (snapshot) => {
        const currentUserData = snapshot.data() as UserType;
        setOtherUserBlocked(
          currentUserData?.blockedUsers?.includes(chat.userId) || false
        );
      }
    );

    return unsubscribeCurrentUser;
  }, [db, user?.id, chat.userId]);

  const onReport = () => push({ pathname: '/profile/report-problem' });

  const handleBlockUser = async () => {
    try {
      const userRef = doc(db, 'Users', user?.id || '');
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();

      const blockedUsers = userData?.blockedUsers || [];
      if (!blockedUsers.includes(chat.userId)) {
        blockedUsers.push(chat.userId);
        await updateDoc(userRef, { blockedUsers });
        setOtherUserBlocked(true);
        toast.success(
          "You've blocked this user and will no longer receive messages from them."
        );
      }
    } catch (error) {
      console.error('Error blocking user: ', error);
    }
  };

  const handleUnblockUser = async () => {
    try {
      const userRef = doc(db, 'Users', user?.id || '');
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();

      const blockedUsers: string[] = userData?.blockedUsers || [];
      const updatedBlockedUsers = blockedUsers.filter(
        (id) => id !== chat.userId
      );

      await updateDoc(userRef, { blockedUsers: updatedBlockedUsers });
      setOtherUserBlocked(false);
      toast.success(
        "You've unblocked this user and can now receive messages from them again."
      );
    } catch (error) {
      console.error('Error unblocking user: ', error);
    }
  };

  const deleteChat = async (): Promise<void> => {
    if (!user?.id || !chat.userId) return;

    setLoading(true);
    const batch = writeBatch(db);

    try {
      const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
      const chatRoomDoc = await getDoc(chatRoomRef);
      const chatRoomData = chatRoomDoc.data();

      if (chatRoomData) {
        const chatRoomFields = Object.keys(chatRoomData);
        const updateObject: any = {};

        chatRoomFields.forEach((field) => {
          if (field !== 'id' && field !== 'participants') {
            updateObject[field] = deleteField();
          }
        });

        updateObject.lastDeleted = serverTimestamp();
        updateObject.lastDeletedBy = user.id;
        batch.update(chatRoomRef, updateObject);
      }

      const messagesCollectionRef = collection(chatRoomRef, 'Messages');
      const messagesSnapshot = await getDocs(messagesCollectionRef);
      messagesSnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      const currentUserChatRef = doc(
        collection(doc(db, 'UserChats', user.id), 'Chats'),
        chat.userId
      );
      const otherUserChatRef = doc(
        collection(doc(db, 'UserChats', chat.userId), 'Chats'),
        user.id
      );

      batch.delete(currentUserChatRef);
      batch.delete(otherUserChatRef);

      await batch.commit();
      toast.success('Chat deleted successfully');
    } catch (error) {
      console.error('Error deleting chat:', error);
      toast.error('Failed to delete chat. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return {
    favourited,
    toggleFavourite,
    hasBlockedOtherUser,
    confirmVisible,
    setConfirmVisible,
    deleteVisible,
    setDeleteVisible,
    chatRoomId,
    onReport,
    handleBlockUser,
    handleUnblockUser,
    deleteChat,
  };
};
