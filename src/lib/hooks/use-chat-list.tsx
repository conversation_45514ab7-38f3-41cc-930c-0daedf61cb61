import {
  collection,
  doc,
  getFirestore,
  onSnapshot,
} from '@react-native-firebase/firestore';
import { useCallback, useEffect, useState } from 'react';

import type { ChatListType, UserType } from '@/types';

import { useLoggedInUser } from './use-logged-in-user';
import { getTimeValue } from '..';

export const useChatList = () => {
  const { data: currentUser } = useLoggedInUser();
  const [chatList, setChatList] = useState<ChatListType[]>([]);
  const [loading, setLoading] = useState(true);

  const subscribeToChats = useCallback(() => {
    if (!currentUser) return;
    const db = getFirestore();
    const userChatsRef = doc(db, 'UserChats', currentUser.id || '');
    const chatsRef = collection(userChatsRef, 'Chats');

    const unsubscribe = onSnapshot(chatsRef, (snapshot) => {
      const chatData: ChatListType[] = [];
      const userStatusSubscriptions: (() => void)[] = [];

      snapshot.forEach((snapshotDoc) => {
        const chat = {
          id: snapshotDoc.id,
          ...snapshotDoc.data(),
        } as ChatListType;
        chatData.push(chat);

        const usersDocRef = doc(db, 'Users', chat.userId);
        const userStatusUnsubscribe = onSnapshot(
          usersDocRef,
          (userSnapshot) => {
            const userData = userSnapshot.data() as UserType;
            setChatList((prevList) => {
              const updatedList = prevList.map((prevChat) =>
                prevChat.id === chat.id
                  ? {
                      ...prevChat,
                      status: userData?.status || 'offline',
                      lastSeen: userData?.lastSeen,
                      hideOnlineStatus: userData?.hideOnlineStatus,
                      hideLastSeen: userData?.hideLastSeen,
                    }
                  : prevChat
              );

              return [...updatedList].sort(
                (a, b) =>
                  getTimeValue(b.lastMessageTime) -
                  getTimeValue(a.lastMessageTime)
              );
            });
          }
        );

        userStatusSubscriptions.push(userStatusUnsubscribe);
      });

      const sortedChats = [...chatData].sort(
        (a, b) =>
          getTimeValue(b.lastMessageTime) - getTimeValue(a.lastMessageTime)
      );

      setChatList(sortedChats);
      setLoading(false);

      return () => {
        userStatusSubscriptions.forEach((unsub) => unsub());
      };
    });

    return () => {
      unsubscribe();
    };
  }, [currentUser]);

  useEffect(() => {
    const unsubscribe = subscribeToChats();
    return () => unsubscribe?.();
  }, [subscribeToChats]);

  return {
    chatList,
    loading,
    setLoading,
  };
};
