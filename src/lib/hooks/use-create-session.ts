import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Location } from '@/types';

// Create the schema and form setup
export function useCreateLiveSession() {
  // const notEmpty = z.string({ required_error: 'Title is required' }).trim();

  const baseSchema = z.object({
    title: z
      .string({ required_error: 'Title is required' })
      .min(5, 'Title should be at least 5 characters long')
      .max(50, 'Title is too Long!'),
    genres: z.array(z.string()),
    enableVideo: z.boolean().default(false),
    location: Location,
    songRequestEnabled: z.boolean().default(true),
    shoutoutEnabled: z.boolean().default(false),
  });

  const requestSchema = baseSchema.extend({
    type: z.literal('REQUEST'),
    cost: z.coerce.number().optional(),
    shoutoutCost: z.coerce.number().optional(),
  });

  const shoutoutSchema = baseSchema.extend({
    type: z.literal('SHOUTOUT'),
    cost: z.coerce.number().optional(),
    shoutoutCost: z.coerce.number().optional(),
  });

  const schema = z
    .discriminatedUnion('type', [requestSchema, shoutoutSchema])
    .superRefine((data, ctx) => {
      if (data.type === 'REQUEST' && data.cost === undefined) {
        ctx.addIssue({
          path: ['cost'],
          code: z.ZodIssueCode.custom,
          message: 'Cost is required',
        });
      }
      if (data.type === 'SHOUTOUT' && data.shoutoutCost === undefined) {
        ctx.addIssue({
          path: ['SHOUTOUT'],
          code: z.ZodIssueCode.custom,
          message: 'Shoutout cost is required',
        });
      }

      if (data.shoutoutEnabled && data.shoutoutCost === undefined) {
        ctx.addIssue({
          path: ['shoutoutCost'],
          code: z.ZodIssueCode.custom,
          message: 'Shoutout cost is required when shoutout is enabled',
        });
      }
      if (data.songRequestEnabled && data.cost === undefined) {
        ctx.addIssue({
          path: ['shoutoutCost'],
          code: z.ZodIssueCode.custom,
          message: 'Cost is required when song request is enabled',
        });
      }

      if (data.cost !== undefined && data.cost < 0) {
        ctx.addIssue({
          path: ['cost'],
          code: z.ZodIssueCode.custom,
          message: 'Cost must be a positive number',
        });
      }

      if (data.shoutoutCost !== undefined && data.shoutoutCost < 0) {
        ctx.addIssue({
          path: ['shoutoutCost'],
          code: z.ZodIssueCode.custom,
          message: 'Shoutout cost must be a positive number',
        });
      }
    });

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      type: 'REQUEST',
      genres: [],
      enableVideo: false,
      location: {
        city: '',
        state: '',
        street: '',
        country: '',
        address: '',
        landmark: '',
        coordinates: {
          lat: 0,
          lng: 0,
        },
      },
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  return {
    formMethods,
    schema,
  };
}

export type CreateLiveFormType = z.infer<
  ReturnType<typeof useCreateLiveSession>['schema']
>;
