import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useCallback, useState } from 'react';
import { Alert, Platform } from 'react-native';

// TODO: Extend mimeType types
export type MimeType =
  | 'application/pdf'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

interface DownloadFileOptions {
  url: string;
  fileName: string;
  mimeType: MimeType;
}

interface UseDownloadFileResult {
  downloadFile: (options: DownloadFileOptions) => Promise<string | null>;
  progress: number;
  isDownloading: boolean;
  error: string | null;
}

export const useDownloadFile = (): UseDownloadFileResult => {
  const [progress, setProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const downloadFile = useCallback(
    async ({
      url,
      fileName,
      mimeType,
    }: DownloadFileOptions): Promise<string | null> => {
      if (!url || !fileName || !mimeType) {
        throw new Error('Missing url, fileName, or mimeType');
      }
      setProgress(0);
      setIsDownloading(true);
      setError(null);

      try {
        const fileUri = `${FileSystem.documentDirectory}${fileName}`;
        let finalFileUri = fileUri;

        const callback = (
          downloadProgress: FileSystem.DownloadProgressData
        ) => {
          const ratio =
            downloadProgress.totalBytesWritten /
            downloadProgress.totalBytesExpectedToWrite;
          const percent = Math.round(ratio * 100);
          setProgress(percent);
        };

        const downloadResumable = FileSystem.createDownloadResumable(
          url,
          fileUri,
          {},
          callback
        );

        const result = await downloadResumable.downloadAsync();

        if (!result) throw new Error('Download failed');

        // 🔥 logs
        console.log('File downloaded to:', result.uri);

        if (Platform.OS === 'android') {
          const permissions =
            await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();

          if (!permissions.granted) {
            Alert.alert(
              'Permission Denied',
              'Storage access permission is required to save the file.'
            );
            return null;
          }

          finalFileUri =
            await FileSystem.StorageAccessFramework.createFileAsync(
              permissions.directoryUri,
              fileName,
              mimeType
            );

          const fileContent = await FileSystem.readAsStringAsync(result.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          await FileSystem.writeAsStringAsync(finalFileUri, fileContent, {
            encoding: FileSystem.EncodingType.Base64,
          });

          console.log('File saved to:', finalFileUri);
        } else {
          // iOS: the file is already in documentDirectory
          if (await Sharing.isAvailableAsync()) {
            await Sharing.shareAsync(result.uri, {
              mimeType,
            });
          }
        }

        return finalFileUri;
      } catch (err: any) {
        console.error('Download error:', err);
        setError(err.message || 'Unknown error');
        return null;
      } finally {
        setIsDownloading(false);
      }
    },
    []
  );

  return {
    downloadFile,
    progress,
    isDownloading,
    error,
  };
};
