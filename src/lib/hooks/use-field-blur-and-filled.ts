import { useState } from 'react';
import { type FieldPath, useFormContext } from 'react-hook-form';

export type FieldState = Record<string, boolean>;

export type FieldStatus = {
  isValid: boolean;
  isValidOptional: boolean;
  isBlurred: boolean;
  isFilledAndValid: boolean;
  isFilledAndBlurred: boolean;
  value: any;
  error?: string;
  isDirty: boolean;
  isTouched: boolean;
};

export function useFieldBlurAndFilled<TFormValues extends Record<string, any>>(
  fieldNames: FieldPath<TFormValues>[]
) {
  // Initialize blurred state for all fields
  const initialState = fieldNames.reduce<FieldState>((acc, fieldName) => {
    acc[fieldName] = true;
    return acc;
  }, {});

  const [blurredFields, setBlurredFields] = useState<FieldState>(initialState);
  const { watch, formState, getFieldState } = useFormContext<TFormValues>();

  // Use a more specific type for fieldStates
  type FieldStatesType = {
    [K in FieldPath<TFormValues>]?: FieldStatus;
  };

  // Get the field states
  const fieldStates = fieldNames.reduce<FieldStatesType>((acc, fieldName) => {
    const { invalid, error, isDirty, isTouched } = getFieldState(
      fieldName,
      formState
    );
    const value = watch(fieldName);
    const isValid = !!value && !invalid;
    const isValidOptional = !invalid;
    const isBlurred = blurredFields[fieldName];

    acc[fieldName] = {
      isValid,
      isBlurred,
      isFilledAndValid: isValid,
      isFilledAndBlurred: isValid && isBlurred,
      value,
      isValidOptional,
      error: error?.message,
      isDirty,
      isTouched,
    };

    return acc;
  }, {}) as {
    [K in FieldPath<TFormValues>]: FieldStatus;
  };

  // Handle blur for a specific field
  const handleFieldBlur = (fieldName: FieldPath<TFormValues>) => {
    setBlurredFields((prev) => ({
      ...prev,
      [fieldName]: true,
    }));
  };

  // Handle onblur (focus) for a specific field
  const handleFieldUnBlur = (fieldName: FieldPath<TFormValues>) => {
    setBlurredFields((prev) => ({
      ...prev,
      [fieldName]: false,
    }));
  };

  return {
    fieldStates,
    handleFieldBlur,
    handleFieldUnBlur,
    blurredFields,
  };
}
