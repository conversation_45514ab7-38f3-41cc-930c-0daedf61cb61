import {
  doc,
  getDoc,
  getFirestore,
  onSnapshot,
  Timestamp,
} from '@react-native-firebase/firestore';
import { useCallback, useEffect, useRef, useState } from 'react';

import type { UserType } from '@/types';

type User = {
  id: string;
  username: string;
  profileImageUrl: string;
  fullName: string;
};

interface UseFirestoreUserResult {
  user: UserType | null;
  isInFirestore: boolean;
  loading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
}

export const useFirestoreUser = (
  userId: string | null,
  fallbackUser?: User
): UseFirestoreUserResult => {
  const [user, setUser] = useState<UserType | null>(null);
  const [isInFirestore, setIsInFirestore] = useState(false);
  const [loading, setLoading] = useState<boolean>(!!userId);
  const [error, setError] = useState<Error | null>(null);

  const unsubscribeRef = useRef<() => void>(() => {});

  const db = getFirestore();

  const transformDatabaseUser = useCallback((dbUser: User): UserType => {
    return {
      id: dbUser.id,
      displayName: dbUser.username || dbUser.fullName,
      photoURL: dbUser.profileImageUrl,
      createdAt: Timestamp.fromDate(new Date()),
      status: 'offline',
    };
  }, []);

  const fetchUserOnce = useCallback(async () => {
    if (!userId) return;
    setLoading(true);
    setError(null);

    try {
      const snapshot = await getDoc(doc(db, 'Users', userId));

      if (snapshot.exists()) {
        setIsInFirestore(true);
        setUser(snapshot.data() as UserType);
      } else if (fallbackUser) {
        setIsInFirestore(false);
        setUser(transformDatabaseUser(fallbackUser));
      } else {
        setIsInFirestore(false);
        setUser(null);
      }
    } catch (err) {
      setError(err as Error);
      setUser(null);
      setIsInFirestore(false);
    } finally {
      setLoading(false);
    }
  }, [db, userId, fallbackUser, transformDatabaseUser]);

  const subscribeToUser = useCallback(() => {
    if (!userId) return;

    unsubscribeRef.current?.();

    const unsubscribe = onSnapshot(
      doc(db, 'Users', userId),
      (snapshot) => {
        if (snapshot.exists()) {
          setIsInFirestore(true);
          setUser(snapshot.data() as UserType);
        } else if (fallbackUser) {
          setIsInFirestore(false);
          setUser(transformDatabaseUser(fallbackUser));
        } else {
          setIsInFirestore(false);
          setUser(null);
        }
        setLoading(false);
      },
      (err) => {
        setError(err as Error);
        setLoading(false);
      }
    );

    unsubscribeRef.current = unsubscribe;
  }, [db, userId, fallbackUser, transformDatabaseUser]);

  useEffect(() => {
    if (!userId) return;

    fetchUserOnce();
    subscribeToUser();

    return () => unsubscribeRef.current?.();
  }, [userId, fetchUserOnce, subscribeToUser]);

  return {
    user,
    isInFirestore,
    loading,
    error,
    refresh: fetchUserOnce,
  };
};
