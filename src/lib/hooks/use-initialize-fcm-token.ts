import { getMessaging } from '@react-native-firebase/messaging';
import { useCallback, useEffect } from 'react';

import { usePatchFcmToken } from '@/api/user';
import {
  GetFCMToken,
  getFCMToken,
  requestUserPermission,
  setFCMToken,
} from '@/lib/notification';

import { useLoggedInUser } from './use-logged-in-user';

export const useInitializeFcmToken = () => {
  const { data: user } = useLoggedInUser();
  const { mutate } = usePatchFcmToken();

  const initializeToken = useCallback(async () => {
    try {
      if (!user?.id || !user.pushNotification) return;

      await requestUserPermission();

      const fcmToken = await GetFCMToken();
      const cachedFcmToken = getFCMToken();

      if (fcmToken && fcmToken !== cachedFcmToken) {
        mutate({ userId: user.id, fcmToken });
      }
    } catch (error) {
      console.error('FCM token initialization error:', error);
    }
  }, [user?.id, mutate]);

  useEffect(() => {
    initializeToken();

    const unsubscribeRefresh = getMessaging().onTokenRefresh(
      async (newToken) => {
        await setFCMToken(newToken);
        if (user?.id) mutate({ userId: user.id, fcmToken: newToken });
      }
    );

    return () => {
      unsubscribeRefresh();
    };
  }, [initializeToken, user?.id]);
};
