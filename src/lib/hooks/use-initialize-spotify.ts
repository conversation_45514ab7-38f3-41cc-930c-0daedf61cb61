import { Env } from '@env';
import dayjs from 'dayjs';
import React from 'react';
import { toast } from 'sonner-native';

import { type SpotifyAccessData, useSpotifyInitiateToken } from '@/api/session';
import { getSpotifyToken, removeSpotifyToken } from '@/lib/session/utils';

import { useSession } from '../session';

export const useInitializeSpotify = () => {
  const setSpotifyData = useSession.use.setSpotifyData();

  const { mutate } = useSpotifyInitiateToken();

  React.useEffect(() => {
    const savedAccessToken = getSpotifyToken();

    const fetchSpotifyData = () => {
      mutate(
        {
          clientId: Env.SPOTIFY_CLIENT_ID,
          clientSecret: Env.SPOTIFY_CLIENT_SECRET,
        },
        {
          onSuccess: (data) => {
            console.log('🚀 ~ fetchSpotifyData ~ data:', data);
            const tokenObj: SpotifyAccessData = {
              ...data,
              expirationDate: dayjs().add(data.expires_in, 'second'),
            };
            setSpotifyData(tokenObj);
          },
          onError: (error) => toast.error(error.message),
        }
      );
    };

    if (!savedAccessToken) {
      console.log('🚀 ~ Fresh ~ fetchSpotifyData');
      fetchSpotifyData();
    } else {
      if (dayjs(savedAccessToken.expirationDate).diff(dayjs()) > 0) {
        setSpotifyData(savedAccessToken);
      } else {
        console.log('🚀 ~ Expired ~ fetchSpotifyData');
        removeSpotifyToken();
        fetchSpotifyData();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};
