import { useMMKVBoolean } from 'react-native-mmkv';

import { storage } from '../storage';

const IS_FIRST_LOGIN = 'IS_FIRST_LOGIN';

export const useIsFirstLogin = () => {
  const [isFirstLogin, setIsFirstLogin] = useMMKVBoolean(
    IS_FIRST_LOGIN,
    storage
  );
  if (isFirstLogin === undefined) {
    return [true, setIsFirstLogin] as const;
  }
  return [isFirstLogin, setIsFirstLogin] as const;
};
