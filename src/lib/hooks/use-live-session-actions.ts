import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { toast } from 'sonner-native';

import { useEndLiveSession, useLeaveLiveSession } from '@/api/session';
import { useSession } from '@/lib/session';
import { removeLiveSession } from '@/lib/session/utils';

import { useGetLiveSessionQuery } from './queries/use-get-live-session-query';

export const useLiveSessionActions = (sessionId: string) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const resetDjSession = useSession.use.resetDjSession();
  const { data: sessionData } = useGetLiveSessionQuery(sessionId);
  const sessionInfo = sessionData?.djSession;

  const [confirmEndVisible, setConfirmEndVisible] = useState(false);

  const { mutate: endLiveSession, isPending: isEnding } = useEndLiveSession();
  const { mutate: leaveLiveSession, isPending: isLeaving } =
    useLeaveLiveSession();

  const onEndLiveSession = useCallback(
    async (leaveAgoraStream?: () => void) => {
      endLiveSession(
        { sessionId },
        {
          onSuccess: async () => {
            if (sessionInfo?.enableVideo && leaveAgoraStream) {
              leaveAgoraStream();
            }
            removeLiveSession();
            resetDjSession();
            setConfirmEndVisible(false);
            await queryClient.invalidateQueries({ queryKey: ['getHomeData'] });
            await queryClient.invalidateQueries({ queryKey: ['getSessions'] });
            router.dismissTo('/');
          },
          onError: (error) => toast.error(error.message),
        }
      );
    },
    [endLiveSession, sessionInfo, sessionId, resetDjSession, router]
  );

  const onLeaveLiveSession = useCallback(
    async (leaveAgoraStream?: () => void) => {
      leaveLiveSession(
        { sessionId },
        {
          onSuccess: () => {
            if (sessionInfo?.enableVideo && leaveAgoraStream) {
              leaveAgoraStream();
            }
            removeLiveSession();
            resetDjSession();
            toast.success('live session exited');
            router.dismissTo('/');
          },
          onError: (error) => toast.error(error.message),
        }
      );
    },
    [leaveLiveSession, sessionInfo, sessionId, resetDjSession, router]
  );

  return {
    isEnding,
    isLeaving,
    confirmEndVisible,
    setConfirmEndVisible,
    onEndLiveSession,
    onLeaveLiveSession,
  };
};
