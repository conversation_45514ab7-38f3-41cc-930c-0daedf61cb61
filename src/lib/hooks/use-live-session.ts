import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useEffect } from 'react';
import { toast } from 'sonner-native';

import {
  type DJLiveSession,
  type LiveSessionInfoResponse,
  type LiveSessionInterface,
  type UserLiveSession,
} from '@/api/session';

import { useSession } from '../session';
import { removeLiveSession } from '../session/utils';
import { useGetLiveSessionQuery } from './queries/use-get-live-session-query';
import { useGetLiveSessionQueueQuery } from './queries/use-get-live-session-queue-query';
import { useRefetchOnFocus } from './use-refetch-on-focus';

interface UseLiveSessionReturn {
  // Data
  sessionData?: LiveSessionInfoResponse;
  queueData?: DJLiveSession & UserLiveSession;

  // Loading states
  isSessionLoading: boolean;
  isQueueLoading: boolean;
  isLoading: boolean;

  // Error states
  sessionError: Error | null;
  queueError: Error | null;

  // Status
  isSessionEnded: boolean;

  // Actions
  retrySession: () => void;
  retryQueue: () => void;
}

export const useLiveSession = (sessionId: string): UseLiveSessionReturn => {
  const queryClient = useQueryClient();
  const router = useRouter();

  // Local state
  const resetDjSession = useSession.use.resetDjSession();
  const removeEndedLiveSession = useSession.use.removeEndedLiveSession();
  const setLiveSession = useSession.use.setLiveSession();

  // Queries
  const {
    data: sessionData,
    isLoading: isSessionLoading,
    error: sessionError,
    refetch: refetchSession,
  } = useGetLiveSessionQuery(sessionId);

  const {
    data: queueData,
    isLoading: isQueueLoading,
    error: queueError,
    refetch: refetchQueue,
  } = useGetLiveSessionQueueQuery(sessionId, {
    enabled: !!sessionData && sessionData.djSession?.status !== 'ENDED',
  });

  useRefetchOnFocus(() => {
    refetchSession();
    refetchQueue();
  }, 0);

  // Handle session data changes
  useEffect(() => {
    if (sessionError) {
      toast.error(sessionError.message);
      return;
    }

    if (!sessionData) return;

    const { djSession, questersOnSession } = sessionData;

    if (djSession.status === 'ENDED') {
      removeEndedLiveSession(sessionId);
      removeLiveSession();
      resetDjSession();
      queryClient.invalidateQueries({ queryKey: ['getHomeData'] });
      queryClient.invalidateQueries({ queryKey: ['getSessions'] });

      router.dismissTo('/');
      return;
    }

    setLiveSession({
      djSession,
      questersOnSession,
    } as unknown as LiveSessionInterface);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionData, sessionError, sessionId, queryClient]);

  // Handle queue data changes
  useEffect(() => {
    if (queueError) {
      toast.error(queueError.message);
      return;
    }
  }, [queueError]);

  return {
    // Data
    sessionData,
    queueData,

    // Loading states
    isSessionLoading,
    isQueueLoading,
    isLoading: isSessionLoading || isQueueLoading,

    // Error states
    sessionError,
    queueError,

    // Status
    isSessionEnded: sessionData?.djSession?.status === 'ENDED',

    // Actions
    retrySession: refetchSession,
    retryQueue: refetchQueue,
  };
};
