import {
  collection,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  onSnapshot,
  query,
  serverTimestamp,
  updateDoc,
  where,
  writeBatch,
} from '@react-native-firebase/firestore';
import { useEffect, useState } from 'react';
import { toast } from 'sonner-native';

import { useSendPushNotification } from '@/api/notifications';
import type {
  ChatListType,
  MessageRequest,
  NotificationType,
  UserType,
} from '@/types';

import { useLoggedInUser } from './use-logged-in-user';

export const useMessageRequests = () => {
  const [requests, setRequests] = useState<MessageRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const { data: currentUser } = useLoggedInUser();

  const { mutate: sendPushNotification } = useSendPushNotification();

  const db = getFirestore();

  useEffect(() => {
    if (!currentUser?.id) {
      setRequests([]);
      setLoading(false);
      return;
    }

    const chatRequestsRef = collection(db, 'ChatRequests');
    const q = query(
      chatRequestsRef,
      where('receiverId', '==', currentUser.id),
      where('status', '==', 'pending')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      if (snapshot) {
        const requestsList: MessageRequest[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          requestsList.push({
            id: doc.id,
            _id: doc.id,
            receiverId: data.receiverId,
            senderId: data.senderId,
            senderName: data.senderName,
            senderAvatar: data.senderAvatar,
            message: data.message,
            text: data.message,
            createdAt: data.createdAt,
            status: data.status,
            showRequestAlert: data.showRequestAlert,
            system: true,
          });
        });

        setRequests(requestsList);
      } else {
        setRequests([]);
      }
      setLoading(false);
    });

    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser?.id]);

  const acceptRequest = async (requestId: string) => {
    try {
      const requestRef = doc(db, 'ChatRequests', requestId);
      const requestSnap = await getDoc(requestRef);

      if (!requestSnap.exists()) {
        return;
      }

      const requestData = requestSnap.data() as MessageRequest;
      const senderId = requestData.senderId;

      const chatRoomId = [currentUser?.id, senderId].sort().join('_');
      const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);

      const senderRef = doc(db, 'Users', senderId);
      const senderSnap = await getDoc(senderRef);
      const senderData = senderSnap.data() as UserType;

      const batch = writeBatch(db);

      batch.update(requestRef, {
        status: 'accepted',
      } as Partial<MessageRequest>);

      const messagesSnapshot = await getDocs(
        collection(chatRoomRef, 'Messages')
      );

      messagesSnapshot.docs.forEach((messageDoc) => {
        const messageData = messageDoc.data();
        if (messageData.senderId === senderId) {
          batch.update(messageDoc.ref, {
            received: true,
            read: true,
          });
        }
      });

      const currentUserChatRef = doc(
        collection(doc(db, 'UserChats', currentUser?.id || ''), 'Chats'),
        senderId
      );

      batch.set(
        currentUserChatRef,
        {
          userId: senderId,
          userName: requestData?.senderName || senderData.displayName,
          userAvatar: requestData?.senderAvatar || senderData.photoURL,
          lastMessage: requestData?.message || '',
          lastMessageTime: serverTimestamp(),
          unreadCount: 0,
        } as Partial<ChatListType>,
        { merge: true }
      );

      const senderUserChatRef = doc(
        collection(doc(db, 'UserChats', senderId), 'Chats'),
        currentUser?.id
      );

      batch.update(senderUserChatRef, {
        isPendingRequest: false,
        requestStatus: 'accepted',
      });

      const systemMessageRef = doc(collection(chatRoomRef, 'Messages'));
      batch.set(systemMessageRef, {
        id: systemMessageRef.id,
        type: 'system',
        text: `${currentUser?.username} accepted your chat request`,
        createdAt: serverTimestamp(),
        senderId: currentUser?.id,
        read: false,
        received: false,
      });
      await batch.commit();

      sendPushNotification({
        username: requestData.senderName,
        title: 'Chat Request Accepted! 🎉',
        body: `${currentUser?.username} accepted your chat request`,
        image: currentUser?.profileImageUrl,
        data: {
          type: 'PRIVATE_CHAT' as NotificationType,
          userId: currentUser?.id,
          username: currentUser?.username,
          userAvatar: currentUser?.profileImageUrl,
          chatRoomId,
        },
      });

      toast.success(`Accepted chat request from ${requestData.senderName}`);
    } catch (error) {
      console.error('Error accepting request:', error);
      toast.error('Failed to accept request. Please try again.');
    }
  };

  const declineRequest = async (requestId: string) => {
    try {
      const requestRef = doc(db, 'ChatRequests', requestId);
      const requestSnap = await getDoc(requestRef);

      if (!requestSnap.exists()) {
        return;
      }
      const requestData = requestSnap.data() as MessageRequest;
      const senderId = requestData.senderId;

      await updateDoc(requestRef, { status: 'declined' });

      const chatRoomId = [currentUser?.id, senderId].sort().join('_');
      const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
      const chatRoomSnap = await getDoc(chatRoomRef);

      if (chatRoomSnap.exists()) {
        const messagesRef = collection(chatRoomRef, 'Messages');
        const messagesSnap = await getDocs(messagesRef);
        const batch = writeBatch(db);
        messagesSnap.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });

        batch.delete(chatRoomRef);

        const currentUserChatRef = doc(
          collection(doc(db, 'UserChats', currentUser?.id || ''), 'Chats'),
          senderId
        );

        const otherUserChatRef = doc(
          collection(doc(db, 'UserChats', senderId), 'Chats'),
          currentUser?.id || ''
        );

        batch.delete(currentUserChatRef);
        batch.delete(otherUserChatRef);

        await batch.commit();
      }

      // Send push notification for declined request
      sendPushNotification({
        username: requestData.senderName,
        title: 'Chat Request Declined',
        body: `${currentUser?.username} declined your chat request`,
        image: currentUser?.profileImageUrl,
        data: {
          type: 'PRIVATE_CHAT' as NotificationType,
          userId: currentUser?.id,
          username: currentUser?.username,
          userAvatar: currentUser?.profileImageUrl,
          chatRoomId,
        },
      });
    } catch (error) {
      console.error('Error declining request:', error);
    }
  };

  return {
    requests,
    loading,
    acceptRequest,
    declineRequest,
  };
};
