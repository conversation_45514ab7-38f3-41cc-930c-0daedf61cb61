import { useRouter } from 'expo-router';
import { usePaystack } from 'react-native-paystack-webview';

import { queryClient } from '@/api';

import { useAuth } from '../auth';
import { usePurchaseTicketContext } from '../contexts/purchase-ticket-context';
import { type SelectedTicket } from './use-purchase-ticket';

export const usePaystackCheckout = ({
  amount,
  discountCode,
}: {
  amount: number;
  discountCode?: string;
}) => {
  const router = useRouter();
  const { email, fullName, id: userId } = useAuth.use.user()!;
  const { event, getSelectedTicketsArray } = usePurchaseTicketContext();
  const selectedTickets = getSelectedTicketsArray();
  const { popup } = usePaystack();

  const formatMetadata = (metadata: {
    purpose: string;
    user: { fullName: string; email: string };
    eventId: string;
    userId: string;
    breakdown: Omit<SelectedTicket, 'cost'>[];
    discountCode?: string;
  }): {
    custom_fields: {
      display_name: string;
      variable_name: string;
      value: string | number;
    }[];
  } => {
    return {
      custom_fields: [
        {
          display_name: 'Purpose',
          variable_name: 'purpose',
          value: metadata.purpose,
        },
        {
          display_name: 'User Details',
          variable_name: 'user',
          value: JSON.stringify(metadata.user),
        },
        {
          display_name: 'Event ID',
          variable_name: 'event_id',
          value: metadata.eventId,
        },
        {
          display_name: 'User ID',
          variable_name: 'userId',
          value: metadata.userId,
        },
        {
          display_name: 'Ticket Breakdown',
          variable_name: 'breakdown',
          value: JSON.stringify(metadata.breakdown),
        },
        ...(metadata.discountCode
          ? [
              {
                display_name: 'Discount Code',
                variable_name: 'discountCode',
                value: metadata.discountCode,
              },
            ]
          : []),
      ],
    };
  };

  const checkout = () => {
    const metadata = formatMetadata({
      purpose: 'EVENT_TICKET_PURCHASE',
      user: { email, fullName },
      eventId: event?.id || '',
      breakdown: selectedTickets.map(({ category, quantity, isPresale }) => ({
        category,
        quantity,
        isPresale,
      })),
      userId,
      discountCode,
    });

    popup.checkout({
      email,
      amount,
      onSuccess: (res) => {
        queryClient.invalidateQueries({
          queryKey: ['getUserTickets', userId],
        });
        queryClient.invalidateQueries({
          queryKey: ['getEvent', event?.id],
        });
        queryClient.invalidateQueries({
          queryKey: ['getEventWithSlug', event?.slug],
        });
        router.replace({
          pathname: '/events/[id]/tickets/success',
          params: { id: event?.id || '' },
        });
      },
      onCancel: () => {
        console.log('User cancelled');
      },
      metadata,
    });
  };

  return { checkout };
};
