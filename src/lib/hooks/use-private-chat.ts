import {
  collection,
  deleteDoc,
  deleteField,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  increment,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
  writeBatch,
} from '@react-native-firebase/firestore';
import { useRouter } from 'expo-router';
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { toast } from 'sonner-native';

import { useSendPushNotification } from '@/api/notifications';
import type {
  ChatListType,
  ChatRequestType,
  Message,
  MessageRequest,
  MessageType,
  NotificationType,
  UserType,
} from '@/types';

import { removeUndefined } from '..';
import { useLoggedInUser } from './use-logged-in-user';

export const usePrivateChat = (
  userId: string,
  userName: string,
  userAvatar: string
) => {
  const { data: currentUser } = useLoggedInUser();
  const router = useRouter();

  const [messages, setMessages] = useState<MessageType[]>([]);
  const [chatUser, setChatUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasBlockedOtherUser, setOtherUserBlocked] = useState(false);
  const [isBlockedByOtherUser, setIsBlockedByOtherUser] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [requestAccepted, setRequestAccepted] = useState(false);
  const [hasPendingRequest, setHasPendingRequest] = useState(false);
  const [_, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | number>(null);
  const [replyToMessage, setReplyToMessage] = useState<MessageType | null>(
    null
  );

  const { mutate: sendPushNotification } = useSendPushNotification();

  const db = getFirestore();

  const isChatBlocked = useMemo(
    () => hasBlockedOtherUser || isBlockedByOtherUser,
    [hasBlockedOtherUser, isBlockedByOtherUser]
  );

  const chatRoomId = useMemo(
    () => [currentUser?.id, userId].sort().join('_'),
    [currentUser?.id, userId]
  );

  const subscribeToChatUser = useCallback(() => {
    const usersDocRef = doc(db, 'Users', userId);
    const userStatusUnsubscribe = onSnapshot(usersDocRef, (userSnapshot) => {
      const userData = userSnapshot.data() as UserType;
      setChatUser(userData);
    });

    return () => {
      userStatusUnsubscribe();
    };
  }, [db, userId]);

  const fetchMessages = useCallback(() => {
    const messagesRef = collection(
      doc(db, 'ChatRooms', chatRoomId),
      'Messages'
    );
    const messagesQuery = query(messagesRef, orderBy('createdAt', 'desc'));

    return onSnapshot(messagesQuery, async (snapshot) => {
      const fetchedMessages = snapshot.docs
        .map((doc) => {
          const message = doc.data() as Message;
          return {
            _id: doc.id,
            text: message.text || '',
            createdAt:
              typeof (message.createdAt as any).toDate === 'function'
                ? message.createdAt?.toDate()
                : new Date(),
            user: {
              _id: message.senderId,
              name: message.senderName,
              avatar: message.senderAvatar || '',
            },
            image: message.image,
            sent: true,
            received: message.received || false,
            read: message.read || false,
            system: message.system || false,
            requestType:
              (message.requestType as MessageType['requestType']) || undefined,
            ...(message.replyToMessage && {
              replyToMessage: {
                _id: message.replyToMessage._id,
                text: message.replyToMessage.text,
                user: {
                  _id: message.replyToMessage.user?._id,
                  name: message.replyToMessage.user?.name,
                  avatar: message.replyToMessage.user?.avatar,
                },
              },
            }),
          };
        })
        .filter(
          (msg) =>
            !msg.system ||
            (['request_accepted', 'request_declined', 'new_request'].includes(
              msg.requestType || ''
            ) &&
              [currentUser?.id, userId].includes(msg.user._id))
        );

      setMessages(fetchedMessages);
      setLoading(false);

      if (requestAccepted) {
        // Identify unread messages sent by the other user
        const unreadMessages = fetchedMessages.filter(
          (msg) => msg.user._id === userId && !msg.read
        );

        if (unreadMessages.length > 0) {
          const batch = writeBatch(db);
          unreadMessages.forEach((msg) => {
            const messageRef = doc(
              collection(doc(db, 'ChatRooms', chatRoomId), 'Messages'),
              msg._id
            );
            batch.update(messageRef, { received: true, read: true });
          });

          const userChatRef = doc(
            collection(doc(db, 'UserChats', currentUser?.id || ''), 'Chats'),
            userId
          );
          batch.update(userChatRef, {
            unreadCount: 0,
          } as Partial<ChatListType>);

          await batch.commit().catch(console.error);
        }
      }
    });
  }, [chatRoomId, currentUser?.id, db, userId, requestAccepted]);

  useLayoutEffect(() => {
    const checkBlockStatus = () => {
      // Listen for changes to the other user's block list
      const unsubscribeOtherUser = onSnapshot(
        doc(db, 'Users', userId),
        (snapshot) => {
          const otherUserData = snapshot.data() as UserType;

          if (otherUserData?.blockedUsers?.includes(currentUser?.id || '')) {
            setIsBlockedByOtherUser(true);
          } else {
            setIsBlockedByOtherUser(false);
          }
        }
      );

      // Listen for changes to the current user's block list
      const unsubscribeCurrentUser = onSnapshot(
        doc(db, 'Users', currentUser?.id || ''),
        (snapshot) => {
          const currentUserData = snapshot.data() as UserType;

          if (currentUserData?.blockedUsers?.includes(userId)) {
            setOtherUserBlocked(true);
          } else {
            setOtherUserBlocked(false);
          }
        }
      );

      return () => {
        unsubscribeOtherUser();
        unsubscribeCurrentUser();
      };
    };

    const checkChatRequestStatus = async () => {
      const requestQuery1 = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', currentUser?.id),
        where('receiverId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const requestQuery2 = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', userId),
        where('receiverId', '==', currentUser?.id),
        orderBy('createdAt', 'desc')
      );

      const [snapshot1, snapshot2] = await Promise.all([
        getDocs(requestQuery1),
        getDocs(requestQuery2),
      ]);

      if (!snapshot1.empty) {
        // Current user sent a request - get the most recent one
        const requestData = snapshot1.docs[0].data() as ChatRequestType;
        setHasPendingRequest(requestData.status === 'pending');
        if (requestData.status === 'accepted') {
          setRequestAccepted(true);
        }
      } else if (!snapshot2.empty) {
        // Current user received a request - get the most recent one
        const requestData = snapshot2.docs[0].data() as ChatRequestType;
        setHasPendingRequest(requestData.status === 'pending');
        if (requestData.status === 'accepted') {
          setRequestAccepted(true);
        }
      } else {
        // No request exists, check if there's an existing chat room
        const hasExistingRoom = await hasExistingChatRoom();
        if (hasExistingRoom) {
          setRequestAccepted(true);
        } else {
          setRequestAccepted(false);
          setHasPendingRequest(false);
        }
      }
    };

    const hasExistingChatRoom = async () => {
      const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
      const chatRoomDoc = await getDoc(chatRoomRef);
      return chatRoomDoc.exists();
    };

    const setupChat = async () => {
      checkBlockStatus();
      await checkChatRequestStatus();

      if (!isChatBlocked && (requestAccepted || hasPendingRequest)) {
        const unsubscribeMessages = fetchMessages();
        return () => {
          unsubscribeMessages();
        };
      } else {
        setLoading(false);
        return undefined;
      }
    };

    let unsubscribeChat: (() => void) | undefined;
    setupChat().then((cleanupFn) => {
      unsubscribeChat = cleanupFn;
    });

    const unsubscribeChatUser = subscribeToChatUser();

    const listenForRequestChanges = () => {
      // Listen for requests sent by current user
      const sentRequestsQuery = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', currentUser?.id),
        where('receiverId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const sentRequestsUnsubscribe = onSnapshot(
        sentRequestsQuery,
        (snapshot) => {
          if (!snapshot.empty) {
            const requestData = snapshot.docs[0].data() as MessageRequest;
            setHasPendingRequest(requestData.status === 'pending');

            if (requestData.status === 'accepted') {
              setRequestAccepted(true);
              setHasPendingRequest(false);

              toast.success(`${userName} accepted your chat request! 🎉`);

              deleteDoc(doc(db, 'ChatRequests', snapshot.docs[0].id));
            } else if (requestData.status === 'declined') {
              toast.info(`${userName} declined your chat request`);

              router.back();
              deleteDoc(doc(db, 'ChatRequests', snapshot.docs[0].id));
            }
          }
        }
      );

      const receivedRequestsQuery = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', userId),
        where('receiverId', '==', currentUser?.id),
        orderBy('createdAt', 'desc')
      );

      const receivedRequestsUnsubscribe = onSnapshot(
        receivedRequestsQuery,
        (snapshot) => {
          if (!snapshot.empty) {
            const requestData = snapshot.docs[0].data() as MessageRequest;

            if (requestData.status === 'pending') {
              setHasPendingRequest(true);
              toast(`${userName} sent you a message request`, {
                description: requestData.message,
                action: {
                  label: 'View',
                  onClick: () => {},
                },
              });
            } else if (requestData.status === 'accepted') {
              setRequestAccepted(true);
              setHasPendingRequest(false);
            }
          }
        }
      );

      return () => {
        sentRequestsUnsubscribe();
        receivedRequestsUnsubscribe();
      };
    };

    const unsubscribeFromRequests = listenForRequestChanges();

    return () => {
      if (unsubscribeChat) {
        unsubscribeChat();
      }
      if (unsubscribeFromRequests) {
        unsubscribeFromRequests();
      }
      unsubscribeChatUser();
    };
  }, [
    currentUser,
    userId,
    isChatBlocked,
    requestAccepted,
    hasPendingRequest,
    userName,
    fetchMessages,
    chatRoomId,
  ]);

  useEffect(() => {
    if (!chatRoomId || !requestAccepted) {
      return;
    }

    const unsubscribe = onSnapshot(
      doc(db, 'ChatRooms', chatRoomId),
      (snapshot) => {
        const data = snapshot.data();
        if (data) {
          const typingStatus = data.typingUsers || {};
          setOtherUserTyping(!!typingStatus[userId]);
        }
      }
    );

    return () => unsubscribe();
  }, [chatRoomId, userId, requestAccepted, db]);

  const handleTypingStatus = useCallback(
    async (isTypingNow: boolean) => {
      if (!currentUser?.id || !chatRoomId || !requestAccepted) {
        return;
      }

      setIsTyping(isTypingNow);

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      try {
        const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
        const chatRoomDoc = await getDoc(chatRoomRef);
        const chatRoom = chatRoomDoc.data();

        if (!chatRoom?.lastMessage) {
          return;
        }

        const batch = writeBatch(db);

        batch.update(chatRoomRef, {
          [`typingUsers.${currentUser.id}`]: isTypingNow,
        });

        const otherUserChatRef = doc(
          collection(doc(db, 'UserChats', userId), 'Chats'),
          currentUser.id
        );

        batch.update(otherUserChatRef, {
          isTyping: isTypingNow,
        });

        await batch.commit();

        if (isTypingNow) {
          typingTimeoutRef.current = setTimeout(async () => {
            try {
              const chatRoomCheckDoc = await getDoc(chatRoomRef);
              if (!chatRoomCheckDoc.exists()) {
                setIsTyping(false);
                return;
              }

              const clearBatch = writeBatch(db);

              clearBatch.update(chatRoomRef, {
                [`typingUsers.${currentUser.id}`]: false,
              });

              clearBatch.update(otherUserChatRef, {
                isTyping: false,
              });

              await clearBatch.commit();
              setIsTyping(false);
            } catch (error) {
              console.log(
                'Error clearing typing status (chat may have been deleted):',
                error
              );
              setIsTyping(false);
            }
          }, 1500);
        }
      } catch (error) {
        console.log(
          'Error updating typing status (chat may have been deleted):',
          error
        );
        setIsTyping(false);
      }
    },
    [currentUser?.id, chatRoomId, requestAccepted, userId, db]
  );

  const markMessagesAsRead = async (readMessages: MessageType[]) => {
    if (!requestAccepted) return;

    const unreadMessages = readMessages.filter(
      (msg) => msg.user?._id === userId && !msg.read
    );

    for (const message of unreadMessages) {
      const messageRef = doc(
        collection(doc(db, 'ChatRooms', chatRoomId), 'Messages'),
        message._id.toString()
      );

      await updateDoc(messageRef, { read: true });
    }

    if (unreadMessages.length > 0) {
      const userChatRef = doc(
        collection(doc(db, 'UserChats', currentUser?.id || ''), 'Chats'),
        userId
      );

      await updateDoc(userChatRef, {
        unreadCount: 0,
      } as Partial<ChatListType>);
    }
  };

  const onSend = useCallback(
    async (newMessages: MessageType[] = []) => {
      if (!newMessages.length || !currentUser || isChatBlocked || isSending)
        return;

      const message = newMessages[0];

      const optimisticMessage: MessageType = removeUndefined({
        ...message,
        ...(replyToMessage && {
          replyToMessage: {
            _id: replyToMessage._id?.toString(),
            text: replyToMessage.text,
            user: {
              _id: replyToMessage.user?._id?.toString(),
              name: replyToMessage.user?.name,
              avatar: replyToMessage.user?.avatar?.toString(),
            },
          },
        }),
        pending: true,
        sent: false,
        received: false,
        read: false,
      });

      setMessages((prev) => [optimisticMessage, ...prev]);

      try {
        setIsSending(true);

        if (!requestAccepted && !hasPendingRequest) {
          await createChatRequest(message.text ?? '');
          setMessages((prev) =>
            prev.map((msg) =>
              msg._id === optimisticMessage._id
                ? { ...msg, sent: true, pending: false }
                : msg
            )
          );
          setHasPendingRequest(true);
          toast.success('Message sent! Waiting for acceptance...');
          setIsSending(false);
          return;
        }

        if (requestAccepted) {
          await sendRegularMessage(message, optimisticMessage);
        }

        setReplyToMessage(null);
      } catch (error) {
        console.error('Error sending message: ', error);
        setMessages((prev) =>
          prev.filter((msg) => msg._id !== optimisticMessage._id)
        );
        toast.error('Failed to send message. Please try again.');
      } finally {
        setIsSending(false);
      }
    },
    [
      chatRoomId,
      currentUser,
      isChatBlocked,
      isSending,
      replyToMessage,
      requestAccepted,
      hasPendingRequest,
      userAvatar,
      userId,
      userName,
      db,
    ]
  );

  const createChatRequest = async (messageText: string) => {
    if (!currentUser) return;
    const batch = writeBatch(db);

    const chatRequestRef = doc(collection(db, 'ChatRequests'));
    batch.set(
      chatRequestRef,
      removeUndefined({
        senderId: currentUser.id,
        senderName: currentUser.username,
        senderAvatar: currentUser.profileImageUrl,
        receiverId: userId,
        message: messageText.trim(),
        status: 'pending',
        createdAt: serverTimestamp(),
        showRequestAlert: true,
      }) as MessageRequest
    );

    const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
    batch.set(
      chatRoomRef,
      removeUndefined({
        participants: [currentUser.id, userId],
        createdAt: serverTimestamp(),
        lastMessage: messageText,
        lastMessageTime: serverTimestamp(),
      })
    );

    const messagesRef = collection(chatRoomRef, 'Messages');
    await setDoc(
      doc(messagesRef),
      removeUndefined({
        text: messageText,
        createdAt: serverTimestamp(),
        senderId: currentUser.id,
        senderName: currentUser.username,
        senderAvatar: currentUser.profileImageUrl,
        sent: true,
        received: false,
        read: false,
        system: false,
      })
    );

    const currentUserChatRef = doc(
      collection(doc(db, 'UserChats', currentUser.id), 'Chats'),
      userId
    );

    batch.set(
      currentUserChatRef,
      removeUndefined({
        userId,
        userName,
        userAvatar,
        lastMessage: messageText,
        lastMessageTime: serverTimestamp(),
        unreadCount: 0,
        isPendingRequest: true,
        requestStatus: 'pending',
      }) as Partial<ChatListType>,
      { merge: true }
    );

    await batch.commit();

    sendPushNotification({
      username: userName ?? '',
      title: 'New Message Request',
      body: `${currentUser.username ?? ''}: ${messageText.trim()}`,
      image: currentUser.profileImageUrl ?? '',
      data: {
        type: 'PRIVATE_CHAT' as NotificationType,
        userId: currentUser.id,
        username: currentUser.username ?? '',
        userAvatar: currentUser.profileImageUrl ?? '',
        chatRoomId,
      },
    });
  };

  const sendRegularMessage = async (
    message: MessageType,
    optimisticMessage: MessageType
  ) => {
    if (!currentUser) return;

    const batch = writeBatch(db);
    const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
    const chatRoomDoc = await getDoc(chatRoomRef);

    if (!chatRoomDoc.exists()) {
      batch.set(
        chatRoomRef,
        removeUndefined({
          participants: [currentUser.id, userId],
          createdAt: serverTimestamp(),
          lastMessage: message.text,
          lastMessageTime: serverTimestamp(),
        })
      );
    } else {
      batch.update(
        chatRoomRef,
        removeUndefined({
          lastMessage: message.text,
          lastMessageTime: serverTimestamp(),
        })
      );
    }

    const messageRef = doc(collection(chatRoomRef, 'Messages'));
    batch.set(
      messageRef,
      removeUndefined({
        text: message.text,
        createdAt: serverTimestamp(),
        senderId: currentUser.id,
        senderName: currentUser.username,
        senderAvatar: currentUser.profileImageUrl,
        sent: true,
        received: false,
        read: false,
        ...(replyToMessage && {
          replyToMessage: {
            _id: replyToMessage._id,
            text: replyToMessage.text,
            user: {
              _id: replyToMessage.user?._id,
              name: replyToMessage.user?.name,
              avatar: replyToMessage.user?.avatar,
            },
          },
        }),
      })
    );

    const currentUserChatRef = doc(
      collection(doc(db, 'UserChats', currentUser.id), 'Chats'),
      userId
    );
    batch.set(
      currentUserChatRef,
      removeUndefined({
        userId,
        userName,
        userAvatar,
        lastMessage: message.text,
        lastMessageTime: serverTimestamp(),
        unreadCount: 0,
      }) as Partial<ChatListType>,
      { merge: true }
    );

    const otherUserChatRef = doc(
      collection(doc(db, 'UserChats', userId), 'Chats'),
      currentUser.id
    );
    batch.set(
      otherUserChatRef,
      removeUndefined({
        userId: currentUser.id,
        userName: currentUser.username,
        userAvatar: currentUser.profileImageUrl,
        lastMessage: message.text,
        lastMessageTime: serverTimestamp(),
        unreadCount: increment(1),
      }),
      { merge: true }
    );

    await batch.commit();

    setMessages((prev) =>
      prev.map((msg) =>
        msg._id === optimisticMessage._id
          ? { ...msg, sent: true, pending: false }
          : msg
      )
    );

    sendPushNotification({
      username: userName ?? '',
      title: `${currentUser.username ?? ''}`,
      body: message.text ?? '',
      image: currentUser.profileImageUrl ?? '',
      data: {
        type: 'PRIVATE_CHAT' as NotificationType,
        userId: currentUser.id,
        username: currentUser.username ?? '',
        userAvatar: currentUser.profileImageUrl ?? '',
        chatRoomId,
        excludeInSavedNotifications: true,
      },
    });
  };

  const setReplyMessage = useCallback((message: MessageType | null) => {
    setReplyToMessage(message);
  }, []);

  const handleBlockUser = async () => {
    try {
      const userRef = doc(db, 'Users', currentUser?.id || '');
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();

      const blockedUsers = userData?.blockedUsers || [];
      if (!blockedUsers.includes(userId)) {
        blockedUsers.push(userId);
      }

      await updateDoc(userRef, { blockedUsers });

      setOtherUserBlocked(true);
      toast.success('User blocked successfully');
    } catch (error) {
      console.error('Error blocking user: ', error);
      toast.error('Failed to block user. Please try again.');
    }
  };

  const handleUnblockUser = async () => {
    try {
      const userRef = doc(db, 'Users', currentUser?.id || '');
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();

      const blockedUsers: string[] = userData?.blockedUsers || [];
      const updatedBlockedUsers = blockedUsers.filter((id) => id !== userId);

      await updateDoc(userRef, { blockedUsers: updatedBlockedUsers });

      setOtherUserBlocked(false);
      toast.success('User unblocked successfully');
    } catch (error) {
      console.error('Error unblocking user: ', error);
      toast.error('Failed to unblock user. Please try again.');
    }
  };

  const handleAcceptRequest = async () => {
    try {
      const requestQuery = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', userId),
        where('receiverId', '==', currentUser?.id),
        where('status', '==', 'pending')
      );

      const snapshot = await getDocs(requestQuery);

      if (!snapshot.empty) {
        const requestDoc = snapshot.docs[0];
        const requestData = requestDoc.data() as MessageRequest;

        await updateDoc(doc(db, 'ChatRequests', requestDoc.id), {
          status: 'accepted',
        });

        const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
        const messagesSnapshot = await getDocs(
          collection(chatRoomRef, 'Messages')
        );

        const batch = writeBatch(db);
        messagesSnapshot.docs.forEach((messageDoc) => {
          const messageData = messageDoc.data();
          if (messageData.senderId === userId) {
            batch.update(messageDoc.ref, {
              received: true,
              read: true,
            });
          }
        });

        const currentUserChatRef = doc(
          collection(doc(db, 'UserChats', currentUser?.id || ''), 'Chats'),
          userId
        );

        batch.set(
          currentUserChatRef,
          removeUndefined({
            userId,
            userName,
            userAvatar: userAvatar || '',
            lastMessage: requestData.message,
            lastMessageTime: serverTimestamp(),
            unreadCount: 0,
          }) as Partial<ChatListType>,
          { merge: true }
        );

        const otherUserChatRef = doc(
          collection(doc(db, 'UserChats', userId), 'Chats'),
          currentUser?.id
        );

        batch.update(otherUserChatRef, {
          isPendingRequest: false,
          requestStatus: 'accepted',
        });

        const systemMessageRef = doc(collection(chatRoomRef, 'Messages'));
        batch.set(systemMessageRef, {
          id: systemMessageRef.id,
          type: 'system',
          text: `${currentUser?.username} accepted your chat request`,
          createdAt: serverTimestamp(),
          senderId: currentUser?.id,
          read: false,
          received: false,
        });
        await batch.commit();

        sendPushNotification({
          username: userName,
          title: 'Chat Request Accepted! 🎉',
          body: `${currentUser?.username} accepted your chat request`,
          image: currentUser?.profileImageUrl,
          data: {
            type: 'PRIVATE_CHAT' as NotificationType,
            userId: currentUser?.id,
            username: currentUser?.username,
            userAvatar: currentUser?.profileImageUrl || '',
            chatRoomId,
          },
        });

        setRequestAccepted(true);
        setHasPendingRequest(false);
        toast.success(`Accepted chat request from ${userName}`);
      }
    } catch (error) {
      console.error('Error accepting request: ', error);
      toast.error('Failed to accept request. Please try again.');
    }
  };

  const handleDeclineRequest = async () => {
    if (!currentUser) {
      return;
    }

    try {
      const requestQuery = query(
        collection(db, 'ChatRequests'),
        where('senderId', '==', userId),
        where('receiverId', '==', currentUser.id),
        where('status', '==', 'pending')
      );

      const snapshot = await getDocs(requestQuery);

      if (!snapshot.empty) {
        const requestDoc = snapshot.docs[0];
        await updateDoc(doc(db, 'ChatRequests', requestDoc.id), {
          status: 'declined',
        });

        const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
        const chatRoomDoc = await getDoc(chatRoomRef);

        if (chatRoomDoc.exists()) {
          const messagesSnapshot = await getDocs(
            collection(chatRoomRef, 'Messages')
          );
          const batch = writeBatch(db);
          messagesSnapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
          });

          batch.delete(chatRoomRef);

          const currentUserChatRef = doc(
            collection(doc(db, 'UserChats', currentUser.id), 'Chats'),
            userId
          );

          const otherUserChatRef = doc(
            collection(doc(db, 'UserChats', userId), 'Chats'),
            currentUser.id
          );

          batch.delete(currentUserChatRef);
          batch.delete(otherUserChatRef);

          await batch.commit();
        }

        sendPushNotification({
          username: userName,
          title: 'Chat Request Declined',
          body: `${currentUser.username} declined your chat request`,
          image: currentUser.profileImageUrl,
          data: {
            type: 'PRIVATE_CHAT' as NotificationType,
            userId: currentUser.id,
            username: currentUser.username,
            userAvatar: currentUser.profileImageUrl || '',
            chatRoomId,
          },
        });

        toast.success(`Declined chat request from ${userName}`);
      }
    } catch (error) {
      console.error('Error declining request: ', error);
      toast.error('Failed to decline request. Please try again.');
    }
  };

  const deleteChat = async (): Promise<void> => {
    if (!currentUser || !currentUser.id || !userId) {
      return;
    }
    setLoading(true);

    const db = getFirestore();
    const batch = writeBatch(db);
    try {
      // Delete all messages in the chat room
      const chatRoomRef = doc(db, 'ChatRooms', chatRoomId);
      const chatRoomDoc = await getDoc(chatRoomRef);
      const chatRoomData = chatRoomDoc.data();

      if (chatRoomData) {
        // This preserves the chat room but removes all content
        const chatRoomFields = Object.keys(chatRoomData);
        const updateObject: any = {};

        chatRoomFields.forEach((field) => {
          if (field !== 'id' && field !== 'participants') {
            updateObject[field] = deleteField();
          }
        });

        updateObject.lastDeleted = serverTimestamp();
        updateObject.lastDeletedBy = currentUser.id;

        batch.update(chatRoomRef, updateObject);
      }

      const messagesCollectionRef = collection(chatRoomRef, 'Messages');
      const messagesSnapshot = await getDocs(messagesCollectionRef);
      messagesSnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      const currentUserChatRef = doc(
        collection(doc(db, 'UserChats', currentUser.id), 'Chats'),
        userId
      );
      const otherUserChatRef = doc(
        collection(doc(db, 'UserChats', userId), 'Chats'),
        currentUser.id
      );

      batch.delete(currentUserChatRef);
      batch.delete(otherUserChatRef);

      await batch.commit();

      toast.success('Chat deleted successfully');
    } catch (error) {
      console.error('Error deleting chat:', error);
      toast.error('Failed to delete chat. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return {
    messages,
    chatUser,
    loading,
    isChatBlocked,
    isBlockedByOtherUser,
    hasBlockedOtherUser,
    onSend,
    handleBlockUser,
    handleUnblockUser,
    requestAccepted,
    hasPendingRequest,
    handleAcceptRequest,
    handleDeclineRequest,
    markMessagesAsRead,
    handleTypingStatus,
    otherUserTyping,
    setReplyMessage,
    replyToMessage,
    deleteChat,
  };
};
