import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useRef } from 'react';

import { useAppState } from './use-app-state';

export const useRefetchOnFocus = (
  refetch: () => void /**
   * Time after refetch function will be refetched
   * If you don't want to cache the values, you can set this to `0`
   * **Default**: 1 minute / 60 seconds (60 * 1000)
   */,
  staleTime = 60 * 1000
) => {
  const isFirstRun = useRef(false);
  const lastFetchTime = useRef(Date.now());

  useFocusEffect(
    useCallback(() => {
      if (!isFirstRun.current) {
        isFirstRun.current = true;
        return;
      }
      // Prevents refetching if the last fetch was less than x(e.g. 1 minute ago)
      // Boosts performance and prevents unnecessary API calls
      if (Date.now() - lastFetchTime.current < staleTime) {
        return;
      }
      lastFetchTime.current = Date.now();

      refetch();
    }, [refetch, staleTime])
  );

  useAppState({
    onForeground: refetch,
  });
};
