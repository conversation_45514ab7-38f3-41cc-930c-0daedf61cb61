import {
  collection,
  doc,
  getDocs,
  getFirestore,
  onSnapshot,
  query,
  Timestamp,
  where,
} from '@react-native-firebase/firestore';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDebounce } from 'use-debounce';

import type { UserObjectData } from '@/api/auth';
import { useGetUserFavourites } from '@/api/user';
import type { UserType } from '@/types';

import { MAX_RECENT_SEARCHES, RECENT_SEARCHES_KEY } from '../constants';
import { getItem, removeItem, setItem } from '../storage';
import { useLoggedInUser } from './use-logged-in-user';

export type SearchMode = 'search' | 'newChat';

interface UseSearchUsersOptions {
  mode?: SearchMode;
}

export const useSearchUsers = (
  searchQuery: string,
  options: UseSearchUsersOptions = { mode: 'search' }
) => {
  const { mode } = options;
  const [debouncedText] = useDebounce(searchQuery, 500);

  const [firestoreUsers, setFirestoreUsers] = useState<UserType[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([]);
  const [recentSearches, setRecentSearches] = useState<UserType[]>([]);
  const [loading, setLoading] = useState(true);

  // Store active subscriptions in a ref to avoid recreation
  const subscriptionsRef = useRef<(() => void)[]>([]);

  // Define query parameters as a memoized object to prevent unnecessary refetches
  // const queryObj = useMemo(
  //   () => ({
  //     skip: 0,
  //     take: 12,
  //     roles: ['QUESTER', 'CREATOR'] as USER_ROLE[],
  //     query: debouncedText,
  //   }),
  //   [debouncedText]
  // );

  // const variables = {
  //   queryObj,
  // };

  const { data: currentUser } = useLoggedInUser();

  const {
    isLoading: queryLoading,
    data: favoriteAccounts,
    refetch,
  } = useGetUserFavourites({
    variables: {
      type: 'ACCOUNT',
    },
    enabled: mode === 'newChat' || debouncedText !== '',
  });

  const filteredFavoriteAccounts = (favoriteAccounts?.filter((account) =>
    (account as UserObjectData).username.includes(debouncedText.toLowerCase())
  ) || []) as UserObjectData[];

  // Load recent searches from storage
  const loadRecentSearches = useCallback(async () => {
    try {
      const storedSearches = getItem<UserType[]>(RECENT_SEARCHES_KEY);
      if (storedSearches) {
        setRecentSearches(storedSearches);
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  }, []);

  // Save a user to recent searches
  const saveToRecentSearches = useCallback(
    async (user: UserType) => {
      try {
        // Don't add the current user to recent searches
        if (user.id === currentUser?.id) return;

        // Create a copy of current recent searches
        const updatedSearches = [...recentSearches];

        // Remove the user if already in the list to avoid duplicates
        const existingIndex = updatedSearches.findIndex(
          (search) => search.id === user.id
        );

        if (existingIndex !== -1) {
          updatedSearches.splice(existingIndex, 1);
        }

        // Add the user to the beginning of the list
        updatedSearches.unshift(user);

        // Trim the list if it exceeds the maximum size
        const trimmedSearches = updatedSearches.slice(0, MAX_RECENT_SEARCHES);

        // Update state and store in storage
        setRecentSearches(trimmedSearches);
        await setItem(RECENT_SEARCHES_KEY, trimmedSearches);
      } catch (error) {
        console.error('Error saving recent search:', error);
      }
    },
    [recentSearches, currentUser?.id]
  );

  const clearRecentSearches = useCallback(async () => {
    try {
      setRecentSearches([]);
      await removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('Error removing recent search:', error);
    }
  }, []);

  // Transform database user to Firestore user format - memoize for repeated use
  const transformDatabaseUser = useCallback(
    (dbUser: UserObjectData): UserType => {
      return {
        id: dbUser.id,
        displayName: dbUser.username || dbUser.fullName,
        email: dbUser.email,
        photoURL: dbUser.profileImageUrl,
        createdAt: Timestamp.fromDate(
          typeof dbUser.createdAt === 'string'
            ? new Date(dbUser.createdAt)
            : dbUser.createdAt?.toDate
              ? dbUser.createdAt.toDate()
              : new Date()
        ),
        status: 'offline',
      };
    },
    []
  );

  // Fetch Firestore users
  const fetchFirestoreUsers = useCallback(async () => {
    if (!currentUser?.id) return [];

    try {
      const db = getFirestore();
      const usersCollection = collection(db, 'Users');
      const usersQuery = query(
        usersCollection,
        where('id', '!=', currentUser.id)
      );

      const querySnapshot = await getDocs(usersQuery);

      const usersData: UserType[] = [];
      querySnapshot.forEach((doc) => {
        usersData.push({ id: doc.id, ...doc.data() } as UserType);
      });

      setFirestoreUsers(usersData);
      return usersData;
    } catch (error) {
      console.error('Error fetching users: ', error);
      return [];
    }
  }, [currentUser?.id]);

  // Initial data fetch and subscription
  useEffect(() => {
    const initialize = async () => {
      setLoading(true);
      await loadRecentSearches();
      await fetchFirestoreUsers();
      !!debouncedText && (await refetch());

      setLoading(false);
    };
    initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Clean up subscriptions on unmount
  useEffect(() => {
    return () => {
      subscriptionsRef.current.forEach((unsubscribe) => unsubscribe());
      subscriptionsRef.current = [];
    };
  }, []);

  const selectUser = useCallback(
    (user: UserType) => {
      saveToRecentSearches(user);
    },
    [saveToRecentSearches]
  );

  // Transform, combine and filter users from both sources
  useEffect(() => {
    // Clean up existing subscriptions
    subscriptionsRef.current.forEach((unsubscribe) => unsubscribe());
    subscriptionsRef.current = [];

    // Filter firestore users to only include those in favorites
    const favoriteIds = new Set(filteredFavoriteAccounts.map((u) => u.id));
    const filteredFirestoreUsers = firestoreUsers.filter((user) =>
      favoriteIds.has(user.id)
    );

    // Create a map of filtered Firestore users by ID
    const firestoreUserMap = new Map(
      filteredFirestoreUsers.map((user) => [user.id, user])
    );

    // Mark firestoreUsers with a flag
    const flaggedFirestoreUsers = filteredFirestoreUsers.map((user) => ({
      ...user,
      isInFirestore: true,
    }));

    // Transform database users to Firestore format
    const databaseUsers = filteredFavoriteAccounts;
    const transformedDatabaseUsers = databaseUsers.map(transformDatabaseUser);

    // Filter transformed database users to find those not in Firestore and mark them
    const uniqueDatabaseUsers = transformedDatabaseUsers
      .filter((dbUser) => !firestoreUserMap.has(dbUser.id))
      .map((user) => ({
        ...user,
        isInFirestore: false,
      }));

    // Combine both sources
    const allUsers = [...flaggedFirestoreUsers, ...uniqueDatabaseUsers];

    // Apply search filter if needed
    const filtered =
      debouncedText.trim() === ''
        ? allUsers
        : allUsers.filter((user) =>
            user.displayName
              ?.toLowerCase()
              .includes(debouncedText.toLowerCase())
          );

    // Sort users by online status and last seen time
    const sortedUsers = [...filtered].sort((a, b) => {
      if (a.status === 'online' && b.status !== 'online') return -1;
      if (a.status !== 'online' && b.status === 'online') return 1;

      const aTime =
        a.lastSeen?.toDate?.() || a.createdAt?.toDate?.() || new Date(0);
      const bTime =
        b.lastSeen?.toDate?.() || b.createdAt?.toDate?.() || new Date(0);
      return bTime.getTime() - aTime.getTime();
    });

    setFilteredUsers(sortedUsers);

    // Get all users that need status monitoring
    const userIdsToMonitor = new Set<string>();
    sortedUsers
      .filter((user) => user.isInFirestore)
      .forEach((user) => userIdsToMonitor.add(user.id));
    recentSearches
      .filter((user) => firestoreUserMap.has(user.id))
      .forEach((user) => userIdsToMonitor.add(user.id));

    const userStatusSubscriptions = Array.from(userIdsToMonitor).map(
      (userId) => {
        const db = getFirestore();
        const userDoc = doc(db, 'Users', userId);

        return onSnapshot(
          userDoc,
          (userSnapshot) => {
            const userData = userSnapshot.data();
            if (userData) {
              setFilteredUsers((prevUsers) =>
                prevUsers.map((prevUser) =>
                  prevUser.id === userId
                    ? {
                        ...prevUser,
                        status: userData.status || 'offline',
                        lastSeen: userData.lastSeen,
                      }
                    : prevUser
                )
              );
              setRecentSearches((prevSearches) => {
                const updatedRecentSearches = prevSearches.map((prevSearch) =>
                  prevSearch.id === userId
                    ? {
                        ...prevSearch,
                        status: userData.status || 'offline',
                        lastSeen: userData.lastSeen,
                      }
                    : prevSearch
                );
                setItem(RECENT_SEARCHES_KEY, updatedRecentSearches);
                return updatedRecentSearches;
              });
            }
          },
          (error) => {
            console.error(
              `Error in user status subscription for ${userId}:`,
              error
            );
          }
        );
      }
    );

    subscriptionsRef.current = userStatusSubscriptions;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedText, firestoreUsers]);

  return {
    debouncedText,
    filteredUsers,
    favoriteAccounts,
    loading,
    queryLoading,
    recentSearches,
    selectUser,
    clearRecentSearches,
  };
};
