import {
  addDoc,
  collection,
  doc,
  getFirestore,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  type Timestamp,
  updateDoc,
  where,
} from '@react-native-firebase/firestore';
import { type FlashList } from '@shopify/flash-list';
import React from 'react';
import { ActionSheetIOS } from 'react-native';

import { useAuth } from '../auth';
import { useGetLiveSessionQuery } from './queries/use-get-live-session-query';

export interface LiveSessionChatMessage {
  id: string;
  createdAt: Timestamp;
  image: string;
  sessionId: string;
  text: string;
  userId: string;
  username: string;
  isCreator?: boolean;
  isPinned?: boolean;
  songRequestInfo?: {
    title: string;
    artist: string;
    status: string;
    isShoutout: boolean;
  };
}

interface Props {
  scrollViewRef: React.RefObject<FlashList<any> | null>;
  sessionId: string;
}
export const useSessionChat = ({ scrollViewRef, sessionId }: Props) => {
  const user = useAuth.use.user();
  const { data: sessionData } = useGetLiveSessionQuery(sessionId);
  const sessionInfo = sessionData?.djSession;
  const [messages, setMessages] = React.useState<LiveSessionChatMessage[]>([]);
  const [message, setMessage] = React.useState('');
  const [pinnedMessage, setPinnedMessage] =
    React.useState<LiveSessionChatMessage | null>(null);

  // Create a memoized value for the Firestore DB instance
  const db = React.useMemo(() => getFirestore(), []);

  const isSessionCreator = React.useMemo(
    () => user?.username === sessionInfo?.dj.username,
    [user?.username, sessionInfo?.dj.username]
  );

  const scrollToBottom = React.useCallback(
    (delay = 100) => {
      setTimeout(() => {
        scrollViewRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, delay);
    },
    [scrollViewRef]
  );

  React.useEffect(() => {
    const liveChatRef = collection(db, 'liveChats');
    const q = query(
      liveChatRef,
      where('sessionId', '==', sessionId),
      orderBy('createdAt', 'asc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messagesData = snapshot.docs.map((doc) => {
        const data = doc.data() as Omit<LiveSessionChatMessage, 'id'>;
        return {
          id: doc.id,
          ...data,
          isCreator: data.username === sessionInfo?.dj.username,
        };
      });

      setMessages(messagesData);

      // Find pinned message
      const pinned = messagesData.find((msg) => msg.isPinned);
      setPinnedMessage(pinned || null);

      // Only scroll if there are messages
      if (messagesData.length > 0) {
        scrollToBottom();
      }
    });

    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionInfo?.dj.username, sessionId]);

  // Send message to Firestore
  const sendMessage = React.useCallback(async () => {
    if (!message.trim() || !user?.id || !sessionId) return;
    try {
      setMessage('');
      const liveChatRef = collection(db, 'liveChats');
      await addDoc(liveChatRef, {
        text: message.trim(),
        createdAt: serverTimestamp(),
        userId: user.id,
        username: user.username,
        image: user.profileImageUrl || '',
        sessionId,
        isCreator: isSessionCreator,
      });

      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [db, message, user, sessionId, isSessionCreator, scrollToBottom]);

  const pinMessage = React.useCallback(
    async (messageToPin: LiveSessionChatMessage) => {
      if (!isSessionCreator) return;

      try {
        // Unpin previous message if it exists
        if (pinnedMessage) {
          const prevPinnedRef = doc(db, 'liveChats', pinnedMessage.id);
          await updateDoc(prevPinnedRef, { isPinned: false });
        }

        // Pin the new message
        const messageRef = doc(db, 'liveChats', messageToPin.id);
        await updateDoc(messageRef, { isPinned: true });

        setPinnedMessage(messageToPin);
      } catch (error) {
        console.error('Error pinning message:', error);
      }
    },
    [db, pinnedMessage, isSessionCreator]
  );

  // Unpin message function
  const unpinMessage = React.useCallback(async () => {
    if (!pinnedMessage || !isSessionCreator) return;

    try {
      const messageRef = doc(db, 'liveChats', pinnedMessage.id);
      await updateDoc(messageRef, { isPinned: false });

      setPinnedMessage(null);
    } catch (error) {
      console.error('Error unpinning message:', error);
    }
  }, [db, pinnedMessage, isSessionCreator]);

  const showCreatorActionSheet = (
    userMessage: LiveSessionChatMessage,
    isPinned?: boolean
  ) => {
    ActionSheetIOS.showActionSheetWithOptions(
      {
        options: [
          'Cancel',
          ...(user?.username === sessionInfo?.dj.username
            ? [
                userMessage.isPinned || isPinned
                  ? 'Unpin Message'
                  : 'Pin Message',
              ]
            : []),
        ],
        cancelButtonIndex: 0,
      },
      async (buttonIndex) => {
        if (buttonIndex === 1) {
          // Pin/unpin message
          if (userMessage.isPinned || isPinned) {
            await unpinMessage();
          } else {
            await pinMessage(userMessage);
          }
        }
      }
    );
  };

  return {
    messages,
    message,
    setMessage,
    sendMessage,
    pinnedMessage,
    pinMessage,
    unpinMessage,
    isSessionCreator,
    showCreatorActionSheet,
  };
};
