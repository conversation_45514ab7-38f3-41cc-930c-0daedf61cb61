/* eslint-disable import/no-cycle */
import { useFormContext } from 'react-hook-form';

import { useModal } from '@/components/ui';

import { type CreateLiveFormType } from './use-create-session';

export function useLiveSessionForm() {
  const { control, watch, resetField, setValue, formState, trigger } =
    useFormContext<CreateLiveFormType>();

  const genreModal = useModal();
  const songRequestModal = useModal();
  const shoutoutModal = useModal();

  const liveType = watch('type');
  const selectedGenres = watch('genres') || [];
  const requestCost = watch('cost');
  const shoutoutCost = watch('shoutoutCost');
  const songRequestEnabled = watch('songRequestEnabled');
  const shoutoutEnabled = watch('shoutoutEnabled');

  const handleEnableShoutout = (checked: boolean) => {
    setValue('shoutoutEnabled', checked);

    if (checked) {
      shoutoutModal.present();
    } else {
      resetField('shoutoutCost');
    }
  };

  const handleEnableSongRequest = (checked: boolean) => {
    setValue('songRequestEnabled', checked);

    if (checked) {
      songRequestModal.present();
    } else {
      resetField('cost');
    }
  };

  const handleEnableVideoStream = (checked: boolean) => {
    setValue('enableVideo', checked);
  };

  const handleToggleGenre = (genreId: string) => {
    let newSelectedGenres;

    if (selectedGenres.includes(genreId)) {
      newSelectedGenres = selectedGenres.filter((id) => id !== genreId);
    } else {
      newSelectedGenres = [...selectedGenres, genreId];
    }

    setValue('genres', newSelectedGenres, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  return {
    control,
    watch,
    formState,
    liveType,
    selectedGenres,
    requestCost,
    shoutoutCost,
    songRequestEnabled: songRequestEnabled && !!requestCost,
    shoutoutEnabled: shoutoutEnabled && !!shoutoutCost,
    videoEnabled: watch('enableVideo'),
    genreModal,
    songRequestModal,
    shoutoutModal,
    setValue,
    handleToggleGenre,
    handleEnableSongRequest,
    handleEnableShoutout,
    handleEnableVideoStream,
    trigger,
  };
}
