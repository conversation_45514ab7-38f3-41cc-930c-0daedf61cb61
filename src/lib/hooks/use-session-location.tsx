import { useState } from 'react';

export type LocationType = 'ONLINE' | 'PHYSICAL';

export function useLiveSessionLocation() {
  const [locationType, setLocationType] = useState<LocationType>('PHYSICAL');
  const [address, setAddress] = useState('');
  const [isEditing, setIsEditing] = useState(true);

  const handleLocationTypeChange = (value: string) => {
    setLocationType(value as LocationType);

    if (value === 'online') {
      setIsEditing(false);
      setAddress('');
    } else if (value === 'physical') {
      setIsEditing(true);
    }
  };

  return {
    locationType,
    address,
    isEditing,
    setLocationType: handleLocationTypeChange,
    setAddress,
    setIsEditing,
  };
}
