import { Gesture } from 'react-native-gesture-handler';
import { useSharedValue } from 'react-native-reanimated';
import { runOnJS, withSpring } from 'react-native-reanimated';

export const ACTION_WIDTH = 80;
export const SWIPE_THRESHOLD = 60;
export const CLOSE_THRESHOLD = 15;

interface SwipeGestureCallbacks {
  onFavorite: () => void;
  onBlock: () => void;
  onReport: () => void;
  onDelete: () => void;
}

export const useSwipeGesture = (callbacks: SwipeGestureCallbacks) => {
  const translateX = useSharedValue(0);
  const startTranslateX = useSharedValue(0);

  const resetPosition = () => {
    'worklet';
    translateX.value = withSpring(0, { damping: 20, stiffness: 300 });
  };

  const handleFavoritePress = () => {
    runOnJS(callbacks.onFavorite)();
    resetPosition();
  };

  const handleBlockPress = () => {
    runOnJS(callbacks.onBlock)();
    resetPosition();
  };

  const handleReportPress = () => {
    runOnJS(callbacks.onReport)();
    resetPosition();
  };

  const handleDeletePress = () => {
    runOnJS(callbacks.onDelete)();
    resetPosition();
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .onStart(() => {
      startTranslateX.value = translateX.value;
    })
    .onUpdate((event) => {
      const newTranslateX = startTranslateX.value + event.translationX;
      if (newTranslateX > ACTION_WIDTH * 2) {
        const excess = newTranslateX - ACTION_WIDTH * 2;
        translateX.value = ACTION_WIDTH * 2 + excess * 0.2;
      } else if (newTranslateX < -ACTION_WIDTH * 2) {
        const excess = Math.abs(newTranslateX + ACTION_WIDTH * 2);
        translateX.value = -ACTION_WIDTH * 2 - excess * 0.2;
      } else {
        translateX.value = newTranslateX;
      }
    })
    .onEnd((event) => {
      const currentTranslateX = translateX.value;
      const startPosition = startTranslateX.value;
      const swipeDistance = Math.abs(event.translationX);
      const velocity = Math.abs(event.velocityX);

      if (Math.abs(startPosition) > 5) {
        if (swipeDistance > CLOSE_THRESHOLD || velocity > 300) {
          resetPosition();
        } else {
          translateX.value = withSpring(startPosition, {
            damping: 20,
            stiffness: 300,
          });
        }
      } else {
        const passedThreshold = Math.abs(currentTranslateX) > SWIPE_THRESHOLD;
        const hasVelocity = velocity > 500;
        if (passedThreshold || hasVelocity) {
          if (
            currentTranslateX > 0 ||
            (event.translationX > 0 && hasVelocity)
          ) {
            translateX.value = withSpring(ACTION_WIDTH * 2, {
              damping: 20,
              stiffness: 300,
            });
          } else if (
            currentTranslateX < 0 ||
            (event.translationX < 0 && hasVelocity)
          ) {
            translateX.value = withSpring(-ACTION_WIDTH * 2, {
              damping: 20,
              stiffness: 300,
            });
          } else {
            resetPosition();
          }
        } else {
          resetPosition();
        }
      }
    });

  const tapGesture = Gesture.Tap()
    .maxDuration(250)
    .onEnd(() => {
      if (Math.abs(translateX.value) >= 5) {
        resetPosition();
      }
    });

  const composedGesture = Gesture.Race(panGesture, tapGesture);

  return {
    translateX,
    composedGesture,
    resetPosition,
    handleFavoritePress,
    handleBlockPress,
    handleReportPress,
    handleDeletePress,
    ACTION_WIDTH,
  };
};
