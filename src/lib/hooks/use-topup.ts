import { useRouter } from 'expo-router';
import { usePaystack } from 'react-native-paystack-webview';
import { toast } from 'sonner-native';

import { queryClient } from '@/api';
import { useInitializeTransaction } from '@/api/transactions';
import { useVerifyPaystackTransaction } from '@/api/transactions/use-verify-paystack-transaction';

import { useAuth } from '../auth';

export const useTopup = ({ amount }: { amount: number }) => {
  const router = useRouter();
  const { email, fullName, id: userId } = useAuth.use.user()!;
  const { popup } = usePaystack();
  const { mutate: initializeTransaction, isPending: initializing } =
    useInitializeTransaction();
  const { mutate: verifyPaystackTransaction } = useVerifyPaystackTransaction();

  const formatMetadata = (metadata: {
    purpose: string;
    user: { fullName: string; email: string };
    userId: string;
  }): {
    custom_fields: {
      display_name: string;
      variable_name: string;
      value: string | number;
    }[];
  } => {
    return {
      custom_fields: [
        {
          display_name: 'Purpose',
          variable_name: 'purpose',
          value: metadata.purpose,
        },
        {
          display_name: 'User Details',
          variable_name: 'user',
          value: JSON.stringify(metadata.user),
        },
        {
          display_name: 'User ID',
          variable_name: 'userId',
          value: metadata.userId,
        },
      ],
    };
  };

  const topup = ({ reference }: { reference: string }) => {
    popup.newTransaction({
      email,
      amount,
      reference,
      onSuccess: (res) => {
        verifyPaystackTransaction(
          { transactionRef: res.reference },
          {
            onSuccess: ({ status }) => {
              queryClient.invalidateQueries({ queryKey: ['getUser'] });
              queryClient.invalidateQueries({
                queryKey: ['getTransactionHistory'],
              });
              if (status === 'success') {
                toast.success('Transaction Successful');
              } else {
                toast.success('Transaction could not be verified');
              }
              router.back();
            },
            onError: (error) => toast.error(error.message),
          }
        );
      },
      onCancel: () => {
        console.log('User cancelled');
        verifyPaystackTransaction({ transactionRef: reference });
      },
      metadata: formatMetadata({
        purpose: 'WALLET_TOPUP',
        user: { email: email, fullName: fullName },
        userId,
      }),
    });
  };

  return { topup, initializeTransaction, initializing };
};
