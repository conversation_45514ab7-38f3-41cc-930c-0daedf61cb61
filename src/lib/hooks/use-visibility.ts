import { useState } from 'react';

export type VisibilityType = {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  toggleVisibility: () => void;
};

export const useVisibility = (): VisibilityType => {
  const [isOpen, setIsOpen] = useState(false);

  const onOpen = () => {
    setIsOpen(true);
  };

  const onClose = () => {
    setIsOpen(false);
  };

  const toggleVisibility = () => {
    setIsOpen((open) => !open);
  };

  return { isOpen, onOpen, onClose, toggleVisibility };
};
