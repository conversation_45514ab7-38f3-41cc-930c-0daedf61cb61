import {
  AuthorizationStatus,
  getMessaging,
} from '@react-native-firebase/messaging';
import { PermissionsAndroid, Platform } from 'react-native';

import { type NotificationExtraData } from '@/types';

import { APP_URL } from './constants';
import { getItem, removeItem, setItem } from './storage';

const FCM_TOKEN = 'fcmToken';

export const getFCMToken = () => getItem<string>(FCM_TOKEN);
export const removeFCMToken = () => removeItem(FCM_TOKEN);
export const setFCMToken = (value: string) => setItem<string>(FCM_TOKEN, value);

export const requestUserPermission = async () => {
  try {
    if (Platform.OS === 'android')
      PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
      );
    const authStatus = await getMessaging().requestPermission();
    const enabled =
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('Authorization status:', authStatus);
    }
  } catch (error) {
    console.log(error, 'permission failed');
  }
};

export const GetFCMToken = async () => {
  try {
    const fcmToken = await getMessaging().getToken();
    setFCMToken(fcmToken);
    return fcmToken;
  } catch (error) {
    console.log(error, 'error in fcmToken');
  }
};

export const RemoveFCMToken = async () => {
  try {
    const token = getFCMToken();
    if (token) {
      removeFCMToken();
      await getMessaging().deleteToken();
    }
  } catch (error) {
    console.log(error, 'error in fcmToken');
  }
};

export function buildDeepLinkFromNotificationData(data: NotificationExtraData) {
  const notificationType = data?.type;

  if (notificationType === 'EVENT' && data.eventId) {
    return `${APP_URL}/events/${data.eventId}`;
  }
  if (
    notificationType === 'PRIVATE_CHAT' &&
    data.userId &&
    data.username &&
    data.userAvatar
  ) {
    return `${APP_URL}/chats/${data.chatRoomId}?userId=${data.userId}&username=${data.username}&userAvatar=${data.userAvatar}`;
  }
  if (notificationType === 'DJ_SESSION' && data.djSessionId) {
    return `${APP_URL}/session?djSessionId=${data?.djSessionId}`;
  }
  console.warn('Missing deep linking implementation for notification type');
  return null;
}

// export const linking = {
//   prefixes: [
//     'popla://',
//     'https://getpopla.com',
//     'https://app.getpopla.com',
//     'https://dev.app.getpopla.com',
//     'https://www.getpopla.com',
//     'https://*.getpopla.com',
//   ],
//   config: {
//     screens: {
//       [PAGE.ORGANIZER_PROFILE.screen]: {
//         path: 'user/:id',
//         parse: {
//           id: (id: string) => `${id}`,
//         },
//       },
//       [PAGE.EVENT_PROFILE.screen]: {
//         path: 'event/:slug',
//         parse: {
//           slug: (slug: string) => `${slug}`,
//         },
//       },
//       [PAGE.CHANGE_PASSWORD.screen]: {
//         path: 'change-password/:userId/:token',
//         parse: {
//           userId: (userId: string) => `${userId}`,
//           token: (token: string) => `${token}`,
//         },
//       },
//       [PAGE.CHAT.screen]: {
//         path: 'chat/:userId/:userName/:userAvatar',
//         parse: {
//           userId: (userId: string) => `${userId}`,
//           userName: (userName: string) => `${userName}`,
//           userAvatar: (userAvatar: string) => `${userAvatar}`,
//         },
//       },
//     },
//   },
//   async getInitialURL() {
//     const url = await Linking.getInitialURL();
//     if (typeof url === 'string') {
//       return url;
//     }
//     //getInitialNotification: When the application is opened from a quit state.
//     const message = await messaging().getInitialNotification();
//     const deeplinkURL = buildDeepLinkFromNotificationData(
//       message?.data as NotificationExtraData
//     );
//     if (typeof deeplinkURL === 'string') {
//       return deeplinkURL;
//     }
//   },
//   subscribe(listener: (url: string) => void) {
//     const onReceiveURL = async ({ url }: { url: string }) => {
//       const stripeHandled = await handleURLCallback(url);
//       if (!stripeHandled) {
//         listener(url);
//       }
//     };

//     // Listen to incoming links from deep linking
//     const linkingSubscription = Linking.addEventListener('url', onReceiveURL);

//     //onNotificationOpenedApp: When the application is running, but in the background.
//     const unsubscribe = messaging().onNotificationOpenedApp((remoteMessage) => {
//       const url = buildDeepLinkFromNotificationData(
//         remoteMessage.data as NotificationExtraData
//       );
//       if (typeof url === 'string') {
//         listener(url);
//       }
//     });

//     return () => {
//       linkingSubscription.remove();
//       unsubscribe();
//     };
//   },
// };
