import type { Theme } from '@react-navigation/native';
import {
  DarkTheme as _DarkTheme,
  DefaultTheme,
} from '@react-navigation/native';
import { useColorScheme } from 'nativewind';

import colors from '@/components/ui/colors';

const DarkTheme: Theme = {
  ..._DarkTheme,
  colors: {
    ..._DarkTheme.colors,
    primary: colors.brand[60],
    background: colors.grey[100],
    text: colors.white,
    border: colors.grey[80],
    card: colors.grey[80],
  },
  fonts: {
    bold: {
      fontFamily: 'Aeonik-Black',
      fontWeight: '700',
    },
    medium: {
      fontFamily: 'Aeonik-Medium',
      fontWeight: '500',
    },
    heavy: { fontFamily: 'Aeonik-Black', fontWeight: '700' },
    regular: { fontFamily: 'Aeonik-Regular', fontWeight: '400' },
  },
};

const LightTheme: Theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.brand[60],
    background: colors.white,
    text: colors.grey[100],
    border: colors.grey[20],
    card: colors.white,
  },
  fonts: {
    bold: {
      fontFamily: 'Aeonik-Black',
      fontWeight: '700',
    },
    medium: {
      fontFamily: 'Aeonik-Medium',
      fontWeight: '500',
    },
    heavy: { fontFamily: 'Aeonik-Black', fontWeight: '700' },
    regular: { fontFamily: 'Aeonik-Regular', fontWeight: '400' },
  },
};

export function useThemeConfig() {
  const { colorScheme } = useColorScheme();

  if (colorScheme === 'dark') return DarkTheme;

  return LightTheme;
}
