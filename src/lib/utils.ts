import { type ClassValue, clsx } from 'clsx';
import { differenceInSeconds, parseISO } from 'date-fns';
import dayjs from 'dayjs';
import * as Location from 'expo-location';
import moment from 'moment';
import { Linking } from 'react-native';
import { Dimensions } from 'react-native';
import { type barDataItem, type pieDataItem } from 'react-native-gifted-charts';
import { type LatLng } from 'react-native-maps';
import { twMerge } from 'tailwind-merge';
import type { StoreApi, UseBoundStore } from 'zustand';

import { type PresaleConfig, type TicketCategories } from '@/api/events';
import { colors, WIDTH } from '@/components/ui';

export function openLinkInBrowser(url: string, storeUrl?: string) {
  Linking.canOpenURL(url)
    .then((canOpen) => canOpen && Linking.openURL(url))
    .catch(
      () =>
        storeUrl &&
        Linking.openURL(storeUrl).catch((err) =>
          console.error('An error occurred', err)
        )
    );
}

export function pxToSnapPoint(px: number) {
  const screenHeight = Dimensions.get('window').height;
  const percent = (px / screenHeight) * 100;
  return `${percent.toFixed(0)}%`;
}
export function formatNumberWithCommas(value: string) {
  // remove non-digits
  const numeric = value.replace(/\D/g, "");
  if (!numeric) return "";
  return Number(numeric).toLocaleString();
}

export const openStoreLink = (storeUrl: string) =>
  Linking.openURL(storeUrl).catch((err) =>
    console.error('An error occurred', err)
  );

type WithSelectors<S> = S extends { getState: () => infer T }
  ? S & { use: { [K in keyof T]: () => T[K] } }
  : never;

export const createSelectors = <S extends UseBoundStore<StoreApi<object>>>(
  _store: S
) => {
  let store = _store as WithSelectors<typeof _store>;
  store.use = {};
  for (let k of Object.keys(store.getState())) {
    (store.use as any)[k] = () => store((s) => s[k as keyof typeof s]);
  }

  return store;
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const PASSWORD_REGEX = {
  twoLowerCaseRegex: /^(.*[a-z]){2,}.*$/,
  oneUpperCaseRegex: /^(.*[A-Z]){1,}.*$/,
  twoDigitsRegex: /^(.*\d){1,}.*$/,
  oneSpecialCharacter: /^.*[!@#$%^&*()_+{}\\[\]:;<>,.?/~\\-].*$/,
  eightCharacterLong: /^.{8,}$/,
};

export const USERNAME_REGEX = {
  noStartEndDotUnderscore: /^(?![._])(?!.*[._]$).*$/,
  noConsecutiveDotUnderscore: /^(?!.*[._]{2}).*$/,
  startWithAlphanumeric: /^[a-zA-Z0-9].*$/,
  validCharacters: /^[a-zA-Z0-9._]*$/,
  validDotUnderscoreUsage: /^(?!.*[._]{2})(?!(?!.*\d)[._]).*$/,
};

export const NO_SPACE_REGEX = /^[^\s]*$/;

/**
 * Extracts the first name and last name from a full name string
 * @param {string} fullName - The full name to parse
 * @returns {Object} An object containing firstName and lastName
 */
export const getNameParts = (
  fullName?: string
): {
  firstName: string;
  lastName: string;
} => {
  // Handle empty or invalid input
  if (!fullName || typeof fullName !== 'string') {
    return { firstName: '', lastName: '' };
  }

  // Trim and split the full name by spaces
  const nameParts = fullName.trim().split(/\s+/);

  // If there's only one part, it's considered the first name
  if (nameParts.length === 1) {
    return { firstName: nameParts[0], lastName: '' };
  }

  // First part is the first name
  const firstName = nameParts[0];

  // Everything else is considered the last name (handles multiple last names)
  const lastName = nameParts.slice(1).join(' ');

  return { firstName, lastName };
};

/**
 * Recursively filters out empty properties and undefined values from an object
 * Also cleans nested objects by removing their empty or undefined properties
 * @template T Object type with string keys and various value types
 * @param obj - The object to filter
 * @returns A new object with empty properties and undefined values removed at all levels
 */
export const filterEmptyProperties = <T extends Record<string, any>>(
  obj: T
): Partial<T> => {
  // Create a new object to store filtered properties
  const result: Partial<T> = {} as Partial<T>;

  // Process each property in the object
  for (const [key, value] of Object.entries(obj)) {
    // Skip undefined values
    if (value === undefined) continue;

    // Skip empty strings
    if (value === '') continue;

    // Handle nested objects recursively
    if (typeof value === 'object' && value !== null) {
      // For arrays
      if (Array.isArray(value)) {
        const filteredArray = value.filter(
          (item) =>
            item !== undefined &&
            item !== '' &&
            (typeof item !== 'object' || Object.keys(item).length > 0)
        );

        // Only add non-empty arrays
        if (filteredArray.length > 0) {
          result[key as keyof T] = filteredArray as any;
        }
      }
      // For nested objects
      else {
        const filteredNestedObj = filterEmptyProperties(value);

        // Only add non-empty objects
        if (Object.keys(filteredNestedObj).length > 0) {
          result[key as keyof T] = filteredNestedObj as any;
        }
      }
    }
    // Add primitive values directly
    else {
      result[key as keyof T] = value;
    }
  }

  return result;
};

export const formatAmount = (
  amount: number | bigint,
  locales?: Intl.LocalesArgument,
  currencyCode?: string
) => {
  // @ts-ignore
  const formatter = new Intl.NumberFormat(locales || 'en-NG', {
    style: 'currency',
    currency: currencyCode || 'NGN',
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  }).format(amount);
  return formatter;
};

// Utility function to format numbers with commas
export const formatNumber = (value: number = 0, decimals = 2) => {
  return toAmountInMajor(value).toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

type AmountValue = string | number;

export function toAmountInMinor(amountValue: AmountValue) {
  if (typeof amountValue === 'string') {
    return Number(amountValue) * 100;
  }
  return amountValue * 100;
}

export function toAmountInMajor(amountValue: AmountValue) {
  if (typeof amountValue === 'string') {
    return Number(amountValue) / 100;
  }
  return amountValue / 100;
}

// Utility function to format percentage
export const formatPercentage = (num: number): string => {
  return `${num.toFixed(2)}%`;
};

export const formatLastSeen = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const oneMinute = 60 * 1000;
  const oneHour = 60 * oneMinute;
  const oneDay = 24 * oneHour;

  if (diff < oneMinute) {
    return 'just now';
  } else if (diff < oneHour) {
    const minutes = Math.floor(diff / oneMinute);
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diff < oneDay) {
    const hours = Math.floor(diff / oneHour);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diff < 2 * oneDay) {
    return 'yesterday';
  } else {
    return moment.utc(date).format('MMM D');
  }
};

export const removeDuplicatesById = <T extends Record<string, any>>(
  array: T[]
): T[] => {
  const map = new Map();
  array.forEach((item) => map.set(item.id, item));
  return Array.from(map.values());
};

export const getCurrentLatLng: () => Promise<LatLng | undefined> = async () => {
  try {
    let location = await Location.getCurrentPositionAsync({});
    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    };
  } catch (error) {
    const { code, message }: any = error;
    console.warn(code, message);
  }
};

export const getLatLongFromCountry = async (countryName: string) => {
  try {
    const results = await Location.geocodeAsync(countryName);
    if (results.length > 0) {
      const { latitude, longitude } = results[0];
      return { latitude, longitude };
    }
    throw new Error('No results found');
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};

export const openInMaps = (address: string) => {
  const formattedAddress = encodeURIComponent(address);
  const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${formattedAddress}`;

  Linking.openURL(mapsUrl).catch((err) =>
    console.error('Error opening maps:', err)
  );
};

// Utility function to clean filename
export const removeSpacesAndSymbols = (text: string): string =>
  text.replace(/[^\w\s]/gi, '').replace(/\s+/g, '');

export const formatEventDateTime = (
  startTime: string = '',
  endTime: string = ''
) => {
  const startMoment = moment.utc(startTime);
  const endMoment = moment.utc(endTime);
  const isSameDay = startMoment.isSame(endMoment, 'day');

  if (isSameDay) {
    return `${startMoment.format('dddd, DD MMM YYYY')}\n${startMoment.format('LT')} - ${endMoment.format('LT')}`;
  } else {
    return `${startMoment.format('dddd, DD MMM YYYY LT')} - \n${endMoment.format('dddd, DD MMM YYYY LT')}`;
  }
};

export const isPresaleActive = (presaleTicket: PresaleConfig): boolean => {
  if (!presaleTicket.startDateTime || !presaleTicket.endDateTime) {
    return false;
  }

  const now = dayjs();
  const startDate = dayjs(presaleTicket.startDateTime);
  const endDate = dayjs(presaleTicket.endDateTime);

  return now.isAfter(startDate) && now.isBefore(endDate);
};

export const hasValidPresaleTicketCategories = (
  presaleConfig?: PresaleConfig[]
): boolean => {
  if (!presaleConfig || presaleConfig.length === 0) {
    return false;
  }

  return presaleConfig.some((presaleTicket) => isPresaleActive(presaleTicket));
};

export function maskLast(str: string, count = 4, maskChar = '*') {
  if (str.length <= count) {
    return maskChar.repeat(count);
  }
  return str.slice(0, -count) + maskChar.repeat(count);
}

export function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

export function getDistanceFromLatLonInKm(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
) {
  const R = 6371;
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function formatEnumLabel(value: string): string {
  return value
    .toLowerCase()
    .split('_')
    .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
    .join(' ');
}

// Type guard to ensure ticketCategories is of correct type
export const hasTicketCategories = (
  categories: TicketCategories | null | undefined
): categories is TicketCategories => {
  return categories !== null && categories !== undefined;
};

export const renderDateTitle = (
  date: string | number | Date | dayjs.Dayjs | null | undefined
): string => {
  const givenDate = dayjs(date);

  const today = dayjs().startOf('day');

  const yesterday = dayjs().subtract(1, 'day').startOf('day');

  if (givenDate.isSame(today, 'day')) {
    return 'Today';
  }

  if (givenDate.isSame(yesterday, 'day')) {
    return 'Yesterday';
  }

  return givenDate.format('MMM DD') + getOrdinalSuffix(givenDate.date());
};

const getOrdinalSuffix = (day: number) => {
  const suffixes = ['th', 'st', 'nd', 'rd'];
  const v = day % 100;
  return suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
};

export const isToday = (
  date: string | number | Date | dayjs.Dayjs | null | undefined
): boolean => {
  const givenDate = dayjs(date);

  const today = dayjs().startOf('day');

  return givenDate.isSame(today, 'day');
};

export const calculateBarChartSpacing = (
  barData: barDataItem[],
  barWidth: number = 32
): number => {
  const outerContainerPadding = 32;
  const cardPadding = 32;
  const chartContainerPadding = 32;
  const dataLength = barData.length;
  const initialSpacing = 0;

  const totalPadding =
    outerContainerPadding + cardPadding + chartContainerPadding;
  const availableWidth = WIDTH - totalPadding;
  const totalBarWidth = barWidth * dataLength;
  const remainingWidth = availableWidth - totalBarWidth - initialSpacing;
  const spacing = Math.max(0, remainingWidth / (dataLength - 1));

  return Math.floor(spacing);
};

interface TicketCategory {
  label: string;
  value: number;
}
interface PieChartData extends Pick<pieDataItem, 'value' | 'color' | 'text'> {}

export const generatePieChartData = (
  categories: TicketCategory[],
  colorKeys: string[]
): PieChartData[] => {
  return categories.map((category, index) => {
    const colorKey = colorKeys[index];
    return {
      value: category.value,
      color: colors[colorKey as keyof typeof colors][40],
      text: `${category.value}%`,
    };
  });
};

export const getShuffledColorKeys = (): string[] => {
  const excludedColorKeys = Object.keys(colors).slice(0, 4);
  const colorKeys = Object.keys(colors).filter(
    (key) => !excludedColorKeys.includes(key)
  );
  return [...colorKeys].sort(() => 0.5 - Math.random());
};

export interface AnalyticsMetrics {
  totalSold: number;
  totalTicketQuantity: number;
  conversionRate: number;
  salesGrowth: number;
  isSoldOut: boolean;
}

export const computeAnalyticsMetrics = (
  ticketCategories: TicketCategories | null | undefined,
  _presaleConfig?: PresaleConfig[]
): AnalyticsMetrics => {
  const isSoldOut = hasTicketCategories(ticketCategories)
    ? Object.values(ticketCategories).every(({ quantity }) => quantity === 0)
    : false;

  const totalSold = hasTicketCategories(ticketCategories)
    ? Object.values(ticketCategories).reduce((sum, category) => {
        const { sold = 0 } = category;
        return sum + sold;
      }, 0)
    : 0;

  const totalTicketQuantity = hasTicketCategories(ticketCategories)
    ? Object.values(ticketCategories).reduce((sum, category) => {
        const { quantity, sold = 0 } = category;
        return sum + quantity + sold; // Total available + sold
      }, 0)
    : 0;

  const conversionRate =
    totalTicketQuantity > 0 ? (totalSold / totalTicketQuantity) * 100 : 0;

  // Mock sales growth calculation - in real app, compare with previous period
  const salesGrowth = 100; // TODO: This should be calculated from historical data in the BE

  return {
    totalSold,
    totalTicketQuantity,
    conversionRate,
    salesGrowth,
    isSoldOut,
  };
};

// Generate pie chart data for conversion rates by ticket category
export const generateConversionRateData = (
  ticketCategories: TicketCategories | null | undefined
): PieChartData[] => {
  if (!hasTicketCategories(ticketCategories)) {
    return [];
  }

  const categories = Object.entries(ticketCategories);
  const totalSold = categories.reduce(
    (sum, [, category]) => sum + (category.sold || 0),
    0
  );

  const conversionData = categories.map(([key, category]) => {
    const sold = category.sold || 0;
    const percentage = totalSold > 0 ? (sold / totalSold) * 100 : 0;

    return {
      category: key,
      value: sold,
      percentage: Math.round(percentage),
    };
  });

  return conversionData.filter((item) => item.value > 0);
};

export const getDateRange = (startDate: Date, endDate: Date): string[] => {
  const dates: string[] = [];
  const current = new Date(startDate);

  while (current <= endDate) {
    dates.push(
      current.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
    );
    current.setDate(current.getDate() + 1);
  }

  return dates;
};

export const getRemainingTimerSeconds = ({
  expiresAt,
  defaultTimer,
}: {
  expiresAt?: string;
  defaultTimer: number;
}): number => {
  if (!expiresAt) return defaultTimer;
  const expiryDate = parseISO(expiresAt);
  const now = new Date();
  const secondsLeft = differenceInSeconds(expiryDate, now);
  return Math.max(secondsLeft, 0);
};

export function hasProperty<T extends object>(
  obj: T,
  key: PropertyKey
): key is keyof T {
  return key in obj;
}

export const removeUndefined = <T>(obj: T): T => {
  if (Array.isArray(obj)) {
    return obj.map((v) => removeUndefined(v)) as unknown as T;
  }
  if (obj && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj)
        .filter(([_, v]) => v !== undefined)
        .map(([k, v]) => [k, removeUndefined(v)])
    ) as T;
  }
  return obj;
};

export const getTimeValue = (time: any) => {
  if (!time) return new Date();
  if (typeof time.toDate === 'function') return time.toDate().getTime();
  if (time instanceof Date) return time.getTime();
  if (typeof time === 'number') return time;
  return new Date();
};
