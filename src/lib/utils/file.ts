import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Alert, Platform } from 'react-native';

interface DownloadParams {
  url: string;
  fileName: string;
  // TODO: Extend mimeType types
  mimeType:
    | 'application/pdf'
    | 'application/vnd.ms-excel'
    | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
}

export const downloadAndSaveFile = async ({
  url,
  fileName,
  mimeType,
}: DownloadParams): Promise<string | null> => {
  try {
    if (!url || !fileName || !mimeType) {
      throw new Error('URL, fileName, and mimeType are required');
    }

    const fileUri: string = `${FileSystem.documentDirectory}${fileName}`;
    let finalFileUri: string = fileUri;

    const callback = (downloadProgress: FileSystem.DownloadProgressData) => {
      const progress: number =
        downloadProgress.totalBytesWritten /
        downloadProgress.totalBytesExpectedToWrite;
      console.log(`Download progress: ${(progress * 100).toFixed(2)}%`);
      // Optionally update UI with progress (e.g., setState for a progress bar)
    };

    const downloadResumable = FileSystem.createDownloadResumable(
      url,
      fileUri,
      {},
      callback
    );

    const result = await downloadResumable.downloadAsync();
    if (!result) {
      throw new Error('Download failed');
    }
    console.log('File downloaded to:', result.uri);

    // Handle Android-specific saving using Storage Access Framework (SAF)
    if (Platform.OS === 'android') {
      const permissions =
        await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();
      if (!permissions.granted) {
        Alert.alert(
          'Permission Denied',
          'Storage access permission is required to save the file.'
        );
        return null;
      }

      finalFileUri = await FileSystem.StorageAccessFramework.createFileAsync(
        permissions.directoryUri,
        fileName,
        mimeType
      );

      const fileContent: string = await FileSystem.readAsStringAsync(
        result.uri,
        {
          encoding: FileSystem.EncodingType.Base64,
        }
      );
      await FileSystem.writeAsStringAsync(finalFileUri, fileContent, {
        encoding: FileSystem.EncodingType.Base64,
      });

      console.log('Success', `File saved to ${fileName}`);
    } else {
      // For iOS, the file is already in documentDirectory, which is accessible
      // Optionally share the file to allow saving to Files app
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(result.uri, {
          mimeType,
        });
      }
    }

    return finalFileUri;
  } catch (error: unknown) {
    console.error('Error downloading or saving file:', error);
    return null;
  }
};
