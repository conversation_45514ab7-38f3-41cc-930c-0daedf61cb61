/* eslint-disable unicorn/filename-case */

export function formatCurrency(
  value: number | string | undefined | null
): string {
  if (value === undefined || value === null || value === '') return '₦0';

  const amount = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(amount)) return '₦0';

  return `₦${amount.toLocaleString('en-NG', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
}
