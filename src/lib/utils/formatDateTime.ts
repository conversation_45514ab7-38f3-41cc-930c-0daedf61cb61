/* eslint-disable unicorn/filename-case */
import { format } from 'date-fns';

export function formatDateTime(date?: Date | null): string {
  if (!date) return 'Start date and time';
  return format(date, 'd MMM, yyyy - h:mm a');
}

const formatDate = (dt: Date | string) => {
  const date = new Date(dt);
  return isNaN(date.getTime()) ? 'Invalid date' : format(date, 'PPP');
};

const formatTime = (dt: Date | string) => {
  const date = new Date(dt);
  return isNaN(date.getTime()) ? 'Invalid time' : format(date, 'p');
};

export { formatDate, formatTime };
