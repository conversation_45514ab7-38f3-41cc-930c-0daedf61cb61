import type {
  FieldValue,
  FirebaseFirestoreTypes,
} from '@react-native-firebase/firestore';
import { type IMessage, type User } from 'react-native-gifted-chat';

export interface MessageType extends IMessage {
  user: User;
  read?: boolean;
  requestType?: 'new_request' | 'request_accepted' | 'request_declined';
  replyToMessage?: {
    _id: string;
    text: string;
    user: {
      _id: string | undefined;
      name: string | undefined;
      avatar: string | undefined;
    };
    image?: string;
  };
}

type RequestStatus = 'pending' | 'accepted' | 'declined';
export interface ChatRequestType {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  receiverId: string;
  message: string;
  status: RequestStatus;
  createdAt: FirebaseFirestoreTypes.Timestamp;
}

export interface UserType {
  id: string;
  displayName: string;
  email?: string;
  photoURL: string;
  blockedUsers?: string[];
  createdAt: FirebaseFirestoreTypes.Timestamp;
  lastSeen?: FirebaseFirestoreTypes.Timestamp;
  status?: 'online' | 'offline';
  hideLastSeen?: boolean;
  hideOnlineStatus?: boolean;
  isInFirestore?: boolean;
}

export interface ChatListType {
  id: string;
  lastMessage: string;
  lastMessageTime: FirebaseFirestoreTypes.Timestamp | FieldValue;
  unreadCount: number | FieldValue;
  userAvatar: string;
  userId: string;
  userName: string;
  isTyping: boolean;
  status?: 'online' | 'offline';
  hideOnlineStatus?: boolean;
  hideLastSeen?: boolean;
  isPendingRequest?: boolean;
  requestStatus?: RequestStatus;
}

export interface MessageRequest {
  id: string;
  receiverId: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  message: string;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  status: 'accepted' | 'declined' | 'pending';
  showRequestAlert: boolean;

  // system message props
  _id: string;
  text: string;
  system: boolean;
}

export interface Message {
  text: string;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  received: boolean;
  read: boolean;
  image?: string;

  // system message props
  _id: string;
  system?: boolean;
  requestType?: 'new_request' | 'request_accepted' | 'request_declined';
  replyToMessage?: {
    _id: string;
    text: string;
    user: {
      _id: string | undefined;
      name: string | undefined;
      avatar: string | undefined;
    };
  };
}
