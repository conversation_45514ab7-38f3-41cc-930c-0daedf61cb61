import { type FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { type Href } from 'expo-router';
import { type StyleProp, type ViewStyle } from 'react-native';
import { type TextStyle } from 'react-native';
import { z } from 'zod';

import { type LoginResponse } from '@/api/auth';
export * from './chat';

export type IconType =
  | 'ionicons'
  | 'material'
  | 'feather'
  | 'font-awesome'
  | 'font-awesome-6'
  | 'font-awesome-5'
  | 'material-community'
  | 'entypo'
  | 'custom-svg';
export interface ProfileItem {
  icon: string; // Ionicons name
  iconType: IconType; // Type of icon to use
  iconColor?: string; // Optional color for the icon (can be hex, tailwind class, or named color)

  label: string; // Display text
  navigateTo: Href; // Navigation target or null if not navigable
  isToggle?: boolean; // Whether this item has a toggle switch instead of chevron
  textColor?: string; // Optional Tailwind color class for text
  hideChevronForward?: boolean;
  handleExternalLink?: () => void;
  isStoreLink?: boolean;
  externalUrl?: string;
  hasWebView?: boolean;
}

export interface ProfileSection {
  title: string; // Section title
  items: ProfileItem[]; // Array of items in this section
}

// Type for the entire profile sections array
export type ProfileSections = ProfileSection[];

export type TransactionType = 'CREDIT' | 'DEBIT';
export type TransactionStatus = 'PENDING' | 'FAILED' | 'SUCCESS';
export type TransactionCategory =
  | 'FUND_WALLET'
  | 'WITHDRAW_WALLET'
  | 'SONG_REQUEST'
  | 'DJ_SESSION_EARNINGS'
  | 'EVENT_TICKET_PURCHASE'
  | 'EVENT_TICKET_EARNINGS';

export interface Transaction {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  currency?: string;
}

export type LiveChatComment = {
  id: string | number;
  fullName: string;
  userName: string;
  avatar: string;
  message: string;
};

// Define tabs
export type TabType = 'Request' | 'Queue' | 'History';

// Define mock data interface

export const LIVE_SESSION_TYPES = ['request', 'shoutout'] as const;

export type LIVE_TYPE = (typeof LIVE_SESSION_TYPES)[number];

export const Coordinates = z.object({
  lat: z.number(),
  lng: z.number(),
});

export type CoordinatesType = z.infer<typeof Coordinates>;

export const Location = z.object({
  city: z.string(),
  state: z.string(),
  street: z.string(),
  country: z.string().toUpperCase(),
  address: z.string(),
  landmark: z.string().optional(),
  coordinates: Coordinates,
});

export type LocationType = z.infer<typeof Location>;

export type ReportType =
  | 'spam'
  | 'inappropriate'
  | 'copyright'
  | 'underage'
  | 'none';

export interface DecodedTokenType extends Partial<LoginResponse> {
  userId: string;
  id: string;
  exp: number;
  iat: number;
}

export enum NotificationType {
  EVENT = 'EVENT',
  DJ_SESSION = 'DJ_SESSION',
  PRIVATE_CHAT = 'PRIVATE_CHAT',
}

export interface NotificationExtraData {
  type?: NotificationType;
  eventId?: string;
  eventSlug?: string;
  djSessionId?: string;
  songId?: string;
  userId?: string;
  username?: string;
  userAvatar?: string;
  chatRoomId?: string;
  excludeInSavedNotifications?: boolean;
  // [key: string]: string;
}

export interface TypedRemoteMessage<T extends Record<string, any> = {}>
  extends Omit<FirebaseMessagingTypes.RemoteMessage, 'data'> {
  data?: T;
}

export type TabScreenItem = {
  key: string;
  title: string;
  component: React.ComponentType;
  notificationCount?: number;
  data?: any;
};

export interface AppTabProps {
  items: TabScreenItem[];
  labalStyle?: StyleProp<TextStyle>;
  tabBarStyle?: StyleProp<ViewStyle>;
  indicatorStyle?: StyleProp<ViewStyle>;
  indicatorContainerStyle?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  style?: StyleProp<ViewStyle>;
  tabIndex?: number;
  tabSetIndex?: (i: number) => void;
  containerClassName?: string;
  contentContainerClassName?: string;
  tabBarClassName?: string;
  tabBarFocusedClassName?: string;
  tabBarLabelClassName?: string;
  tabBarLabelActiveClassName?: string;
  tabBarLabelInactiveClassName?: string;
  countContainerClassName?: string;
  countClassName?: string;
}
