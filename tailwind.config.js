const colors = require('./src/components/ui/colors');
const semanticColors = require('./src/components/ui/semantic-colors');

/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        'aeonik-air': ['Aeonik-Air'],
        'aeonik-air-italic': ['Aeonik-AirItalic'],
        'aeonik-thin': ['Aeonik-Thin'],
        'aeonik-thin-italic': ['Aeonik-ThinItalic'],
        'aeonik-light': ['Aeonik-Light'],
        'aeonik-light-italic': ['Aeonik-LightItalic'],
        'aeonik-regular': ['Aeonik-Regular'],
        'aeonik-regular-italic': ['Aeonik-RegularItalic'],
        'aeonik-medium': ['Aeonik-Medium'],
        'aeonik-medium-italic': ['Aeonik-MediumItalic'],
        'aeonik-black': ['Aeonik-Black'],
        'aeonik-black-italic': ['Aeonik-BlackItalic'],
        // Shorthand versions:
        aeonik: ['Aeonik-Regular'],
        'aeonik-bold': ['Aeonik-Black'],
      },
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '24px',
        '2xl': '32px',
        '3xl': '40px',
        '4xl': '64px',
      },
      fontWeight: {
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      lineHeight: {
        100: '100%',
        120: '120%',
        150: '150%',
      },
      backgroundImage: {
        'profile-ring': 'linear-gradient(to bottom, #664FB0 0%, #A992F5 100%)',
      },
      borderRadius: {
        none: '0px',
        sm: '4px',
        md: '8px',
        lg: '16px',
        xl: '32px',
        full: '9999px',
      },
      spacing: {
        0: '0px',
        0.5: '2px',
        1: '4px',
        1.5: '6px',
        2: '8px',
        2.5: '10px',
        3: '12px',
        3.5: '14px',
        4: '16px',
        5: '20px',
        6: '24px', // Was giving 21px, now will be 24px
        7: '28px',
        8: '32px', // Was giving 28px, now will be 32px
        9: '36px',
        10: '40px',
        11: '44px',
        12: '48px',
        14: '56px',
        16: '64px',
        20: '80px',
        24: '96px',
        28: '112px',
        32: '128px',
        36: '144px',
        40: '160px',
        44: '176px',
        48: '192px',
        52: '208px',
        56: '224px',
        60: '240px',
        64: '256px',
        72: '288px',
        80: '320px',
        96: '384px',
      },
      colors: {
        ...colors,
        ...semanticColors,
      },
      textColor: semanticColors,
    },
  },
  plugins: [],
};
